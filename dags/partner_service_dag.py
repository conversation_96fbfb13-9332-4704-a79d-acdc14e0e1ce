from datetime import datetime

from common_helpers.database_services import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Postgres<PERSON>andler
from common_helpers.logging import get_logger
from common_helpers.utils import create_migration_result_table
from partner_service.refund_sales_transaction import RefundSalesTransaction
from partner_service.sales_transaction import SalesTransaction
from partner_service.sales_transaction_item import SalesTransactionItem
from partner_service.sales_transaction_burn_payment import (
    SalesTransactionBurnPayment,
)
from partner_service.sales_transaction_payment import SalesTransactionPayment
from partner_service.product_category import ProductCategory
from partner_service.product_brand import ProductBrand

from airflow import DAG
from airflow.models import Variable
from airflow.operators.python_operator import PythonOperator

logger = get_logger()


class PartnerService:
    def __init__(self):
        self.service_name = "partner_service"
        self.passphrase = Variable.get("SECRET_PII_ENCRYPTION")
        self.batch_size = int(
            Variable.get(f"{self.service_name}.batch_size", default_var=10000),
        )
        self.executor_max_workers = int(
            Variable.get(f"{self.service_name}.executor_max_workers", default_var=5),
        )
        self.incremental_query_date = Variable.get(
            f"{self.service_name}.incremental_query_date", default_var=None
        )
        self.loyalty_value_handler = MSSQLHandler(
            conn_id="loyalty_value_smc_db_connection_id"
        )
        self.newmember_handler = MSSQLHandler(conn_id="newmember_smc_db_connection_id")
        self.postgresql_handler = PostgresHandler(conn_id="temp_db_connection_id")

    def prepare_brand_table_for_migration(
        self,
    ) -> None:
        """
        Insert a row into Brand, with id of PARTNER_SERVICE_MIGRATION,
        which is needed for ProductBrand and ProductCategory migrations.

        Args:
            None

        Returns:
            None
        """
        insert_query = """
            INSERT INTO "partner_service"."Brand" (
                "id",
                "code",
                "name",
                "status",
                "createdAt",
                "createdBy",
                "updatedAt",
                "updatedBy"
            )
            VALUES (
                'PARTNER_SERVICE_MIGRATION',
                'PARTNER_SERVICE_MIGRATION',
                '{}',
                'PARTNER_SERVICE_MIGRATION',
                NOW () AT TIME ZONE 'UTC',
                '{}',
                NOW () AT TIME ZONE 'UTC',
                '{}'
            )
            ON CONFLICT ("id") DO NOTHING;
        """

        try:
            postgresql_connection = self.postgresql_handler.hook.get_conn()

            logger.info(f"started preparing Brand table for migration...")
            self.postgresql_handler.execute_with_rollback(
                connection=postgresql_connection, query_string=insert_query
            )
            logger.info(f"finished preparing Brand table for migration.")
        except Exception as error:
            logger.error(f"an error has occured: {error}")

            if postgresql_connection:
                logger.info("a postgresql connection is found, rolling back...")
                postgresql_connection.rollback()
                logger.info("successfully rolled back.")

            raise error
        finally:
            if postgresql_connection:
                postgresql_connection.close()

    def prepare_batch_tracker_table(self):
        """
        Prepare a table to track the migration process.

        Args:
            None

        Returns:
            None
        """
        create_table_query_string = """
            CREATE TABLE IF NOT EXISTS partner_service.batch_tracker (
                table_name VARCHAR(100) PRIMARY KEY,
                total_records INT NOT NULL,
                completed_batches JSONB NOT NULL DEFAULT '[]'::jsonb,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """
        postgresql_connection = self.postgresql_handler.hook.get_conn()

        try:
            logger.info(f"started preparing batch tracker table for migration...")
            self.postgresql_handler.execute_with_rollback(
                connection=postgresql_connection,
                query_string=create_table_query_string,
            )
            logger.info(f"finished preparing batch tracker table for migration.")
        finally:
            postgresql_connection.close()

    def get_temp_tables_incremental_query_condition(self, date_field: str) -> str:
        if self.incremental_query_date is None:
            return f"{date_field} >= DATEADD(DAY, -1, CAST(CAST(GETDATE() AS DATE) AS DATETIME)) AND {date_field} < CAST(CAST(GETDATE() AS DATE) AS DATETIME)"

        return f"{date_field} >= CAST('{self.incremental_query_date}' AS DATETIME) AND {date_field} < DATEADD(DAY, 1, CAST('{self.incremental_query_date}' AS DATETIME))"

    def prepare_generate_sales_transaction_id_function(self) -> None:
        generate_sales_transaction_id_function_queyr = """
            IF NOT EXISTS (
                SELECT
                    1
                FROM
                    sys.objects
                WHERE
                    object_id = OBJECT_ID (N'dbo.fn_generate_sales_transaction_id')
                    AND type IN (N'FN', N'IF', N'TF', N'FS', N'FT')
            ) BEGIN EXEC (
                '
                CREATE FUNCTION dbo.fn_generate_sales_transaction_id (@key NVARCHAR(255))
                RETURNS BIGINT
                AS
                BEGIN
                    DECLARE @result BIGINT
                    DECLARE @temp NVARCHAR(255)
                    DECLARE @pipe_count INT = LEN(@key) - LEN(REPLACE(@key, ''|'', ''''))

                    IF @pipe_count = 4
                    BEGIN
                        SET @temp = 
                            LEFT(
                                SUBSTRING(@key, 1, 3) + SUBSTRING(@key, 6, LEN(@key)),
                                CHARINDEX(''|'', @key, 
                                    CHARINDEX(''|'', @key, 
                                        CHARINDEX(''|'', @key, -2) + 1
                                    ) + 1
                                ) - 2
                            ) +
                            RIGHT(
                                @key,
                                LEN(@key) + 4 - CHARINDEX(''|'', @key, 
                                    CHARINDEX(''|'', @key, 
                                        CHARINDEX(''|'', @key, 
                                            CHARINDEX(''|'', @key, 0) + 1
                                        ) + 1
                                    ) + 1
                                )
                            )
                    END
                    ELSE IF @pipe_count = 3
                    BEGIN
                        SET @temp = REPLACE(@key, ''P'', ''1'')
                    END
                    ELSE IF @key LIKE ''50|3600001191416|%'' 
                    BEGIN
                        SET @temp = REPLACE(@key, ''360000'', '''')
                    END
                    ELSE
                    BEGIN
                        SET @temp = @key
                    END

                    SET @temp = REPLACE(@temp, ''|'', '''')
                    SET @temp = REPLACE(@temp, ''/'', '''')
                    SET @temp = REPLACE(@temp, ''-R'', ''1'')
                    SET @temp = REPLACE(@temp, ''A'', '''')
                    SET @temp = REPLACE(@temp, ''C'', '''')
                    SET @temp = REPLACE(@temp, ''D'', '''')
                    SET @temp = REPLACE(@temp, ''E'', '''')
                    SET @temp = REPLACE(@temp, ''M'', '''')
                    SET @temp = REPLACE(@temp, ''B'', '''')
                    SET @temp = REPLACE(@temp, ''I'', '''')
                    SET @temp = REPLACE(@temp, ''K'', '''')
                    SET @temp = REPLACE(@temp, ''S'', '''')
                    SET @temp = REPLACE(@temp, ''P'', '''')
                    SET @temp = REPLACE(@temp, ''R'', '''')
                    SET @temp = REPLACE(@temp, ''U'', '''')
                    SET @temp = REPLACE(@temp, ''H'', '''')
                    SET @temp = REPLACE(@temp, ''G'', '''')
                    SET @temp = REPLACE(@temp, ''Z'', '''')
                    SET @temp = REPLACE(@temp, ''N'', '''')
                    SET @temp = REPLACE(@temp, ''T'', '''')
                    SET @temp = REPLACE(@temp, ''X'', '''')
                    SET @temp = REPLACE(@temp, ''L'', '''')
                    SET @temp = REPLACE(@temp, ''J'', '''')
                    SET @temp = REPLACE(@temp, ''F'', '''')
                    SET @temp = REPLACE(@temp, ''V'', '''')
                    SET @temp = REPLACE(@temp, ''Q'', '''')
                    SET @temp = REPLACE(@temp, ''W'', '''')
                    SET @temp = REPLACE(@temp, ''O'', '''')
                    SET @temp = REPLACE(@temp, ''_'', '''')

                    SET @result = TRY_CAST(@temp AS BIGINT)

                    RETURN ISNULL(@result, -1) * -1
                END
                '
            ) END
        """

        loyalty_value_connection = self.loyalty_value_handler.hook.get_conn()

        try:
            logger.info(
                f"started preparing a function for salesTransactionId generation..."
            )
            self.loyalty_value_handler.execute_query_string(
                connection=loyalty_value_connection,
                query_string=generate_sales_transaction_id_function_queyr,
            )
            logger.info(
                f"finished preparing a function for salesTransactionId generation."
            )
        finally:
            loyalty_value_connection.close()

    def prepare_temp_tables_for_sales_transaction(self, is_full_dump: bool = True):
        """
        Prepare indexed temporary tables for SalesTransaction migration.

        Args:
            is_full_dump (bool): The migration type.

        Returns:
            None
        """
        create_smc_sales_header_temp_table = (
            """
                IF OBJECT_ID('Newmember.dbo.temp_smc_sales_header_for_sales_transaction_full_dump', 'U') IS NULL
                BEGIN
                    SELECT
                        TRIM(ssh.member_id) AS memberId,
                        TRIM(ssh.member_id) AS gwlNo,
                        ssh.key_search COLLATE SQL_Latin1_General_CP1_CI_AS AS externalId,
                        ssh.BranchNo AS partnerId,
                        ssh.BranchNo AS brandId,
                        ssh.Site AS branchId,
                        'EARNED' AS status,
                        ssh.DataDate,
                        ssh.ShoppingCard
                    INTO Newmember.dbo.temp_smc_sales_header_for_sales_transaction_full_dump
                    FROM Newmember.dbo.SMCSalesHeader ssh
                    JOIN Newmember.dbo.df_member dm
                    ON dm.member_id = ssh.member_id
                    WHERE ssh.SaleStatus != 'R' AND dm.del_flag = '' AND ssh.DataDate < CAST(CAST(GETDATE() AS DATE) AS DATETIME);

                    CREATE INDEX temp_smc_sales_header_for_sales_transaction_full_dump_externalId ON Newmember.dbo.temp_smc_sales_header_for_sales_transaction_full_dump (externalId);
                END;
            """
            if is_full_dump
            else f"""
                IF OBJECT_ID('Newmember.dbo.temp_smc_sales_header_for_sales_transaction_incremental', 'U') IS NOT NULL
                BEGIN
                    DROP TABLE Newmember.dbo.temp_smc_sales_header_for_sales_transaction_incremental;
                END;

                SELECT
                    TRIM(ssh.member_id) AS memberId,
                    TRIM(ssh.member_id) AS gwlNo,
                    ssh.key_search COLLATE SQL_Latin1_General_CP1_CI_AS AS externalId,
                    ssh.BranchNo AS partnerId,
                    ssh.BranchNo AS brandId,
                    ssh.Site AS branchId,
                    'EARNED' AS status,
                    ssh.DataDate,
                    ssh.ShoppingCard
                INTO Newmember.dbo.temp_smc_sales_header_for_sales_transaction_incremental
                FROM Newmember.dbo.SMCSalesHeader ssh
                JOIN Newmember.dbo.df_member dm
                ON dm.member_id = ssh.member_id
                WHERE ssh.SaleStatus != 'R' AND dm.del_flag = '' AND {self.get_temp_tables_incremental_query_condition('ssh.DataDate')};
                CREATE INDEX temp_smc_sales_header_for_sales_transaction_incremental_externalId ON Newmember.dbo.temp_smc_sales_header_for_sales_transaction_incremental (externalId);
            """
        )
        create_smc_sales_trans_temp_table = (
            """
                IF OBJECT_ID('Newmember.dbo.temp_smc_sales_trans_for_sales_transaction_full_dump', 'U') IS NULL
                BEGIN
                    WITH smc_base AS (
                        SELECT 
                            key_search,
                            Net,
                            Amount,
                            Discount,
                            lineCancel,
                            UpdateCoupon,
                            MAX(CAST(UpdateCoupon AS INT)) OVER (PARTITION BY key_search, MatCode) AS max_coupon
                        FROM Newmember.dbo.SMCSalesTrans
                    ),
                    filtered_smc_sales_trans AS (
                        SELECT 
                            key_search,
                            Net,
                            Amount,
                            Discount,
                            lineCancel,
                            CASE 
                                WHEN UpdateCoupon = 1 AND max_coupon = 1 THEN 1
                                WHEN max_coupon = 0 THEN 1
                                ELSE 0
                            END AS row_num
                        FROM smc_base
                    )
                    SELECT
                        key_search COLLATE SQL_Latin1_General_CP1_CI_AS AS key_search,
                        SUM(Net) AS netTotalAmount,
                        SUM(Amount) AS totalOriginalPrice,
                        SUM(Discount) AS totalDiscount
                    INTO Newmember.dbo.temp_smc_sales_trans_for_sales_transaction_full_dump
                    FROM filtered_smc_sales_trans
                    WHERE row_num = 1 AND lineCancel != 1
                    GROUP BY key_search;
                    
                    CREATE INDEX temp_smc_sales_trans_for_sales_transaction_full_dump_key_search ON Newmember.dbo.temp_smc_sales_trans_for_sales_transaction_full_dump (key_search);
                END;
            """
            if is_full_dump
            else """
                IF OBJECT_ID('Newmember.dbo.temp_smc_sales_trans_for_sales_transaction_incremental', 'U') IS NOT NULL
                BEGIN
                    DROP TABLE Newmember.dbo.temp_smc_sales_trans_for_sales_transaction_incremental;
                END;

                WITH smc_base AS (
                    SELECT 
                        key_search,
                        Net,
                        Amount,
                        Discount,
                        lineCancel,
                        UpdateCoupon,
                        MAX(CAST(UpdateCoupon AS INT)) OVER (PARTITION BY key_search, MatCode) AS max_coupon
                    FROM Newmember.dbo.SMCSalesTrans
                ),
                filtered_smc_sales_trans AS (
                    SELECT 
                        key_search,
                        Net,
                        Amount,
                        Discount,
                        lineCancel,
                        CASE 
                            WHEN UpdateCoupon = 1 AND max_coupon = 1 THEN 1
                            WHEN max_coupon = 0 THEN 1
                            ELSE 0
                        END AS row_num
                    FROM smc_base
                )
                SELECT
                    key_search COLLATE SQL_Latin1_General_CP1_CI_AS AS key_search,
                    SUM(Net) AS netTotalAmount,
                    SUM(Amount) AS totalOriginalPrice,
                    SUM(Discount) AS totalDiscount
                INTO Newmember.dbo.temp_smc_sales_trans_for_sales_transaction_incremental
                FROM filtered_smc_sales_trans
                WHERE row_num = 1 AND lineCancel != 1
                GROUP BY key_search;
                CREATE INDEX temp_smc_sales_trans_for_sales_transaction_incremental_key_search ON Newmember.dbo.temp_smc_sales_trans_for_sales_transaction_incremental (key_search);
            """
        )
        create_smc_sales_payment_temp_table = (
            """
                IF OBJECT_ID('Newmember.dbo.temp_smc_sales_payment_for_sales_transaction_full_dump', 'U') IS NULL
                BEGIN
                    SELECT
                        key_search COLLATE SQL_Latin1_General_CP1_CI_AS AS key_search,
                        SUM(Net) AS totalEarnableAmount,
                        SUM(Net) AS totalAccumSpendableAmount
                    INTO Newmember.dbo.temp_smc_sales_payment_for_sales_transaction_full_dump
                    FROM Newmember.dbo.SMCSalesPayment
                    WHERE Amount >= 0 AND MethodCode NOT IN (SELECT DISTINCT Code from MAST_NonAccCarat)
                    GROUP BY key_search;

                    CREATE INDEX temp_smc_sales_payment_for_sales_transaction_full_dump_key_search ON Newmember.dbo.temp_smc_sales_payment_for_sales_transaction_full_dump (key_search);
                END;
            """
            if is_full_dump
            else """
                IF OBJECT_ID('Newmember.dbo.temp_smc_sales_payment_for_sales_transaction_incremental', 'U') IS NOT NULL
                BEGIN
                    DROP TABLE Newmember.dbo.temp_smc_sales_payment_for_sales_transaction_incremental;
                END;

                SELECT
                    key_search COLLATE SQL_Latin1_General_CP1_CI_AS AS key_search,
                    SUM(Net) AS totalEarnableAmount,
                    SUM(Net) AS totalAccumSpendableAmount
                INTO Newmember.dbo.temp_smc_sales_payment_for_sales_transaction_incremental
                FROM Newmember.dbo.SMCSalesPayment
                WHERE Amount >= 0 AND MethodCode NOT IN (SELECT DISTINCT Code from MAST_NonAccCarat) 
                GROUP BY key_search;
                CREATE INDEX temp_smc_sales_payment_for_sales_transaction_incremental_key_search ON Newmember.dbo.temp_smc_sales_payment_for_sales_transaction_incremental (key_search);
            """
        )
        create_lv_header_temp_table = (
            f"""
                IF OBJECT_ID('temp_lv_header_for_sales_transaction_full_dump', 'U') IS NULL
                BEGIN
                    WITH key_search_grouped_lv_header AS (
                        SELECT 
                            KeySearch,
                            LVHeaderKey,
                            AddDT,
                            FinishDT,
                            CancelHeaderKey,
                            DocDate,
                            LVCardNo,
                            ROW_NUMBER() OVER (PARTITION BY KeySearch ORDER BY LVHeaderKey DESC) AS row_num
                        FROM LVHeader lvh
                    ), filtered_df_cardhist AS (
                        SELECT 
                            emboss_id,
                            card_type_id,
                            ROW_NUMBER() OVER (
                                PARTITION BY emboss_id
                                ORDER BY update_datetime DESC
                            ) AS row_num
                        FROM Newmember.dbo.df_cardhist
                    )
                    SELECT
                        CancelHeaderKey,
                        lvh.KeySearch COLLATE SQL_Latin1_General_CP1_CI_AS AS KeySearch,
                        lvh.LVHeaderKey,
                        lvh.AddDT AS createdAt,
                        lvh.DocDate AS DocDate,
                        CASE 
                            WHEN lvh.FinishDT IS NOT NULL THEN lvh.FinishDT
                            ELSE lvh.AddDT
                        END AS updatedAt,
                        (SELECT mct.card_type_id AS 'memberLegacyTier.id', mct.card_type_code AS 'memberLegacyTier.code' FOR JSON PATH, WITHOUT_ARRAY_WRAPPER) AS settings
                    INTO temp_lv_header_for_sales_transaction_full_dump
                    FROM key_search_grouped_lv_header lvh
                    JOIN filtered_df_cardhist dch ON dch.emboss_id = lvh.LVCardNo COLLATE SQL_Latin1_General_CP1_CI_AS
                    JOIN Newmember.dbo.mst_card_type mct ON mct.card_type_code = dch.card_type_id
                    WHERE lvh.row_num = 1 AND dch.row_num = 1 AND lvh.DocDate < CAST(CAST(GETDATE() AS DATE) AS DATETIME);

                    CREATE INDEX temp_lv_header_for_sales_transaction_full_dump_key_search ON temp_lv_header_for_sales_transaction_full_dump (KeySearch);
                    CREATE INDEX temp_lv_header_for_sales_transaction_full_dump_lv_header_key ON temp_lv_header_for_sales_transaction_full_dump (LVHeaderKey);
                END;
            """
            if is_full_dump
            else f"""
                IF OBJECT_ID('temp_lv_header_for_sales_transaction_incremental', 'U') IS NOT NULL
                BEGIN
                    DROP TABLE temp_lv_header_for_sales_transaction_incremental;
                END;

                WITH key_search_grouped_lv_header AS (
                    SELECT 
                        KeySearch,
                        LVHeaderKey,
                        AddDT,
                        FinishDT,
                        CancelHeaderKey,
                        DocDate,
                        LVCardNo,
                        ROW_NUMBER() OVER (PARTITION BY KeySearch ORDER BY LVHeaderKey DESC) AS row_num
                    FROM LVHeader lvh
                ), filtered_df_cardhist AS (
                    SELECT 
                        emboss_id,
                        card_type_id,
                        ROW_NUMBER() OVER (
                            PARTITION BY emboss_id
                            ORDER BY update_datetime DESC
                        ) AS row_num
                    FROM Newmember.dbo.df_cardhist
                )
                SELECT
                    CancelHeaderKey,
                    lvh.KeySearch COLLATE SQL_Latin1_General_CP1_CI_AS AS KeySearch,
                    lvh.LVHeaderKey,
                    lvh.AddDT AS createdAt,
                    CASE 
                        WHEN lvh.FinishDT IS NOT NULL THEN lvh.FinishDT
                        ELSE lvh.AddDT
                    END AS updatedAt,
                    (SELECT mct.card_type_id AS 'memberLegacyTier.id', mct.card_type_code AS 'memberLegacyTier.code' FOR JSON PATH, WITHOUT_ARRAY_WRAPPER) AS settings
                INTO temp_lv_header_for_sales_transaction_incremental
                FROM key_search_grouped_lv_header lvh
                JOIN filtered_df_cardhist dch ON dch.emboss_id = lvh.LVCardNo COLLATE SQL_Latin1_General_CP1_CI_AS
                JOIN Newmember.dbo.mst_card_type mct ON mct.card_type_code = dch.card_type_id
                WHERE lvh.row_num = 1 AND dch.row_num = 1 AND {self.get_temp_tables_incremental_query_condition('lvh.DocDate')};
                CREATE INDEX temp_lv_header_for_sales_transaction_incremental_key_search ON temp_lv_header_for_sales_transaction_incremental (KeySearch);
                CREATE INDEX temp_lv_header_for_sales_transaction_incremental_lv_header_key ON temp_lv_header_for_sales_transaction_incremental (LVHeaderKey);
            """
        )
        create_lv_trans_temp_table = (
            """
                IF OBJECT_ID('temp_lv_trans_for_sales_transaction_full_dump', 'U') IS NULL
                BEGIN
                    SELECT
                        lvh.KeySearch COLLATE SQL_Latin1_General_CP1_CI_AS AS KeySearch,
                        SUM(lvt.totalPointEarned) AS totalPointEarned
                    INTO temp_lv_trans_for_sales_transaction_full_dump
                    FROM (
                        SELECT
                            lvt.LVHeaderKey AS LVHeaderKey,
                            SUM(lvt.Amount) AS totalPointEarned
                        FROM
                            LVTrans lvt
                            JOIN LVData lvd ON lvd.LVMainKey = lvt.LVMainKey
                        WHERE
                            lvt.MovementCode = 'PTPOS'
                            AND lvd.ValueCode IN (
                                'AP001',
                                'EP001',
                                'EP002',
                                'EP003',
                                'EP004',
                                'EP005',
                                'EP006',
                                'EP007',
                                'EP008',
                                'EP009',
                                'EP010',
                                'KPC01',
                                'KPO02',
                                'CR001',
                                'PT001'
                            )
                        GROUP BY
                            lvt.LVHeaderKey
                    ) AS lvt JOIN LVHeader lvh ON lvh.LVHeaderKey = lvt.LVHeaderKey
                    GROUP BY lvh.KeySearch;
                    CREATE INDEX temp_lv_trans_for_sales_transaction_full_dump_key_search ON temp_lv_trans_for_sales_transaction_full_dump (KeySearch);
                END;
            """
            if is_full_dump
            else f"""
                IF OBJECT_ID('temp_lv_trans_for_sales_transaction_incremental', 'U') IS NOT NULL
                BEGIN
                    DROP TABLE temp_lv_trans_for_sales_transaction_incremental;
                END;

                SELECT
                    lvh.KeySearch COLLATE SQL_Latin1_General_CP1_CI_AS AS KeySearch,
                    SUM(lvt.totalPointEarned) AS totalPointEarned
                INTO temp_lv_trans_for_sales_transaction_incremental
                FROM (
                    SELECT
                        lvt.LVHeaderKey AS LVHeaderKey,
                        SUM(lvt.Amount) AS totalPointEarned
                    FROM
                        LVTrans lvt
                        JOIN LVData lvd ON lvd.LVMainKey = lvt.LVMainKey
                    WHERE
                        lvd.ValueCode IN (
                            'AP001',
                            'EP001',
                            'EP002',
                            'EP003',
                            'EP004',
                            'EP005',
                            'EP006',
                            'EP007',
                            'EP008',
                            'EP009',
                            'EP010',
                            'KPC01',
                            'KPO02',
                            'CR001',
                            'PT001'
                        )
                    GROUP BY
                        lvt.LVHeaderKey
                ) AS lvt JOIN LVHeader lvh ON lvh.LVHeaderKey = lvt.LVHeaderKey
                GROUP BY lvh.KeySearch;
                CREATE INDEX temp_lv_trans_for_sales_transaction_incremental_key_search ON temp_lv_trans_for_sales_transaction_incremental (KeySearch);
            """
        )

        create_with_lv_header_result_table = """
            IF OBJECT_ID('temp_sales_transaction_with_lv_header_for_full_dump_migration', 'U') IS NULL
            BEGIN
                SELECT
                    lvh.LVHeaderKey AS id,
                    ssh.memberId,
                    ssh.gwlNo,
                    ssh.externalId,
                    ssh.partnerId,
                    ssh.brandId,
                    CASE
                        WHEN ssh.branchId IS NULL THEN ''
                        ELSE ssh.branchId
                    END AS branchId,
                    sst.netTotalAmount,
                    sst.totalOriginalPrice,
                    sst.totalDiscount,
                    CASE
                        WHEN ssp.totalEarnableAmount IS NOT NULL THEN ssp.totalEarnableAmount
                        ELSE 0
                    END AS totalEarnableAmount,
                    CASE
                        WHEN ssp.totalAccumSpendableAmount IS NOT NULL THEN ssp.totalAccumSpendableAmount
                        ELSE 0
                    END AS totalAccumSpendableAmount,
                    lvt.totalPointEarned,
                    ssh.status,
                    lvh.settings,
                    DATEADD (HOUR, -7, lvh.createdAt) AS createdAt,
                    DATEADD (HOUR, -7, lvh.updatedAt) AS updatedAt,
                    DATEADD (HOUR, -7, lvh.updatedAt) AS completedAt,
                    ssh.DataDate,
                    ssh.ShoppingCard
                INTO temp_sales_transaction_with_lv_header_for_full_dump_migration
                FROM Newmember.dbo.temp_smc_sales_header_for_sales_transaction_full_dump ssh
                JOIN Newmember.dbo.temp_smc_sales_trans_for_sales_transaction_full_dump sst ON sst.key_search = ssh.externalId
                LEFT JOIN Newmember.dbo.temp_smc_sales_payment_for_sales_transaction_full_dump ssp ON ssp.key_search = sst.key_search
                JOIN temp_lv_header_for_sales_transaction_full_dump lvh ON lvh.KeySearch = sst.key_search
                JOIN temp_lv_trans_for_sales_transaction_full_dump lvt ON lvt.KeySearch = lvh.KeySearch AND lvh.CancelHeaderKey IS NULL;

                CREATE INDEX ix_temp_sales_transaction_with_lv_header_for_full_dump_migration_id
                ON temp_sales_transaction_with_lv_header_for_full_dump_migration (id);
            END;
        """

        create_without_lv_header_result_table = """
            IF OBJECT_ID('temp_sales_transaction_without_lv_header_for_full_dump_migration', 'U') IS NULL
            BEGIN
                SELECT
                    dbo.fn_generate_sales_transaction_id(ssh.externalId) AS id,
                    ssh.memberId,
                    ssh.gwlNo,
                    ssh.externalId,
                    ssh.partnerId,
                    ssh.brandId,
                    CASE
                        WHEN ssh.branchId IS NULL THEN ''
                        ELSE ssh.branchId
                    END AS branchId,
                    sst.netTotalAmount,
                    sst.totalOriginalPrice,
                    sst.totalDiscount,
                    CASE
                        WHEN ssp.totalEarnableAmount IS NOT NULL THEN ssp.totalEarnableAmount
                        ELSE 0
                    END AS totalEarnableAmount,
                    CASE
                        WHEN ssp.totalAccumSpendableAmount IS NOT NULL THEN ssp.totalAccumSpendableAmount
                        ELSE 0
                    END AS totalAccumSpendableAmount,
                    0 AS totalPointEarned,
                    ssh.status,
                    '{}' AS settings,
                    ssh.DataDate AS createdAt,
                    ssh.DataDate AS updatedAt,
                    ssh.DataDate AS completedAt,
                    ssh.DataDate,
                    ssh.ShoppingCard
                INTO temp_sales_transaction_without_lv_header_for_full_dump_migration
                FROM Newmember.dbo.temp_smc_sales_header_for_sales_transaction_full_dump ssh
                JOIN Newmember.dbo.temp_smc_sales_trans_for_sales_transaction_full_dump sst ON sst.key_search = ssh.externalId
                LEFT JOIN Newmember.dbo.temp_smc_sales_payment_for_sales_transaction_full_dump ssp ON ssp.key_search = sst.key_search
                LEFT JOIN temp_lv_header_for_sales_transaction_full_dump lvh ON lvh.KeySearch = sst.key_search
                WHERE lvh.LVHeaderKey IS NULL;

                CREATE INDEX ix_temp_sales_transaction_without_lv_header_for_full_dump_migration_id
                ON temp_sales_transaction_without_lv_header_for_full_dump_migration (id);
            END;
        """

        loyalty_value_connection = self.loyalty_value_handler.hook.get_conn()

        try:
            logger.info(f"started preparing SMCSalesHeader temp table for migration...")
            self.loyalty_value_handler.execute_query_string(
                connection=loyalty_value_connection,
                query_string=create_smc_sales_header_temp_table,
            )
            logger.info(f"finished preparing SMCSalesHeader temp table for migration.")

            logger.info(f"started preparing SMCSalesTrans temp table for migration...")
            self.loyalty_value_handler.execute_query_string(
                connection=loyalty_value_connection,
                query_string=create_smc_sales_trans_temp_table,
            )
            logger.info(f"finished preparing SMCSalesTrans temp table for migration.")

            logger.info(
                f"started preparing SMCSalesPayment temp table for migration..."
            )
            self.loyalty_value_handler.execute_query_string(
                connection=loyalty_value_connection,
                query_string=create_smc_sales_payment_temp_table,
            )
            logger.info(f"finished preparing SMCSalesPayment temp table for migration.")

            logger.info(f"started preparing LVHeader temp table for migration...")
            self.loyalty_value_handler.execute_query_string(
                connection=loyalty_value_connection,
                query_string=create_lv_header_temp_table,
            )
            logger.info(f"finished preparing LVHeader temp table for migration.")

            logger.info(f"started preparing LVTrans temp table for migration...")
            self.loyalty_value_handler.execute_query_string(
                connection=loyalty_value_connection,
                query_string=create_lv_trans_temp_table,
            )
            logger.info(f"finished preparing LVTrans temp table for migration.")

            if is_full_dump:
                logger.info(
                    f"started preparing with LVHeader result temp table for migration..."
                )
                self.loyalty_value_handler.execute_query_string(
                    connection=loyalty_value_connection,
                    query_string=create_with_lv_header_result_table,
                )
                logger.info(
                    f"finished preparing with LVHeader result temp table for migration."
                )

                logger.info(
                    f"started preparing without LVHeader result temp table for migration..."
                )
                self.loyalty_value_handler.execute_query_string(
                    connection=loyalty_value_connection,
                    query_string=create_without_lv_header_result_table,
                )
                logger.info(
                    f"finished preparing without LVHeader result temp table for migration."
                )
        finally:
            loyalty_value_connection.close()

    def prepare_temp_tables_for_sales_transaction_item(self, is_full_dump: bool = True):
        """
        Prepare indexed temporary tables for SalesTransactionItem migration.

        Args:
            is_full_dump (bool): The migration type.

        Returns:
            None
        """
        create_lv_header_temp_table = (
            """
                IF OBJECT_ID('temp_lv_header_for_sales_transaction_item_full_dump', 'U') IS NULL
                BEGIN
                    SELECT
                        KeySearch COLLATE Thai_CI_AS AS KeySearch,
                        LVHeaderKey
                    INTO temp_lv_header_for_sales_transaction_item_full_dump
                    FROM LVHeader
                    WHERE DocDate < CAST(CAST(GETDATE() AS DATE) AS DATETIME);

                    CREATE NONCLUSTERED INDEX idx_temp_lv_header_for_sales_transaction_item_full_dump_key_search_lv_header
                    ON temp_lv_header_for_sales_transaction_item_full_dump (KeySearch, LVHeaderKey);
                END;
            """
            if is_full_dump
            else f"""
                IF OBJECT_ID('temp_lv_header_for_sales_transaction_item_incremental', 'U') IS NOT NULL
                BEGIN
                    DROP TABLE temp_lv_header_for_sales_transaction_item_incremental;
                END;
                
                SELECT
                    KeySearch COLLATE Thai_CI_AS AS KeySearch,
                    LVHeaderKey
                INTO temp_lv_header_for_sales_transaction_item_incremental
                FROM LVHeader
                WHERE {self.get_temp_tables_incremental_query_condition('DocDate')};
                CREATE NONCLUSTERED INDEX idx_temp_lv_header_for_sales_transaction_item_incremental_key_search_lv_header ON temp_lv_header_for_sales_transaction_item_incremental (KeySearch, LVHeaderKey);
            """
        )

        create_recent_sales_temp_table = f"""
            IF OBJECT_ID('temp_sales_transaction_item_recent_sales_full_dump', 'U') IS NULL
            BEGIN
                SELECT
                    CASE
                        WHEN lvh.LVHeaderKey IS NOT NULL THEN lvh.LVHeaderKey
                        ELSE dbo.fn_generate_sales_transaction_id(sst.key_search)
                    END AS sales_transaction_id,
                    sst.MatCode AS sku,
                    sst.Qty AS quantity,
                    sst.Net AS net_amount,
                    sst.Amount AS original_price,
                    lvh.LVHeaderKey
                INTO
                    temp_sales_transaction_item_recent_sales_full_dump
                FROM
                    Newmember.dbo.SMCSalesTrans sst
                    JOIN Newmember.dbo.SMCSalesHeader ssh ON ssh.key_search = sst.key_search
                    LEFT JOIN temp_lv_header_for_sales_transaction_item_full_dump lvh ON lvh.KeySearch = ssh.key_search
                WHERE
                    ssh.DataDate < CAST(CAST(GETDATE () AS DATE) AS DATETIME);

                CREATE INDEX ix_temp_sales_transaction_item_recent_sales_full_dump_sales_transaction_id
                ON temp_sales_transaction_item_recent_sales_full_dump (sales_transaction_id);
            END;
        """

        create_partitioned_temp_table = """
            IF OBJECT_ID('temp_sales_transaction_item_partitioned_full_dump', 'U') IS NULL
            BEGIN
                SELECT
                    sales_transaction_id,
                    sku,
                    quantity,
                    net_amount,
                    original_price,
                    LVHeaderKey,
                    ROW_NUMBER() OVER (
                        PARTITION BY sales_transaction_id
                        ORDER BY sales_transaction_id
                    ) AS row_num
                INTO temp_sales_transaction_item_partitioned_full_dump
                FROM temp_sales_transaction_item_recent_sales_full_dump;

                CREATE INDEX ix_temp_sales_transaction_item_partitioned_full_dump_sales_transaction_id
                ON temp_sales_transaction_item_partitioned_full_dump (sales_transaction_id);
            END;
        """

        create_point_earned_temp_table = """
            IF OBJECT_ID('temp_sales_transaction_item_point_earned_full_dump', 'U') IS NULL
            BEGIN
                SELECT 
                    lvh.LVHeaderKey AS sales_transaction_id,
                    SUM(lvt.Amount) AS normal_point_earned
                INTO
                    temp_sales_transaction_item_point_earned_full_dump
                FROM LVHeader lvh
                JOIN LVTrans lvt ON lvt.MovementCode = 'PTPOS' AND lvt.LVHeaderKey = lvh.LVHeaderKey
                GROUP BY lvh.LVHeaderKey;

                CREATE INDEX ix_temp_sales_transaction_item_point_earned_full_dump_sales_transaction_id
                ON temp_sales_transaction_item_point_earned_full_dump (sales_transaction_id);
            END;
        """

        create_result_table = """
            IF OBJECT_ID('temp_sales_transaction_item_for_full_dump_migration', 'U') IS NULL
            BEGIN
                SELECT
                    ROW_NUMBER() OVER (ORDER BY partitioned.sales_transaction_id, partitioned.sku, partitioned.row_num) AS id,
                    partitioned.sales_transaction_id,
                    partitioned.sku,
                    partitioned.quantity,
                    partitioned.net_amount,
                    partitioned.original_price,
                    CASE
                        WHEN partitioned.LVHeaderKey IS NULL THEN 0
                        WHEN partitioned.row_num = 1 THEN COALESCE(point_earned.normal_point_earned, 0)
                        ELSE 0
                    END AS normal_point_earned
                INTO temp_sales_transaction_item_for_full_dump_migration
                FROM temp_sales_transaction_item_partitioned_full_dump partitioned
                LEFT JOIN temp_sales_transaction_item_point_earned_full_dump point_earned 
                    ON point_earned.sales_transaction_id = partitioned.sales_transaction_id
                ORDER BY partitioned.sales_transaction_id;

                CREATE CLUSTERED INDEX ix_temp_sales_transaction_item_for_full_dump_migration_id
                ON temp_sales_transaction_item_for_full_dump_migration (id);
            END;
        """

        loyalty_value_connection = self.loyalty_value_handler.hook.get_conn()

        try:
            logger.info(f"started preparing LVHeader temp table for migration...")
            self.loyalty_value_handler.execute_query_string(
                connection=loyalty_value_connection,
                query_string=create_lv_header_temp_table,
            )
            logger.info(f"finished preparing LVHeader temp table for migration.")

            if is_full_dump:
                logger.info(f"started preparing recent sales temp table...")
                self.loyalty_value_handler.execute_query_string(
                    connection=loyalty_value_connection,
                    query_string=create_recent_sales_temp_table,
                )
                logger.info(f"finished preparing recent sales temp table.")

                logger.info(f"started preparing partitioned temp table...")
                self.loyalty_value_handler.execute_query_string(
                    connection=loyalty_value_connection,
                    query_string=create_partitioned_temp_table,
                )
                logger.info(f"finished preparing partitioned temp table.")

                logger.info(
                    f"started preparing point earned temp table for migration..."
                )
                self.loyalty_value_handler.execute_query_string(
                    connection=loyalty_value_connection,
                    query_string=create_point_earned_temp_table,
                )
                logger.info(
                    f"finished preparing point earned temp table for migration."
                )

                logger.info(f"started preparing result temp table for migration...")
                self.loyalty_value_handler.execute_query_string(
                    connection=loyalty_value_connection,
                    query_string=create_result_table,
                )
                logger.info(f"finished preparing result temp table for migration.")
        finally:
            loyalty_value_connection.close()

    def migrate_full_dump_product_brand(self):
        """
        The main function for ProductBrand full dump migration task.

        Args:
            None

        Returns:
            None
        """
        ProductBrand(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            incremental_query_date=self.incremental_query_date,
            mssql_handler=self.newmember_handler,
            postgresql_handler=self.postgresql_handler,
        ).migrate_full_dump()

    def migrate_incremental_product_brand(self):
        """
        The main function for ProductBrand incremental migration task.

        Args:
            None

        Returns:
            None
        """
        ProductBrand(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            incremental_query_date=self.incremental_query_date,
            mssql_handler=self.newmember_handler,
            postgresql_handler=self.postgresql_handler,
        ).migrate_incremental()

    def migrate_full_dump_product_category(self):
        """
        The main function for ProductCategory full dump migration task.

        Args:
            None

        Returns:
            None
        """
        ProductCategory(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            incremental_query_date=self.incremental_query_date,
            mssql_handler=self.newmember_handler,
            postgresql_handler=self.postgresql_handler,
        ).migrate_full_dump()

    def migrate_incremental_product_category(self):
        """
        The main function for ProductCategory incremental migration task.

        Args:
            None

        Returns:
            None
        """
        ProductCategory(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            incremental_query_date=self.incremental_query_date,
            mssql_handler=self.newmember_handler,
            postgresql_handler=self.postgresql_handler,
        ).migrate_incremental()

    def migrate_full_dump_sales_transaction(self):
        """
        The main function for SalesTransaction full dump migration task.

        Args:
            None

        Returns:
            None
        """
        SalesTransaction(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            incremental_query_date=self.incremental_query_date,
            mssql_handler=self.loyalty_value_handler,
            postgresql_handler=self.postgresql_handler,
        ).migrate_full_dump()

    def migrate_full_dump_sales_transaction_without_lv_header(self):
        """
        The main function for SalesTransaction full dump migration task.

        Args:
            None

        Returns:
            None
        """
        SalesTransaction(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            incremental_query_date=self.incremental_query_date,
            mssql_handler=self.loyalty_value_handler,
            postgresql_handler=self.postgresql_handler,
        ).migrate_full_dump_without_lv_header()

    def migrate_incremental_sales_transaction(self):
        """
        The main function for SalesTransaction incremental migration task.

        Args:
            None

        Returns:
            None
        """
        SalesTransaction(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            incremental_query_date=self.incremental_query_date,
            mssql_handler=self.loyalty_value_handler,
            postgresql_handler=self.postgresql_handler,
        ).migrate_incremental()

    def migrate_incremental_sales_transaction_without_lv_header(self):
        """
        The main function for SalesTransaction incremental migration task.

        Args:
            None

        Returns:
            None
        """
        SalesTransaction(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            incremental_query_date=self.incremental_query_date,
            mssql_handler=self.loyalty_value_handler,
            postgresql_handler=self.postgresql_handler,
        ).migrate_incremental_without_lv_header()

    def migrate_full_dump_sales_transaction_item(self):
        """
        The main function for SalesTransactionItem full dump migration task.

        Args:
            None

        Returns:
            None
        """
        SalesTransactionItem(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            incremental_query_date=self.incremental_query_date,
            mssql_handler=self.loyalty_value_handler,
            postgresql_handler=self.postgresql_handler,
        ).migrate_full_dump()

    def migrate_incremental_sales_transaction_item(self):
        """
        The main function for SalesTransactionItem incremental migration task.

        Args:
            None

        Returns:
            None
        """
        SalesTransactionItem(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            incremental_query_date=self.incremental_query_date,
            mssql_handler=self.loyalty_value_handler,
            postgresql_handler=self.postgresql_handler,
        ).migrate_incremental()

    def migrate_full_dump_sales_transaction_payment(self):
        """
        The main function for SalesTransactionPayment full dump migration task.

        Args:
            None

        Returns:
            None
        """
        SalesTransactionPayment(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            incremental_query_date=self.incremental_query_date,
            mssql_handler=self.loyalty_value_handler,
            postgresql_handler=self.postgresql_handler,
        ).migrate_full_dump()

    def migrate_incremental_sales_transaction_payment(self):
        """
        The main function for SalesTransactionPayment incremental migration task.

        Args:
            None

        Returns:
            None
        """
        SalesTransactionPayment(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            incremental_query_date=self.incremental_query_date,
            mssql_handler=self.loyalty_value_handler,
            postgresql_handler=self.postgresql_handler,
        ).migrate_incremental()

    def migrate_full_dump_sales_transaction_burn_payment(self):
        """
        The main function for SalesTransactionBurnPayment full dump migration task.

        Args:
            None

        Returns:
            None
        """
        SalesTransactionBurnPayment(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            incremental_query_date=self.incremental_query_date,
            mssql_handler=self.loyalty_value_handler,
            postgresql_handler=self.postgresql_handler,
        ).migrate_full_dump()

    def migrate_incremental_sales_transaction_burn_payment(self):
        """
        The main function for SalesTransactionBurnPayment incremental migration task.

        Args:
            None

        Returns:
            None
        """
        SalesTransactionBurnPayment(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            incremental_query_date=self.incremental_query_date,
            mssql_handler=self.loyalty_value_handler,
            postgresql_handler=self.postgresql_handler,
        ).migrate_incremental()

    def migrate_full_dump_refund_sales_transaction(self):
        """
        The main function for RefundSalesTransaction full dump migration task.

        Args:
            None

        Returns:
            None
        """
        RefundSalesTransaction(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            incremental_query_date=self.incremental_query_date,
            mssql_handler=self.loyalty_value_handler,
            postgresql_handler=self.postgresql_handler,
        ).migrate_full_dump()

    def migrate_incremental_refund_sales_transaction(self):
        """
        The main function for RefundSalesTransaction incremental migration task.

        Args:
            None

        Returns:
            None
        """
        RefundSalesTransaction(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            incremental_query_date=self.incremental_query_date,
            mssql_handler=self.loyalty_value_handler,
            postgresql_handler=self.postgresql_handler,
        ).migrate_incremental()

    def cleanup_sales_transaction_temporary_tables(self, is_full_dump: bool = True):
        """
        Cleanup temporary tables for SalesTransaction migration.

        Args:
            is_full_dump (bool): The migration type.

        Returns:
            None
        """

        try:
            loyalty_value_connection = self.loyalty_value_handler.hook.get_conn()

            with loyalty_value_connection.cursor() as cursor:
                if is_full_dump:
                    logger.info(
                        f"started dropping up Newmember.dbo.temp_smc_sales_header_for_sales_transaction_full_dump table..."
                    )
                    cursor.execute(
                        "DROP TABLE Newmember.dbo.temp_smc_sales_header_for_sales_transaction_full_dump;"
                    )
                    logger.info(
                        f"finished dropping up Newmember.dbo.temp_smc_sales_header_for_sales_transaction_full_dump table."
                    )

                    logger.info(
                        f"started dropping up Newmember.dbo.temp_smc_sales_trans_for_sales_transaction_full_dump table..."
                    )
                    cursor.execute(
                        "DROP TABLE Newmember.dbo.temp_smc_sales_trans_for_sales_transaction_full_dump;"
                    )
                    logger.info(
                        f"finished dropping up Newmember.dbo.temp_smc_sales_trans_for_sales_transaction_full_dump table."
                    )

                    logger.info(
                        f"started dropping up Newmember.dbo.temp_smc_sales_payment_for_sales_transaction_full_dump table..."
                    )
                    cursor.execute(
                        "DROP TABLE Newmember.dbo.temp_smc_sales_payment_for_sales_transaction_full_dump;"
                    )
                    logger.info(
                        f"finished dropping up Newmember.dbo.temp_smc_sales_payment_for_sales_transaction_full_dump table."
                    )

                    logger.info(
                        f"started dropping up temp_lv_header_for_sales_transaction_full_dump table..."
                    )
                    cursor.execute(
                        "DROP TABLE temp_lv_header_for_sales_transaction_full_dump;"
                    )
                    logger.info(
                        f"finished dropping up temp_lv_header_for_sales_transaction_full_dump table."
                    )

                    logger.info(
                        f"started dropping up temp_lv_trans_for_sales_transaction_full_dump table..."
                    )
                    cursor.execute(
                        "DROP TABLE temp_lv_trans_for_sales_transaction_full_dump;"
                    )
                    logger.info(
                        f"finished dropping up temp_lv_trans_for_sales_transaction_full_dump table."
                    )
                else:
                    logger.info(
                        f"started dropping up Newmember.dbo.temp_smc_sales_header_for_sales_transaction_incremental table..."
                    )
                    cursor.execute(
                        "DROP TABLE Newmember.dbo.temp_smc_sales_header_for_sales_transaction_incremental;"
                    )
                    logger.info(
                        f"finished dropping up Newmember.dbo.temp_smc_sales_header_for_sales_transaction_incremental table."
                    )

                    logger.info(
                        f"started dropping up Newmember.dbo.temp_smc_sales_trans_for_sales_transaction_incremental table..."
                    )
                    cursor.execute(
                        "DROP TABLE Newmember.dbo.temp_smc_sales_trans_for_sales_transaction_incremental;"
                    )
                    logger.info(
                        f"finished dropping up Newmember.dbo.temp_smc_sales_trans_for_sales_transaction_incremental table."
                    )

                    logger.info(
                        f"started dropping up Newmember.dbo.temp_smc_sales_payment_for_sales_transaction_incremental table..."
                    )
                    cursor.execute(
                        "DROP TABLE Newmember.dbo.temp_smc_sales_payment_for_sales_transaction_incremental;"
                    )
                    logger.info(
                        f"finished dropping up Newmember.dbo.temp_smc_sales_payment_for_sales_transaction_incremental table."
                    )

                    logger.info(
                        f"started dropping up temp_lv_header_for_sales_transaction_incremental table..."
                    )
                    cursor.execute(
                        "DROP TABLE temp_lv_header_for_sales_transaction_incremental;"
                    )
                    logger.info(
                        f"finished dropping up temp_lv_header_for_sales_transaction_incremental table."
                    )

                    logger.info(
                        f"started dropping up temp_lv_trans_for_sales_transaction_incremental table..."
                    )
                    cursor.execute(
                        "DROP TABLE temp_lv_trans_for_sales_transaction_incremental;"
                    )
                    logger.info(
                        f"finished dropping up temp_lv_trans_for_sales_transaction_incremental table."
                    )

            loyalty_value_connection.commit()
        except Exception as error:
            logger.error(f"an error has occured: {error}")

            if loyalty_value_connection:
                logger.info("a newmember connection is found, rolling back...")
                loyalty_value_connection.rollback()
                logger.info("successfully rolled back.")

            raise error
        finally:
            if loyalty_value_connection:
                loyalty_value_connection.close()

    def cleanup_sales_transaction_item_temporary_tables(
        self, is_full_dump: bool = True
    ):
        """
        Cleanup temporary tables for SalesTransactionItem migration.

        Args:
            is_full_dump (bool): The migration type.

        Returns:
            None
        """

        try:
            loyalty_value_connection = self.loyalty_value_handler.hook.get_conn()

            with loyalty_value_connection.cursor() as cursor:
                if is_full_dump:
                    logger.info(
                        f"started dropping up temp_lv_header_for_sales_transaction_item_full_dump table..."
                    )
                    cursor.execute(
                        "DROP TABLE temp_lv_header_for_sales_transaction_item_full_dump;"
                    )
                    logger.info(
                        f"finished dropping up temp_lv_header_for_sales_transaction_item_full_dump table."
                    )
                else:
                    logger.info(
                        f"started dropping up temp_lv_header_for_sales_transaction_item_incremental table..."
                    )
                    cursor.execute(
                        "DROP TABLE temp_lv_header_for_sales_transaction_item_incremental;"
                    )
                    logger.info(
                        f"finished dropping up temp_lv_header_for_sales_transaction_item_incremental table."
                    )

            loyalty_value_connection.commit()
        except Exception as error:
            logger.error(f"an error has occured: {error}")

            if loyalty_value_connection:
                logger.info("a mssql connection is found, rolling back...")
                loyalty_value_connection.rollback()
                logger.info("successfully rolled back.")

            raise error
        finally:
            if loyalty_value_connection:
                loyalty_value_connection.close()


with DAG(
    "partner_service_full_dump_migration_product_brand",
    default_args={
        "owner": "airflow",
    },
    description="Partner Service, ProductBrand full dump migration",
    schedule_interval=None,
    start_date=datetime(2025, 3, 5),
    catchup=False,
    tags=["partner_service", "full_dump", "product_brand"],
) as partner_service_full_dump_migration_product_brand_dag:
    partner_service = PartnerService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    prepare_batch_tracker_table_task = PythonOperator(
        task_id="prepare_batch_tracker_table_task",
        python_callable=partner_service.prepare_batch_tracker_table,
    )

    prepare_brand_table_task = PythonOperator(
        task_id="prepare_brand_table_task",
        python_callable=partner_service.prepare_brand_table_for_migration,
    )

    migrate_product_brand_task = PythonOperator(
        task_id="migrate_product_brand_task",
        python_callable=partner_service.migrate_full_dump_product_brand,
    )

    [
        prepare_migration_result_table_task,
        prepare_batch_tracker_table_task,
        prepare_brand_table_task,
    ] >> migrate_product_brand_task

with DAG(
    "partner_service_full_dump_migration_product_category",
    default_args={
        "owner": "airflow",
    },
    description="Partner Service, ProductCategory full dump migration",
    schedule_interval=None,
    start_date=datetime(2025, 3, 5),
    catchup=False,
    tags=["partner_service", "full_dump", "product_category"],
) as partner_service_full_dump_migration_product_category_dag:
    partner_service = PartnerService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    prepare_batch_tracker_table_task = PythonOperator(
        task_id="prepare_batch_tracker_table_task",
        python_callable=partner_service.prepare_batch_tracker_table,
    )

    prepare_brand_table_task = PythonOperator(
        task_id="prepare_brand_table_task",
        python_callable=partner_service.prepare_brand_table_for_migration,
    )

    migrate_product_category_task = PythonOperator(
        task_id="migrate_product_category_task",
        python_callable=partner_service.migrate_full_dump_product_category,
    )

    [
        prepare_migration_result_table_task,
        prepare_batch_tracker_table_task,
        prepare_brand_table_task,
    ] >> migrate_product_category_task

with DAG(
    "partner_service_full_dump_migration_sales_transaction",
    default_args={
        "owner": "airflow",
    },
    description="Partner Service, SalesTransaction full dump migration",
    schedule_interval=None,
    start_date=datetime(2025, 3, 7),
    catchup=False,
    tags=["partner_service", "full_dump", "sales_transaction"],
) as partner_service_full_dump_migration_sales_transaction_dag:
    partner_service = PartnerService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    prepare_batch_tracker_table_task = PythonOperator(
        task_id="prepare_batch_tracker_table_task",
        python_callable=partner_service.prepare_batch_tracker_table,
    )

    prepare_temp_tables_task = PythonOperator(
        task_id="prepare_temp_tables_task",
        python_callable=partner_service.prepare_temp_tables_for_sales_transaction,
        op_args=[True],
    )

    # cleanup_temporary_tables_task = PythonOperator(
    #     task_id="cleanup_temporary_tables_task",
    #     python_callable=partner_service.cleanup_sales_transaction_temporary_tables,
    #     op_args=[True],
    # )

    migrate_sales_transaction_task = PythonOperator(
        task_id="migrate_sales_transaction_task",
        python_callable=partner_service.migrate_full_dump_sales_transaction,
    )

    migrate_sales_transaction_task_without_lv_header = PythonOperator(
        task_id="migrate_sales_transaction_task_without_lv_header",
        python_callable=partner_service.migrate_full_dump_sales_transaction_without_lv_header,
    )

    (
        [
            prepare_migration_result_table_task,
            prepare_batch_tracker_table_task,
            prepare_temp_tables_task,
        ]
        >> migrate_sales_transaction_task
        >> migrate_sales_transaction_task_without_lv_header
        # >> cleanup_temporary_tables_task
    )

with DAG(
    "partner_service_full_dump_migration_sales_transaction_item",
    default_args={
        "owner": "airflow",
    },
    description="Partner Service, SalesTransactionItem full dump migration",
    schedule_interval=None,
    start_date=datetime(2025, 3, 3),
    catchup=False,
    tags=["partner_service", "full_dump", "sales_transaction_item"],
) as partner_service_full_dump_migration_sales_transaction_item_dag:
    partner_service = PartnerService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    prepare_batch_tracker_table_task = PythonOperator(
        task_id="prepare_batch_tracker_table_task",
        python_callable=partner_service.prepare_batch_tracker_table,
    )

    prepare_temp_tables_task = PythonOperator(
        task_id="prepare_temp_tables_task",
        python_callable=partner_service.prepare_temp_tables_for_sales_transaction_item,
        op_args=[True],
    )

    migrate_sales_transaction_item_task = PythonOperator(
        task_id="migrate_sales_transaction_item_task",
        python_callable=partner_service.migrate_full_dump_sales_transaction_item,
    )

    # cleanup_temporary_tables_task = PythonOperator(
    #     task_id="cleanup_temporary_tables_task",
    #     python_callable=partner_service.cleanup_sales_transaction_item_temporary_tables,
    #     op_args=[True],
    # )

    (
        [
            prepare_migration_result_table_task,
            prepare_batch_tracker_table_task,
            prepare_temp_tables_task,
        ]
        >> migrate_sales_transaction_item_task
        # >> cleanup_temporary_tables_task
    )

with DAG(
    "partner_service_full_dump_migration_sales_transaction_payment",
    default_args={
        "owner": "airflow",
    },
    description="Partner Service, SalesTransactionPayment full dump migration",
    schedule_interval=None,
    start_date=datetime(2025, 3, 5),
    catchup=False,
    tags=["partner_service", "full_dump", "sales_transaction_payment"],
) as partner_service_full_dump_migration_sales_transaction_payment_dag:
    partner_service = PartnerService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    prepare_batch_tracker_table_task = PythonOperator(
        task_id="prepare_batch_tracker_table_task",
        python_callable=partner_service.prepare_batch_tracker_table,
    )

    migrate_sales_transaction_payment_task = PythonOperator(
        task_id="migrate_sales_transaction_payment_task",
        python_callable=partner_service.migrate_full_dump_sales_transaction_payment,
    )

    [
        prepare_migration_result_table_task,
        prepare_batch_tracker_table_task,
    ] >> migrate_sales_transaction_payment_task

with DAG(
    "partner_service_full_dump_migration_sales_transaction_burn_payment",
    default_args={
        "owner": "airflow",
    },
    description="Partner Service, SalesTransactionBurnPayment full dump migration",
    schedule_interval=None,
    start_date=datetime(2025, 3, 5),
    catchup=False,
    tags=["partner_service", "full_dump", "sales_transaction_burn_payment"],
) as partner_service_full_dump_migration_sales_transaction_burn_payment_dag:
    partner_service = PartnerService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    prepare_batch_tracker_table_task = PythonOperator(
        task_id="prepare_batch_tracker_table_task",
        python_callable=partner_service.prepare_batch_tracker_table,
    )

    migrate_sales_transaction_burn_payment_task = PythonOperator(
        task_id="migrate_sales_transaction_burn_payment_task",
        python_callable=partner_service.migrate_full_dump_sales_transaction_burn_payment,
    )

    [
        prepare_migration_result_table_task,
        prepare_batch_tracker_table_task,
    ] >> migrate_sales_transaction_burn_payment_task

with DAG(
    "partner_service_full_dump_migration_refund_sales_transaction",
    default_args={
        "owner": "airflow",
    },
    description="Partner Service, RefundSalesTransaction full dump migration",
    schedule_interval=None,
    start_date=datetime(2025, 3, 5),
    catchup=False,
    tags=["partner_service", "full_dump", "refund_sales_transaction"],
) as partner_service_full_dump_migration_refund_sales_transaction_dag:
    partner_service = PartnerService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    prepare_batch_tracker_table_task = PythonOperator(
        task_id="prepare_batch_tracker_table_task",
        python_callable=partner_service.prepare_batch_tracker_table,
    )

    migrate_refund_sales_transaction_task = PythonOperator(
        task_id="migrate_refund_sales_transaction_task",
        python_callable=partner_service.migrate_full_dump_refund_sales_transaction,
    )

    [
        prepare_migration_result_table_task,
        prepare_batch_tracker_table_task,
    ] >> migrate_refund_sales_transaction_task


with DAG(
    "partner_service_incremental_migration_product_brand",
    default_args={
        "owner": "airflow",
    },
    description="Partner Service, ProductBrand incremental migration",
    schedule_interval="0 20 * * *",
    start_date=datetime(2025, 12, 31),
    catchup=False,
    tags=["partner_service", "incremental", "product_brand"],
) as partner_service_incremental_migration_product_brand_dag:
    partner_service = PartnerService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    prepare_brand_table_task = PythonOperator(
        task_id="prepare_brand_table_task",
        python_callable=partner_service.prepare_brand_table_for_migration,
    )

    migrate_product_brand_task = PythonOperator(
        task_id="migrate_product_brand_task",
        python_callable=partner_service.migrate_incremental_product_brand,
    )

    [
        prepare_migration_result_table_task,
        prepare_brand_table_task,
    ] >> migrate_product_brand_task

with DAG(
    "partner_service_incremental_migration_product_category",
    default_args={
        "owner": "airflow",
    },
    description="Partner Service, ProductCategory incremental migration",
    schedule_interval="0 20 * * *",
    start_date=datetime(2025, 12, 31),
    catchup=False,
    tags=["partner_service", "incremental", "product_category"],
) as partner_service_incremental_migration_product_category_dag:
    partner_service = PartnerService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    prepare_brand_table_task = PythonOperator(
        task_id="prepare_brand_table_task",
        python_callable=partner_service.prepare_brand_table_for_migration,
    )

    migrate_product_category_task = PythonOperator(
        task_id="migrate_product_category_task",
        python_callable=partner_service.migrate_incremental_product_category,
    )

    [
        prepare_migration_result_table_task,
        prepare_brand_table_task,
    ] >> migrate_product_category_task

with DAG(
    "partner_service_incremental_migration_sales_transaction_item",
    default_args={
        "owner": "airflow",
    },
    description="Partner Service, SalesTransactionItem incremental migration",
    schedule_interval="0 20 * * *",
    start_date=datetime(2025, 12, 31),
    catchup=False,
    tags=["partner_service", "incremental", "sales_transaction_item"],
) as partner_service_incremental_migration_sales_transaction_item_dag:
    partner_service = PartnerService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    prepare_temp_tables_task = PythonOperator(
        task_id="prepare_temp_tables_task",
        python_callable=partner_service.prepare_temp_tables_for_sales_transaction_item,
        op_args=[False],
    )

    # cleanup_temporary_tables_task = PythonOperator(
    #     task_id="cleanup_temporary_tables_task",
    #     python_callable=partner_service.cleanup_sales_transaction_item_temporary_tables,
    #     op_args=[False],
    # )

    migrate_sales_transaction_item_task = PythonOperator(
        task_id="migrate_sales_transaction_item_task",
        python_callable=partner_service.migrate_incremental_sales_transaction_item,
    )

    (
        [prepare_migration_result_table_task, prepare_temp_tables_task]
        >> migrate_sales_transaction_item_task
        # >> cleanup_temporary_tables_task
    )

with DAG(
    "partner_service_incremental_migration_sales_transaction",
    default_args={
        "owner": "airflow",
    },
    description="Partner Service, SalesTransaction incremental migration",
    schedule_interval="0 20 * * *",
    start_date=datetime(2025, 12, 31),
    catchup=False,
    tags=["partner_service", "incremental", "sales_transaction"],
) as partner_service_incremental_migration_sales_transaction_dag:
    partner_service = PartnerService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    prepare_temp_tables_task = PythonOperator(
        task_id="prepare_temp_tables_task",
        python_callable=partner_service.prepare_temp_tables_for_sales_transaction,
        op_args=[False],
    )

    # cleanup_temporary_tables_task = PythonOperator(
    #     task_id="cleanup_temporary_tables_task",
    #     python_callable=partner_service.cleanup_sales_transaction_temporary_tables,
    #     op_args=[False],
    # )

    migrate_sales_transaction_task = PythonOperator(
        task_id="migrate_sales_transaction_task",
        python_callable=partner_service.migrate_incremental_sales_transaction,
    )

    migrate_sales_transaction_without_lv_header_task = PythonOperator(
        task_id="migrate_sales_transaction_without_lv_header_task",
        python_callable=partner_service.migrate_incremental_sales_transaction_without_lv_header,
    )

    (
        [prepare_temp_tables_task, prepare_migration_result_table_task]
        >> migrate_sales_transaction_task
        >> migrate_sales_transaction_without_lv_header_task
        # >> cleanup_temporary_tables_task
    )

with DAG(
    "partner_service_incremental_migration_sales_transaction_payment",
    default_args={
        "owner": "airflow",
    },
    description="Partner Service, SalesTransactionPayment incremental migration",
    schedule_interval="0 20 * * *",
    start_date=datetime(2025, 12, 31),
    catchup=False,
    tags=["partner_service", "incremental", "sales_transaction_payment"],
) as partner_service_incremental_migration_sales_transaction_payment_dag:
    partner_service = PartnerService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    migrate_sales_transaction_payment_task = PythonOperator(
        task_id="migrate_sales_transaction_payment_task",
        python_callable=partner_service.migrate_incremental_sales_transaction_payment,
    )

    prepare_migration_result_table_task >> migrate_sales_transaction_payment_task

with DAG(
    "partner_service_incremental_migration_sales_transaction_burn_payment",
    default_args={
        "owner": "airflow",
    },
    description="Partner Service, SalesTransactionBurnPayment incremental migration",
    schedule_interval="0 20 * * *",
    start_date=datetime(2025, 12, 31),
    catchup=False,
    tags=["partner_service", "incremental", "sales_transaction_burn_payment"],
) as partner_service_incremental_migration_sales_transaction_burn_payment_dag:
    partner_service = PartnerService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    migrate_sales_transaction_burn_payment_task = PythonOperator(
        task_id="migrate_sales_transaction_burn_payment_task",
        python_callable=partner_service.migrate_incremental_sales_transaction_burn_payment,
    )

    prepare_migration_result_table_task >> migrate_sales_transaction_burn_payment_task

with DAG(
    "partner_service_incremental_migration_refund_sales_transaction",
    default_args={
        "owner": "airflow",
    },
    description="Partner Service, RefundSalesTransaction incremental migration",
    schedule_interval="0 20 * * *",
    start_date=datetime(2025, 12, 31),
    catchup=False,
    tags=["partner_service", "incremental", "refund_sales_transaction"],
) as partner_service_incremental_migration_refund_sales_transaction_dag:
    partner_service = PartnerService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    migrate_refund_sales_transaction_task = PythonOperator(
        task_id="migrate_refund_sales_transaction_task",
        python_callable=partner_service.migrate_incremental_refund_sales_transaction,
    )

    prepare_migration_result_table_task >> migrate_refund_sales_transaction_task
