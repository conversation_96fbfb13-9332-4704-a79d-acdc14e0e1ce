from datetime import datetime, timedelta

from psycopg2.extensions import connection as postgres_connection

from common_helpers.database_services import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PostgresHandler
from common_helpers.logging import get_logger
from common_helpers.utils import create_migration_result_table
from engagement_service.member_privilege import MemberPrivilege

from airflow import DAG
from airflow.models import Variable
from airflow.operators.python_operator import PythonOperator

logger = get_logger()


class EngagementService:
    def __init__(self) -> None:
        self.service_name = "engagement_service"
        self.batch_size = int(
            Variable.get(f"{self.service_name}.batch_size", default_var=10000),
        )
        self.executor_max_workers = int(
            Variable.get(f"{self.service_name}.executor_max_workers", default_var=5),
        )
        self.mssql_handler = MSSQLHandler(conn_id="loyalty_value_smc_db_connection_id")
        self.postgresql_handler = PostgresHandler(conn_id="temp_db_connection_id")

    def prepare_privilege_table_for_migration(
        self,
        connection: postgres_connection,
    ) -> None:
        """
        Insert a row into engagement_service.Privilege, with id of ENGAGEMENT_SERVICE_MIGRATION,
        which is needed for engagement_service.MemberPrivilege migration.

        Args:
            None

        Returns:
            None
        """
        insert_query = """
            INSERT INTO "engagement_service"."Privilege" (
                "id",
                "nameEn",
                "nameTh",
                "typeCode",
                "subTypeCode",
                "renewalFrequency",
                "usageCondition",
                "createdAt",
                "updatedAt",
                "status",
                "effectiveDate"
            )
            VALUES (%s, %s, %s, %s, %s, %s, %s, NOW () AT TIME ZONE 'UTC', NOW () AT TIME ZONE 'UTC', %s, NOW () AT TIME ZONE 'UTC')
            ON CONFLICT ("id") DO NOTHING;
        """

        self.postgresql_handler.execute_with_rollback(
            connection=connection,
            query_string=insert_query,
            record=(
                self.service_name,
                self.service_name,
                self.service_name,
                self.service_name,
                self.service_name,
                self.service_name,
                "{}",
                self.service_name,
            ),
        )

    def prepare_tables(self):
        """
        Prepare mandatory table(s) for Engagement Service migration.

        Args:
            None

        Returns:
            None
        """
        mssql_connection = self.mssql_handler.hook.get_conn()
        postgresql_connection = self.postgresql_handler.hook.get_conn()

        try:
            logger.info(f"started preparing Privilege table for migration...")
            self.prepare_privilege_table_for_migration(postgresql_connection)
            logger.info(f"finished preparing Privilege table for migration.")
        except Exception as error:
            logger.error(f"an error has occured: {error}")

            if postgresql_connection:
                logger.info("a postgresql connection is found, rolling back...")
                postgresql_connection.rollback()
                logger.info("successfully rolled back.")

            raise error
        finally:
            if mssql_connection:
                mssql_connection.close()
            if postgresql_connection:
                postgresql_connection.close()

    def prepare_batch_tracker_table(self):
        """
        Prepare a table to track the migration process.

        Args:
            None

        Returns:
            None
        """
        create_table_query_string = """
            CREATE TABLE IF NOT EXISTS engagement_service.batch_tracker (
                table_name VARCHAR(100) PRIMARY KEY,
                total_records INT NOT NULL,
                completed_batches JSONB NOT NULL DEFAULT '[]'::jsonb,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """
        postgresql_connection = self.postgresql_handler.hook.get_conn()

        try:
            logger.info(f"started preparing batch tracker table for migration...")
            self.postgresql_handler.execute_with_rollback(
                connection=postgresql_connection,
                query_string=create_table_query_string,
            )
            logger.info(f"finished preparing batch tracker table for migration.")
        finally:
            postgresql_connection.close()

    def migrate_member_privilege(self):
        """
        The main function for MemberPrivilege migration task.

        Args:
            None

        Returns:
            None
        """
        MemberPrivilege(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            mssql_handler=self.mssql_handler,
            postgresql_handler=self.postgresql_handler,
            service_name=self.service_name,
        ).migrate_full_dump()

    def validate_migration(self):
        """
        Validate the migrated data for all services.

        Args:
            None

        Returns:
            None
        """
        logger.info("Starting migration validation...")
        
        # Validate MemberPrivilege migration
        logger.info("Validating MemberPrivilege migration...")
        MemberPrivilege(
            batch_size=self.batch_size,
            executor_max_workers=self.executor_max_workers,
            mssql_handler=self.mssql_handler,
            postgresql_handler=self.postgresql_handler,
            service_name=self.service_name,
        ).validate_migration()
        
        logger.info("Migration validation completed")


with DAG(
    "engagement_service_migration",
    default_args={
        "owner": "airflow",
    },
    description="A dag for Engagement Service full dump and incremental migration.",
    schedule_interval="0 20 * * *",
    start_date=datetime(2025, 12, 31),
    catchup=False,
    tags=["engagement_service", "full_dump", "incremental", "member_privilege"],
) as engagement_service_migration_dag:
    engagement_service = EngagementService()

    prepare_migration_result_table_task = PythonOperator(
        task_id="prepare_migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    prepare_batch_tracker_table_task = PythonOperator(
        task_id="prepare_batch_tracker_table_task",
        python_callable=engagement_service.prepare_batch_tracker_table,
    )

    prepare_tables_task = PythonOperator(
        task_id="prepare_tables_task",
        python_callable=engagement_service.prepare_tables,
    )

    migrate_member_privilege_task = PythonOperator(
        task_id="migrate_member_privilege_task",
        retries=3,
        retry_delay=timedelta(minutes=5),
        python_callable=engagement_service.migrate_member_privilege,
    )

    validate_migration_task = PythonOperator(
        task_id="validate_migration_task",
        python_callable=engagement_service.validate_migration,
    )

    [
        prepare_migration_result_table_task,
        prepare_batch_tracker_table_task,
        prepare_tables_task,
    ] >> migrate_member_privilege_task >> validate_migration_task
