import os

import pendulum
from validations.validation_tasks_xc_logic import (
    validate_engagement_service,
    validate_loyalty_service, 
    validate_partner_service, 
    validate_point_service
)

from airflow import DAG
from airflow.operators.empty import EmptyOperator
from airflow.operators.python import PythonOperator

file_name = os.path.basename(__file__).split('.')[0]

default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'start_date': pendulum.now('Asia/Bangkok').add(days=-1)
}

with DAG(
    dag_id=file_name,
    default_args=default_args,
    description='validate data exclude logic from temp database to gwl database',
    schedule=None,
    catchup=False,
    params = {
        'date': pendulum.now('Asia/Bangkok').to_iso8601_string(),
    },
    tags=['crossdb_stg_gwl', 'validation', 'exclude-logic']
) as dag:
    start_validation_task = EmptyOperator(task_id="start_validation")
    end_validation_task = EmptyOperator(task_id="end_validation")

    validate_engagement_service_task = PythonOperator(
        task_id='validate_engagement_service',
        python_callable=validate_engagement_service,
        dag=dag
    )

    validate_loyalty_service_task = PythonOperator(
        task_id='validate_loyalty_service',
        python_callable=validate_loyalty_service,
        dag=dag
    )

    validate_partner_service_task = PythonOperator(
        task_id='validate_partner_service',
        python_callable=validate_partner_service,
        dag=dag
    )

    validate_point_service_task = PythonOperator(
        task_id='validate_point_service',
        python_callable=validate_point_service,
        dag=dag
    )

    start_validation_task >> validate_engagement_service_task >> \
        validate_loyalty_service_task >> validate_partner_service_task >> \
        validate_point_service_task >> end_validation_task
