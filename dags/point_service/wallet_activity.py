import json
import math
import os
import threading
from concurrent.futures import as_completed, ThreadPoolExecutor
from datetime import datetime, timedelta

from psycopg2.extensions import connection as postgres_connection

from common_helpers.database_services import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PostgresHandler
from common_helpers.mapper import CSVMapper
from common_helpers.utils import get_query_offsets, insert_migration_result
from common_helpers.logging import get_logger

logger = get_logger()


class WalletActivity:
    def __init__(
        self,
        batch_size: int,
        executor_max_workers: int,
        mssql_handler: MSSQLHandler,
        postgresql_handler: <PERSON>gres<PERSON>and<PERSON>,
        incremental_query_date: str = None,
    ) -> None:
        self.batch_size = batch_size
        self.executor_max_workers = executor_max_workers
        self.incremental_query_date = incremental_query_date
        self.wallet_mapping_file_mapper = CSVMapper(
            file_path=os.path.join(
                "dags",
                "data",
                "wallet_mapping.csv",
            ),
            key="Value Code",
            sub_keys=["WalletCode"],
        )
        self.wallet_activity_type_mapping_mapper = CSVMapper(
            file_path=os.path.join(
                "dags",
                "data",
                "wallet_activity_type_mapping.csv",
            ),
            key="SMC MovementCode",
            sub_keys=["GWL Wallet Activity Type Code", "GWL Wallet Acitivity Ref Type"],
        )
        self.destination_insert_query = """
            INSERT INTO "point_service"."WalletActivity" (
                "id",
                "memberId",
                "walletCode",
                "type",
                "refType",
                "refId",
                "externalId",
                "amount",
                "partnerCode",
                "remark",
                "createdAt",
                "updatedAt",
                "documentDate",
                "branchCode",
                "detail"
            )
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON CONFLICT ("id") DO NOTHING;
        """

        self.mssql_handler = mssql_handler
        self.postgresql_handler = postgresql_handler

    def get_incremental_query_condition(self, date_field: str) -> str:
        if self.incremental_query_date is None:
            return f"{date_field} >= DATEADD(DAY, -1, CAST(CAST(GETDATE() AS DATE) AS DATETIME)) AND {date_field} < CAST(CAST(GETDATE() AS DATE) AS DATETIME)"

        return f"{date_field} >= CAST(CAST({self.incremental_query_date} AS DATE) AS DATETIME) AND {date_field} < DATEADD(DAY, 1, CAST(CAST({self.incremental_query_date} AS DATE) AS DATETIME))"

    def get_count_query_string(self, is_full_dump: bool = True) -> str:
        """
        Generates a query string for counting total records for both full dump and
        incremental migration.

        Args:
            is_full_dump (bool): Migration type.

        Returns:
            str: A query string.
        """
        return (
            """
                SELECT COUNT(*)
                FROM temp_wallet_activity_result_for_full_dump_migration;
            """
            if is_full_dump
            else f"""
                WITH ranked_data AS (
                    SELECT
                        *,
                        ROW_NUMBER() OVER (PARTITION BY wallet_code, type, external_id ORDER BY ref_id DESC) AS row_number
                    FROM temp_wallet_activity_for_incremental_migration
                ), sum_data AS (
                    SELECT
                        wallet_code, type, external_id, SUM(amount) AS amount
                    FROM temp_wallet_activity_for_incremental_migration
                    GROUP BY wallet_code, type, external_id
                ), grouped_result AS (
                    SELECT
                        r.id,
                        r.member_id,
                        r.wallet_code,
                        r.[type],
                        r.ref_type,
                        r.ref_id,
                        r.external_id,
                        s.amount,
                        r.partner_code,
                        r.remark,
                        r.created_at,
                        r.updated_at,
                        r.document_date,
                        r.add_user,
                        r.branch_code
                    FROM ranked_data r
                    INNER JOIN sum_data s
                        ON r.wallet_code = s.wallet_code AND r.type = s.type AND r.external_id = s.external_id
                    WHERE r.row_number = 1
                )
                SELECT COUNT(*)
                FROM grouped_result;
            """
        )

    def get_select_query_string(self, is_full_dump: str) -> str:
        """
        Generates a query string for selecting records from source for both full dump and
        incremental migration.

        Args:
            is_full_dump (bool): Migration type.

        Returns:
            str: A query string.
        """
        return (
            """
                SELECT
                    id,
                    member_id,
                    wallet_code,
                    [type],
                    ref_type,
                    ref_id,
                    external_id,
                    amount,
                    partner_code,
                    remark,
                    created_at,
                    updated_at,
                    document_date,
                    add_user,
                    branch_code,
                    movement_code
                FROM temp_wallet_activity_result_for_full_dump_migration
                ORDER BY id
                OFFSET
                    %s ROWS
                FETCH NEXT
                    %s ROWS ONLY;
            """
            if is_full_dump
            else """
                WITH ranked_data AS (
                    SELECT
                        *,
                        ROW_NUMBER() OVER (PARTITION BY wallet_code, type, external_id ORDER BY ref_id DESC) AS row_number
                    FROM temp_wallet_activity_for_incremental_migration
                ), sum_data AS (
                    SELECT
                        wallet_code, type, external_id, SUM(amount) AS amount
                    FROM temp_wallet_activity_for_incremental_migration
                    GROUP BY wallet_code, type, external_id
                ), grouped_result AS (
                    SELECT
                        r.id,
                        r.member_id,
                        r.wallet_code,
                        r.[type],
                        r.ref_type,
                        r.ref_id,
                        r.external_id,
                        s.amount,
                        r.partner_code,
                        r.remark,
                        DATEADD (HOUR, -7, r.created_at) AS created_at,
                        DATEADD (HOUR, -7, r.updated_at) AS updated_at,
                        r.document_date,
                        r.add_user,
                        r.branch_code,
                        r.movement_code
                    FROM ranked_data r
                    INNER JOIN sum_data s
                        ON r.wallet_code = s.wallet_code AND r.type = s.type AND r.external_id = s.external_id
                    WHERE r.row_number = 1
                )
                SELECT *
                FROM grouped_result
                ORDER BY id
                OFFSET
                    %s ROWS
                FETCH NEXT
                    %s ROWS ONLY;
            """
        )

    def map_value_code_to_wallet_code(self, value_code_field: str) -> str:
        wallet_code_mapping = {}

        for value_code in self.wallet_mapping_file_mapper.mapping:
            wallet_code = self.wallet_mapping_file_mapper.mapping.get(value_code)[
                "WalletCode"
            ]

            if wallet_code not in wallet_code_mapping:
                wallet_code_mapping[wallet_code] = []

            wallet_code_mapping[wallet_code].append(value_code)

        wallet_code_mapping_query = " ".join(
            [
                f"WHEN {value_code_field} IN ({', '.join([repr(each) for each in wallet_code_mapping[wallet_code]])}) THEN '{wallet_code}'"
                for wallet_code in wallet_code_mapping
            ]
        )

        return f"CASE {wallet_code_mapping_query} END"

    def map_movement_code_to_type(self, movement_code_field: str) -> str:
        type_mappping = {}

        for movement_code in self.wallet_activity_type_mapping_mapper.mapping:
            type = self.wallet_activity_type_mapping_mapper.mapping.get(movement_code)[
                "GWL Wallet Activity Type Code"
            ]

            if type not in type_mappping:
                type_mappping[type] = []

            type_mappping[type].append(movement_code)

        type_mappping_query = " ".join(
            [
                f"WHEN {movement_code_field} IN ({', '.join([repr(each) for each in type_mappping[type]])}) THEN '{type}'"
                for type in type_mappping
            ]
        )

        return f"CASE {type_mappping_query} END"

    def map_movement_code_to_ref_type(self, movement_code_field: str) -> str:
        ref_type_mappping = {}

        for movement_code in self.wallet_activity_type_mapping_mapper.mapping:
            type = self.wallet_activity_type_mapping_mapper.mapping.get(movement_code)[
                "GWL Wallet Acitivity Ref Type"
            ]

            if type not in ref_type_mappping:
                ref_type_mappping[type] = []

            ref_type_mappping[type].append(movement_code)

        ref_type_mappping_query = " ".join(
            [
                f"WHEN {movement_code_field} IN ({', '.join([repr(each) for each in ref_type_mappping[type]])}) THEN '{type}'"
                for type in ref_type_mappping
            ]
        )

        return f"CASE {ref_type_mappping_query} END"

    def prepare_wallet_activity_temp_table(self, is_full_dump: bool = True):
        """
        Prepare temporary ungrouped WalletActivity table for migration.

        Args:
            is_full_dump (bool): Is the migration type full dump.

        Returns:
            None
        """

        create_ungrouped_wallet_activity_temp_table = (
            f"""
                IF OBJECT_ID('temp_wallet_activity_for_full_dump_migration', 'U') IS NULL
                BEGIN
                    WITH filtered_lv_header_extend AS (
                        SELECT
                            LVHeaderExtendKey,
                            LVHeaderKey,
                            Remark,
                            ROW_NUMBER() OVER (
                                PARTITION BY LVHeaderKey
                                ORDER BY LVHeaderExtendKey
                            ) AS row_num
                        FROM LVHeaderExtend
                    ), mapped_lv_data AS (
                        SELECT
                            LVMainKey,
                            LVNumber AS member_id,
                            {self.map_value_code_to_wallet_code('ValueCode')} AS wallet_code
                        FROM LVData
                        WHERE ValueCode IN (
                            'AP001',
                            'EP001',
                            'EP002',
                            'EP003',
                            'EP004',
                            'EP005',
                            'EP006',
                            'EP007',
                            'EP008',
                            'EP009',
                            'EP010',
                            'KPC01',
                            'KPO02',
                            'CR001',
                            'PT001'
                        )
                    ), mapped_lv_trans AS (
                        SELECT
                            LVHeaderKey,
                            LVMainKey,
                            Amount,
                            MovementCode,
                            {self.map_movement_code_to_type('MovementCode')} AS type,
                            {self.map_movement_code_to_ref_type('MovementCode')} AS ref_type
                        FROM LVTrans
                        WHERE MovementCode IN (
                            'ADJ',
                            'ADJOUT',
                            'BD',
                            'BE',
                            'CB',
                            'CBO',
                            'CHG',
                            'CVIN',
                            'CVOUT',
                            'CVPE',
                            'EADJ',
                            'EADJOUT',
                            'EXP',
                            'ISS',
                            'JS-ADJIN',
                            'JS-ADJOUT',
                            'MIGRATE',
                            'POS',
                            'PTPOS',
                            'RD',
                            'RDG',
                            'STRET',
                            'UGC',
                            'UGCI',
                            'USE'
                        )
                    )
                    SELECT
                        CAST(lvh.LVHeaderKey AS VARCHAR(20)) + '_' + CAST(lvt.type AS VARCHAR) + '_' + CAST(lvd.wallet_code AS VARCHAR) + '_' + CAST(lvd.member_id AS VARCHAR(10)) AS id,
                        lvd.member_id,
                        lvd.wallet_code,
                        lvt.type,
                        lvt.ref_type,
                        lvh.LVHeaderKey AS ref_id,
                        lvh.KeySearch AS external_id,
                        SUM(lvt.Amount) AS amount,
                        lvh.BranchNo AS partner_code,
                        MAX(CAST(lvhe.Remark AS NVARCHAR(max))) AS remark,
                        lvh.AddDT AS created_at,
                        lvh.FinishDT AS updated_at,
                        CAST(lvh.DocDate AS DATE) AS document_date,
                        lvh.AddUser AS add_user,
                        ssh.Site AS branch_code,
                        lvt.MovementCode AS movement_code
                    INTO temp_wallet_activity_for_full_dump_migration
                    FROM
                        temp_lv_header_for_wallet_activity_full_dump lvh
                        LEFT JOIN filtered_lv_header_extend lvhe WITH (NOLOCK) ON lvhe.LVHeaderKey = lvh.LVHeaderKey AND lvhe.row_num = 1
                        LEFT JOIN Newmember.dbo.temp_smc_sales_header_for_wallet_activity_full_dump ssh ON ssh.key_search = lvh.KeySearch AND ssh.DataDate < CAST(CAST(GETDATE() AS DATE) AS DATETIME)
                        JOIN mapped_lv_trans lvt ON lvt.LVHeaderKey = lvh.LVHeaderKey
                        JOIN mapped_lv_data lvd ON lvd.LVMainKey = lvt.LVMainKey
                    WHERE
                        lvh.AddDT < CAST(CAST(GETDATE() AS DATE) AS DATETIME)
                    GROUP BY
                        lvh.LVHeaderKey,
                        lvh.KeySearch,
                        lvh.BranchNo,
                        lvh.AddUser,
                        lvh.AddDT,
                        lvh.FinishDT,
                        lvh.DocDate,
                        lvt.MovementCode,
                        lvt.type,
                        lvt.ref_type,
                        lvd.wallet_code,
                        lvd.member_id,
                        ssh.Site
                    ORDER BY
                        lvh.LVHeaderKey;
                END;
            """
            if is_full_dump
            else f"""
                IF OBJECT_ID('temp_wallet_activity_for_incremental_migration', 'U') IS NOT NULL
                BEGIN
                    DROP TABLE temp_wallet_activity_for_incremental_migration;
                END;

                WITH filtered_lv_header_extend AS (
                    SELECT
                        LVHeaderExtendKey,
                        LVHeaderKey,
                        Remark,
                        ROW_NUMBER() OVER (
                            PARTITION BY LVHeaderKey
                            ORDER BY LVHeaderExtendKey
                        ) AS row_num
                    FROM LVHeaderExtend
                ), mapped_lv_data AS (
                    SELECT
                        LVMainKey,
                        LVNumber AS member_id,
                        {self.map_value_code_to_wallet_code('ValueCode')} AS wallet_code
                    FROM LVData
                    WHERE ValueCode IN (
                        'AP001',
                        'EP001',
                        'EP002',
                        'EP003',
                        'EP004',
                        'EP005',
                        'EP006',
                        'EP007',
                        'EP008',
                        'EP009',
                        'EP010',
                        'KPC01',
                        'KPO02',
                        'CR001',
                        'PT001'
                    )
                ), mapped_lv_trans AS (
                    SELECT
                        LVHeaderKey,
                        LVMainKey,
                        Amount,
                        MovementCode,
                        {self.map_movement_code_to_type('MovementCode')} AS type,
                        {self.map_movement_code_to_ref_type('MovementCode')} AS ref_type
                    FROM LVTrans
                    WHERE MovementCode IN (
                        'ADJ',
                        'ADJOUT',
                        'BD',
                        'BE',
                        'CB',
                        'CBO',
                        'CHG',
                        'CVIN',
                        'CVOUT',
                        'CVPE',
                        'EADJ',
                        'EADJOUT',
                        'EXP',
                        'ISS',
                        'JS-ADJIN',
                        'JS-ADJOUT',
                        'MIGRATE',
                        'POS',
                        'PTPOS',
                        'RD',
                        'RDG',
                        'STRET',
                        'UGC',
                        'UGCI',
                        'USE'
                    )
                )
                SELECT
                    CAST(lvh.LVHeaderKey AS VARCHAR(20)) + '_' + CAST(lvt.type AS VARCHAR) + '_' + CAST(lvd.wallet_code AS VARCHAR) + '_' + CAST(lvd.member_id AS VARCHAR(10)) AS id,
                    lvd.member_id,
                    lvd.wallet_code,
                    lvt.type,
                    lvt.ref_type,
                    lvh.LVHeaderKey AS ref_id,
                    lvh.KeySearch AS external_id,
                    SUM(lvt.Amount) AS amount,
                    lvh.BranchNo AS partner_code,
                    MAX(CAST(lvhe.Remark AS NVARCHAR(max))) AS remark,
                    lvh.AddDT AS created_at,
                    lvh.FinishDT AS updated_at,
                    CAST(lvh.DocDate AS DATE) AS document_date,
                    lvh.AddUser AS add_user,
                    ssh.Site AS branch_code,
                    lvt.MovementCode AS movement_code
                INTO temp_wallet_activity_for_incremental_migration
                FROM
                    temp_lv_header_for_wallet_activity_incremental lvh
                    LEFT JOIN filtered_lv_header_extend lvhe WITH (NOLOCK) ON lvhe.LVHeaderKey = lvh.LVHeaderKey AND lvhe.row_num = 1
                    LEFT JOIN Newmember.dbo.temp_smc_sales_header_for_wallet_activity_incremental ssh ON ssh.key_search = lvh.KeySearch AND {self.get_incremental_query_condition('ssh.DataDate')}
                    JOIN mapped_lv_trans lvt ON lvt.LVHeaderKey = lvh.LVHeaderKey
                    JOIN mapped_lv_data lvd ON lvd.LVMainKey = lvt.LVMainKey
                WHERE
                    {self.get_incremental_query_condition('lvh.DocDate')}
                GROUP BY
                    lvh.LVHeaderKey,
                    lvh.KeySearch,
                    lvh.BranchNo,
                    lvh.AddUser,
                    lvh.AddDT,
                    lvh.FinishDT,
                    lvh.DocDate,
                    lvt.MovementCode,
                    lvt.type,
                    lvt.ref_type,
                    lvd.wallet_code,
                    lvd.member_id,
                    ssh.Site
                ORDER BY
                    lvh.LVHeaderKey
            """
        )

        create_wallet_activity_temp_table = """
            IF OBJECT_ID('temp_wallet_activity_result_for_full_dump_migration', 'U') IS NULL
            BEGIN
                WITH ranked_data AS (
                    SELECT
                        *,
                        ROW_NUMBER() OVER (PARTITION BY wallet_code, type, external_id ORDER BY ref_id DESC) AS row_number
                    FROM temp_wallet_activity_for_full_dump_migration
                ), sum_data AS (
                    SELECT
                        wallet_code, type, external_id, SUM(amount) AS amount
                    FROM temp_wallet_activity_for_full_dump_migration
                    GROUP BY wallet_code, type, external_id
                ), grouped_result AS (
                    SELECT
                        r.id,
                        r.member_id,
                        r.wallet_code,
                        r.[type],
                        r.ref_type,
                        r.ref_id,
                        r.external_id,
                        s.amount,
                        r.partner_code,
                        r.remark,
                        DATEADD (HOUR, -7, r.created_at) AS created_at,
                        DATEADD (HOUR, -7, r.updated_at) AS updated_at,
                        r.document_date,
                        r.add_user,
                        r.branch_code,
                        r.movement_code
                    FROM ranked_data r
                    INNER JOIN sum_data s
                        ON r.wallet_code = s.wallet_code AND r.type = s.type AND r.external_id = s.external_id
                    WHERE r.row_number = 1
                )
                SELECT *
                INTO temp_wallet_activity_result_for_full_dump_migration
                FROM grouped_result;

                CREATE CLUSTERED INDEX ix_temp_wallet_activity_result_for_full_dump_migration_id
                ON temp_wallet_activity_result_for_full_dump_migration (id);
            END;
        """

        loyalty_value_handler = MSSQLHandler(
            conn_id="loyalty_value_smc_db_connection_id"
        )
        loyalty_value_connection = loyalty_value_handler.hook.get_conn()

        try:
            logger.info(
                f"started preparing ungrouped WalletActivity temp table for migration..."
            )
            loyalty_value_handler.execute_query_string(
                connection=loyalty_value_connection,
                query_string=create_ungrouped_wallet_activity_temp_table,
            )
            logger.info(
                f"finished preparing ungrouped WalletActivity temp table for migration."
            )

            if is_full_dump:
                logger.info(
                    f"started preparing WalletActivity temp table for migration..."
                )
                loyalty_value_handler.execute_query_string(
                    connection=loyalty_value_connection,
                    query_string=create_wallet_activity_temp_table,
                )
                logger.info(
                    f"finished preparing WalletActivity temp table for migration."
                )
        finally:
            loyalty_value_connection.close()

    def transform_record(
        self,
        record: tuple,
    ) -> tuple:
        """
        Transform a record queried from source table to destination table schema format.

        Args:
            record (tuple): A record queried from source table.

        Returns:
            tuple: A record in destination table schema format.
        """
        (
            id,
            member_id,
            wallet_code,
            type,
            ref_type,
            ref_id,
            external_id,
            amount,
            partner_code,
            remark,
            created_at,
            updated_at,
            document_date,
            add_user,
            branch_code,
            movement_code,
        ) = record

        if movement_code in ["PTPOS", "CB"] and amount < 0:
            type = "VOID_EARNED_BY_PURCHASE"
        elif movement_code in ["USE"] and amount > 0:
            type = "VOID_BURNED_BY_PURCHASE"

        if type == "OTHER":
            type = f"{type}_{'ADD' if amount >= 0 else 'DEDUCT'}"
            ref_type = "MIGRATION"

        detail = (
            json.dumps({}) if add_user is None else json.dumps({"createdBy": add_user})
        )

        return (
            id,
            member_id,
            wallet_code,
            type,
            ref_type,
            ref_id,
            external_id,
            amount,
            partner_code,
            remark,
            created_at,
            updated_at,
            document_date,
            branch_code,
            detail,
        )

    def insert_batch_to_destination(
        self,
        connection: postgres_connection,
        batch: list[tuple],
    ) -> None:
        """
        Insert a batch to destination table.

        Args:
            connection (postgres_connection): A PostgreSQL connection.
            batch (list): A list of  records to insert to destination table.

        Returns:
            None
        """
        self.postgresql_handler.execute_with_rollback(
            connection, self.destination_insert_query, batch
        )

    def process_batch(
        self,
        connection: postgres_connection,
        batch: list[tuple],
        batch_no: int,
        total_batches: int,
        total_records: int,
        is_full_dump: bool,
    ) -> None:
        """
        Transform queried result from source table and insert them to a new table.

        Args:
            connection (postgres_connection): A PostgreSQL connection.
            batch (list): A batch to process.
            batch_no (int): The current batch's number, used only for logging.
            total_batches (int): The total number of batches to process, used only for logging.

        Returns:
            None
        """
        logger.info(
            f"started transforming and inserting batch {batch_no}/{total_batches} (size {len(batch)})..."
        )
        transformed_batch = [self.transform_record(record=record) for record in batch]
        self.insert_batch_to_destination(
            connection=connection,
            batch=transformed_batch,
        )
        logger.info(
            f"successfully transformed and inserted batch {batch_no}/{total_batches} (size {len(batch)})."
        )

        if is_full_dump:
            self.postgresql_handler.update_batch_tracker(
                connection=connection,
                service_name="point_service",
                table_name="WalletActivity",
                total_records=total_records,
                batch_no=batch_no,
            )

    def migrate(
        self,
        count_query_string: str,
        select_query_string: str,
        is_full_dump: bool = True,
    ) -> None:
        """
        The main function for WalletActivity migration flow.

        Args:
            None

        Returns:
            None
        """
        created_at = datetime.now()
        incremental_date = (
            None
            if is_full_dump
            else (
                self.incremental_query_date
                if self.incremental_query_date is not None
                else (datetime.today() - timedelta(days=1)).strftime("%Y-%m-%d")
            )
        )

        mssql_connection = self.mssql_handler.hook.get_conn()
        postgresql_connection = self.postgresql_handler.hook.get_conn()

        tracker = (
            self.postgresql_handler.get_latest_batch_info(
                connection=postgresql_connection,
                service_name="point_service",
                table_name="WalletActivity",
            )
            if is_full_dump
            else None
        )

        self.prepare_wallet_activity_temp_table(is_full_dump=is_full_dump)

        total_records = (
            self.mssql_handler.get_table_total_records(count_query_string)
            if tracker is None
            else tracker[0]
        )
        total_batches = math.ceil(total_records / self.batch_size)
        offsets = get_query_offsets(
            total_records=total_records,
            batch_size=self.batch_size,
            starting_offset=(
                0 if tracker is None else (tracker[1] - 1) * self.batch_size
            ),
        )
        completed_batches = tracker[2] if tracker is not None else []

        is_migration_succeeded = False

        try:
            futures = []

            batch_generator = self.mssql_handler.generate_batches(
                connection=mssql_connection,
                query_string=select_query_string,
                total_records=total_records,
                batch_size=self.batch_size,
                offsets=offsets,
                completed_batches=completed_batches,
            )

            with ThreadPoolExecutor(max_workers=self.executor_max_workers) as executor:
                semaphore = threading.Semaphore(self.executor_max_workers)

                while True:
                    semaphore.acquire()

                    try:
                        batch, batch_no = next(batch_generator)
                    except StopIteration:
                        break

                    future = executor.submit(
                        self.process_batch,
                        postgresql_connection,
                        batch,
                        batch_no,
                        total_batches,
                        total_records,
                        is_full_dump,
                    )
                    futures.append(future)
                    future.add_done_callback(lambda _: semaphore.release())

                for future in as_completed(futures):
                    future.result()

            logger.info(
                f"succesfully processed {total_records} records into WalletActivity"
            )

            logger.info(f"started cleaning up batch tracker...")
            self.postgresql_handler.cleanup_batch_tracker(
                connection=postgresql_connection,
                service_name="point_service",
                table_name="WalletActivity",
            )
            logger.info(f"finished cleaning up batch tracker.")

            is_migration_succeeded = True

        except Exception as error:
            logger.error(f"an error has occured: {error}")

            if postgresql_connection:
                logger.info("a postgresql connection is found, rolling back...")
                postgresql_connection.rollback()
                logger.info("successfully rolled back.")

            raise error
        finally:
            logger.info("started inserting migration result log...")
            if is_migration_succeeded:
                insert_migration_result(
                    postgresql_handler=self.postgresql_handler,
                    dag_name=f"point_service_{'full_dump' if is_full_dump else 'incremental'}_migration_wallet_activity",
                    migration_type="FULL_DUMP" if is_full_dump else "INCREMENTAL",
                    source_table="LVHeader",
                    source_table_count=total_records,
                    destination_table="WalletActivity",
                    destination_table_count=total_records,
                    validation_type="COMPLETENESS",
                    validation_result=100,
                    created_at=created_at,
                    incremental_date=incremental_date,
                )
            else:
                tracker = self.postgresql_handler.get_latest_batch_info(
                    connection=postgresql_connection,
                    service_name="point_service",
                    table_name="WalletActivity",
                )

                destination_table_count = (
                    0
                    if tracker is None
                    else (
                        ((tracker[1] - 1) * self.batch_size)
                        if (tracker[1] - 1) * self.batch_size <= total_records
                        else total_records
                    )
                )

                total_processed = (
                    0 if tracker is None else len(tracker[2]) * self.batch_size
                )

                if tracker is not None and total_batches in tracker[2]:
                    total_processed -= self.batch_size + total_records % self.batch_size

                insert_migration_result(
                    postgresql_handler=self.postgresql_handler,
                    dag_name=f"point_service_{'full_dump' if is_full_dump else 'incremental'}_migration_wallet_activity",
                    migration_type="FULL_DUMP" if is_full_dump else "INCREMENTAL",
                    source_table="LVHeader",
                    source_table_count=total_records,
                    destination_table="WalletActivity",
                    destination_table_count=destination_table_count,
                    validation_type="COMPLETENESS",
                    validation_result=(
                        0 if tracker is None else total_processed / total_records * 100
                    ),
                    created_at=created_at,
                    incremental_date=incremental_date,
                )
            logger.info("finished inserting migration result log.")

            if mssql_connection:
                mssql_connection.close()
            if postgresql_connection:
                postgresql_connection.close()

    def migrate_full_dump(self) -> None:
        """
        The main function for WalletActivity full dump migration flow.

        Args:
            None

        Returns:
            None
        """
        full_dump_count_query_string = self.get_count_query_string(is_full_dump=True)
        full_dump_select_query_string = self.get_select_query_string(is_full_dump=True)

        self.migrate(
            count_query_string=full_dump_count_query_string,
            select_query_string=full_dump_select_query_string,
            is_full_dump=True,
        )

    def migrate_incremental(self) -> None:
        """
        The main function for WalletActivity incremental migration flow.

        Args:
            None

        Returns:
            None
        """
        incremental_count_query_string = self.get_count_query_string(is_full_dump=False)
        incremental_select_query_string = self.get_select_query_string(
            is_full_dump=False
        )

        self.migrate(
            count_query_string=incremental_count_query_string,
            select_query_string=incremental_select_query_string,
            is_full_dump=False,
        )
