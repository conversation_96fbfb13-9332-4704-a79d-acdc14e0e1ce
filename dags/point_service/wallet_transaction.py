import math
import os
import threading
from concurrent.futures import as_completed, ThreadPoolExecutor
from datetime import datetime, timedelta

from psycopg2.extensions import connection as postgres_connection

from common_helpers.database_services import <PERSON>S<PERSON><PERSON><PERSON><PERSON>, <PERSON>gresHandler, QueryCache
from common_helpers.mapper import CSVMapper
from common_helpers.utils import get_query_offsets, insert_migration_result
from common_helpers.logging import get_logger

logger = get_logger()

MAST_Movement_CACHE_TIME = 900


class WalletTransaction:
    def __init__(
        self,
        batch_size: int,
        executor_max_workers: int,
        mssql_handler: MSSQLHandler,
        postgresql_handler: <PERSON>gresHandler,
        incremental_query_date: str = None,
    ) -> None:
        self.batch_size = batch_size
        self.executor_max_workers = executor_max_workers
        self.incremental_query_date = incremental_query_date
        self.wallet_mapping_file_mapper = CSVMapper(
            file_path=os.path.join(
                "dags",
                "data",
                "wallet_mapping.csv",
            ),
            key="Value Code",
            sub_keys=["WalletCode"],
        )
        self.wallet_activity_type_mapping_mapper = CSVMapper(
            file_path=os.path.join(
                "dags",
                "data",
                "wallet_activity_type_mapping.csv",
            ),
            key="SMC MovementCode",
            sub_keys=["GWL Wallet Activity Type Code"],
        )
        self.destination_insert_query = """
            INSERT INTO "point_service"."WalletTransaction" (
                "id",
                "memberId",
                "walletActivityId",
                "balanceId",
                "type",
                "walletCode",
                "amount",
                "expiredAt",
                "createdAt"
            )
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON CONFLICT ("id") DO NOTHING;
        """

        self.mssql_handler = mssql_handler
        self.postgresql_handler = postgresql_handler

    def get_incremental_query_condition(self) -> str:
        if self.incremental_query_date is None:
            return "lvh.DocDate >= DATEADD(DAY, -1, CAST(CAST(GETDATE() AS DATE) AS DATETIME)) AND lvh.DocDate < CAST(CAST(GETDATE() AS DATE) AS DATETIME)"

        return f"lvh.DocDate >= CAST(CAST({self.incremental_query_date} AS DATE) AS DATETIME) AND lvh.DocDate < DATEADD(DAY, 1, CAST(CAST({self.incremental_query_date} AS DATE) AS DATETIME))"

    def prepare_wallet_transaction_temp_table(self):
        """
        Prepare WalletTransaction temp table for migration.

        Args:
            None

        Returns:
            None
        """

        create_wallet_transaction_temp_table_query = """
            IF OBJECT_ID('temp_wallet_transaction_for_full_dump_migration', 'U') IS NULL
            BEGIN
                SELECT
                    CAST(lvh.LVHeaderKey AS VARCHAR(20)) + '_' + CAST(lvt.MovementCode AS VARCHAR(10)) + '_' + CAST(lvd.ValueCode AS VARCHAR(10)) + '_' + CAST(lvt.LVMainKey AS VARCHAR(20)) AS id,
                    lvd.LVNumber AS member_id,
                    lvh.LVHeaderKey AS lv_header_key,
                    lvt.LVMainKey AS balance_id,
                    lvt.MovementCode AS movement_code,
                    lvd.ValueCode AS value_code,
                    SUM(lvt.Amount) AS amount,
                    CASE 
                        WHEN lvd.[ExpireDate] = CAST(lvd.[ExpireDate] AS DATE)
                        THEN DATEADD(
                            MILLISECOND,
                            61199999, 
                            CAST(lvd.[ExpireDate] AS DATETIME2(3))
                        )
                        ELSE DATEADD (HOUR, -7, lvd.[ExpireDate])
                    END AS expired_at,
                    DATEADD (HOUR, -7, lvh.AddDT) AS created_at
                INTO temp_wallet_transaction_for_full_dump_migration
                FROM
                    LVHeader lvh
                    JOIN LVTrans lvt ON lvt.LVHeaderKey = lvh.LVHeaderKey
                    JOIN LVData lvd ON lvd.LVMainKey = lvt.LVMainKey
                WHERE
                    lvt.MovementCode IN (
                        'ADJ',
                        'ADJOUT',
                        'BD',
                        'BE',
                        'CB',
                        'CBO',
                        'CHG',
                        'CVIN',
                        'CVOUT',
                        'CVPE',
                        'EADJ',
                        'EADJOUT',
                        'EXP',
                        'ISS',
                        'JS-ADJIN',
                        'JS-ADJOUT',
                        'MIGRATE',
                        'POS',
                        'PTPOS',
                        'RD',
                        'RDG',
                        'STRET',
                        'UGC',
                        'UGCI',
                        'USE'
                    )
                    AND lvd.ValueCode IN (
                        'AP001',
                        'EP001',
                        'EP002',
                        'EP003',
                        'EP004',
                        'EP005',
                        'EP006',
                        'EP007',
                        'EP008',
                        'EP009',
                        'EP010',
                        'KPC01',
                        'KPO02',
                        'CR001',
                        'PT001'
                    ) AND lvh.AddDT < CAST(CAST(GETDATE() AS DATE) AS DATETIME)
                GROUP BY
                    lvh.LVHeaderKey,
                    lvt.MovementCode,
                    lvd.ValueCode,
                    lvt.LVMainKey,
                    lvd.LVNumber,
                    lvh.AddDT,
                    lvd.[ExpireDate]

                CREATE INDEX ix_temp_wallet_transaction_for_full_dump_migration_lv_header_key_id
                ON temp_wallet_transaction_for_full_dump_migration (lv_header_key, id);
            END;
        """

        loyalty_value_handler = MSSQLHandler(
            conn_id="loyalty_value_smc_db_connection_id"
        )
        loyalty_value_connection = loyalty_value_handler.hook.get_conn()

        try:
            logger.info(
                f"started preparing WalletTransaction temp table for migration..."
            )
            loyalty_value_handler.execute_query_string(
                connection=loyalty_value_connection,
                query_string=create_wallet_transaction_temp_table_query,
            )
            logger.info(
                f"finished preparing WalletTransaction temp table for migration."
            )
        finally:
            loyalty_value_connection.close()

    def get_count_query_string(self, is_full_dump: bool = True) -> str:
        """
        Generates a query string for counting total records for both full dump and
        incremental migration.

        Args:
            is_full_dump (bool): Migration type.

        Returns:
            str: A query string.
        """
        if not is_full_dump:
            return f"""
                SELECT
                    COUNT(*)
                FROM (
                    SELECT
                        lvh.LVHeaderKey
                    FROM
                        LVHeader lvh
                        JOIN LVTrans lvt ON lvt.LVHeaderKey = lvh.LVHeaderKey
                        JOIN LVData lvd ON lvd.LVMainKey = lvt.LVMainKey
                    WHERE
                        lvt.MovementCode IN (
                            'ADJ',
                            'ADJOUT',
                            'BD',
                            'BE',
                            'CB',
                            'CBO',
                            'CHG',
                            'CVIN',
                            'CVOUT',
                            'CVPE',
                            'EADJ',
                            'EADJOUT',
                            'EXP',
                            'ISS',
                            'JS-ADJIN',
                            'JS-ADJOUT',
                            'MIGRATE',
                            'POS',
                            'PTPOS',
                            'RD',
                            'RDG',
                            'STRET',
                            'UGC',
                            'UGCI',
                            'USE'
                        )
                        AND lvd.ValueCode IN (
                            'AP001',
                            'EP001',
                            'EP002',
                            'EP003',
                            'EP004',
                            'EP005',
                            'EP006',
                            'EP007',
                            'EP008',
                            'EP009',
                            'EP010',
                            'KPC01',
                            'KPO02',
                            'CR001',
                            'PT001'
                        ) AND {self.get_incremental_query_condition()}
                    GROUP BY
                        lvh.LVHeaderKey,
                        lvt.MovementCode,
                        lvd.ValueCode,
                        lvt.LVMainKey,
                        lvd.LVNumber,
                        lvh.AddDT,
                        lvd.[ExpireDate]
                ) AS total_rows;
            """

        return f"""
            SELECT COUNT(*)
            FROM temp_wallet_transaction_for_full_dump_migration;
        """

    def get_select_query_string(self, is_full_dump: str) -> str:
        """
        Generates a query string for selecting records from source for both full dump and
        incremental migration.

        Args:
            is_full_dump (bool): Migration type.

        Returns:
            str: A query string.
        """
        if not is_full_dump:
            return f"""
                SELECT
                    CAST(lvh.LVHeaderKey AS VARCHAR(20)) + '_' + CAST(lvt.MovementCode AS VARCHAR(10)) + '_' + CAST(lvd.ValueCode AS VARCHAR(10)) + '_' + CAST(lvt.LVMainKey AS VARCHAR(20)) AS id,
                    lvd.LVNumber AS member_id,
                    lvh.LVHeaderKey AS lv_header_key,
                    lvt.LVMainKey AS balance_id,
                    lvt.MovementCode AS movement_code,
                    lvd.ValueCode AS value_code,
                    SUM(lvt.Amount) AS amount,
                    CASE 
                        WHEN lvd.[ExpireDate] = CAST(lvd.[ExpireDate] AS DATE)
                        THEN DATEADD(
                            MILLISECOND,
                            61199999, 
                            CAST(lvd.[ExpireDate] AS DATETIME2(3))
                        )
                        ELSE DATEADD (HOUR, -7, lvd.[ExpireDate])
                    END AS expired_at,
                    DATEADD (HOUR, -7, lvh.AddDT) AS created_at
                FROM
                    LVHeader lvh
                    JOIN LVTrans lvt ON lvt.LVHeaderKey = lvh.LVHeaderKey
                    JOIN LVData lvd ON lvd.LVMainKey = lvt.LVMainKey
                WHERE
                    lvt.MovementCode IN (
                        'ADJ',
                        'ADJOUT',
                        'BD',
                        'BE',
                        'CB',
                        'CBO',
                        'CHG',
                        'CVIN',
                        'CVOUT',
                        'CVPE',
                        'EADJ',
                        'EADJOUT',
                        'EXP',
                        'ISS',
                        'JS-ADJIN',
                        'JS-ADJOUT',
                        'MIGRATE',
                        'POS',
                        'PTPOS',
                        'RD',
                        'RDG',
                        'STRET',
                        'UGC',
                        'UGCI',
                        'USE'
                    )
                    AND lvd.ValueCode IN (
                        'AP001',
                        'EP001',
                        'EP002',
                        'EP003',
                        'EP004',
                        'EP005',
                        'EP006',
                        'EP007',
                        'EP008',
                        'EP009',
                        'EP010',
                        'KPC01',
                        'KPO02',
                        'CR001',
                        'PT001'
                    ) AND {self.get_incremental_query_condition()}
                GROUP BY
                    lvh.LVHeaderKey,
                    lvt.MovementCode,
                    lvd.ValueCode,
                    lvt.LVMainKey,
                    lvd.LVNumber,
                    lvh.AddDT,
                    lvd.[ExpireDate]
                ORDER BY
                    lvh.LVHeaderKey,
                    lvt.MovementCode,
                    lvd.ValueCode,
                    lvt.LVMainKey
                OFFSET
                    %s ROWS
                FETCH NEXT
                    %s ROWS ONLY;
            """

        return f"""
            SELECT
                id,
                member_id,
                lv_header_key,
                balance_id,
                movement_code,
                value_code,
                amount,
                expired_at,
                created_at
            FROM temp_wallet_transaction_for_full_dump_migration
            ORDER BY
                lv_header_key,
                id
            OFFSET
                %s ROWS
            FETCH NEXT
                %s ROWS ONLY;
        """

    def get_nature_by_movement_code(self, cache: QueryCache, movement_code: str) -> str:
        query_str = "SELECT MovementCode, Nature FROM MAST_Movement"
        result = cache.get(query_str)

        if result is None:
            result = self.mssql_handler.extract_data(query_str)

            cache.set(query_str, dict(result))

        if movement_code not in result:
            return ""
        else:
            return "INCREASE" if result[movement_code] == 1 else "DECREASE"

    def transform_record(
        self,
        record: tuple,
        mast_movement_cache: QueryCache,
    ) -> tuple:
        """
        Transform a record queried from source table to destination table schema format.

        Args:
            record (tuple): A record queried from source table.

        Returns:
            tuple: A record in destination table schema format.
        """
        (
            id,
            member_id,
            lv_header_key,
            balance_id,
            movement_code,
            value_code,
            amount,
            expired_at,
            created_at,
        ) = record

        nature_type = self.get_nature_by_movement_code(
            mast_movement_cache, movement_code
        )
        wallet_code = self.wallet_mapping_file_mapper.get_value(value_code)[
            "WalletCode"
        ]
        type = self.wallet_activity_type_mapping_mapper.get_value(movement_code)[
            "GWL Wallet Activity Type Code"
        ]
        wallet_activity_id = f"{lv_header_key}_{type}_{wallet_code}_{member_id}"

        return (
            id,
            member_id,
            wallet_activity_id,
            balance_id,
            nature_type,
            wallet_code,
            amount,
            expired_at,
            created_at,
        )

    def insert_batch_to_destination(
        self,
        connection: postgres_connection,
        batch: list[tuple],
    ) -> None:
        """
        Insert a batch to destination table.

        Args:
            connection (postgres_connection): A PostgreSQL connection.
            batch (list): A list of  records to insert to destination table.

        Returns:
            None
        """
        self.postgresql_handler.execute_with_rollback(
            connection, self.destination_insert_query, batch
        )

    def process_batch(
        self,
        connection: postgres_connection,
        batch: list[tuple],
        batch_no: int,
        total_batches: int,
        mast_movement_cache: QueryCache,
        total_records: int,
        is_full_dump: bool,
    ) -> None:
        """
        Transform queried result from source table and insert them to a new table.

        Args:
            connection (postgres_connection): A PostgreSQL connection.
            batch (list): A batch to process.
            batch_no (int): The current batch's number, used only for logging.
            total_batches (int): The total number of batches to process, used only for logging.

        Returns:
            None
        """
        logger.info(
            f"started transforming and inserting batch {batch_no}/{total_batches} (size {len(batch)})..."
        )
        transformed_batch = [
            self.transform_record(
                record=record,
                mast_movement_cache=mast_movement_cache,
            )
            for record in batch
        ]
        self.insert_batch_to_destination(
            connection=connection,
            batch=transformed_batch,
        )
        logger.info(
            f"successfully transformed and inserted batch {batch_no}/{total_batches} (size {len(batch)})."
        )

        if is_full_dump:
            self.postgresql_handler.update_batch_tracker(
                connection=connection,
                service_name="point_service",
                table_name="WalletTransaction",
                total_records=total_records,
                batch_no=batch_no,
            )

    def migrate(
        self,
        count_query_string: str,
        select_query_string: str,
        is_full_dump: bool = True,
    ) -> None:
        """
        The main function for WalletTransaction migration flow.

        Args:
            None

        Returns:
            None
        """
        created_at = datetime.now()
        incremental_date = (
            None
            if is_full_dump
            else (
                self.incremental_query_date
                if self.incremental_query_date is not None
                else (datetime.today() - timedelta(days=1)).strftime("%Y-%m-%d")
            )
        )

        mssql_connection = self.mssql_handler.hook.get_conn()
        postgresql_connection = self.postgresql_handler.hook.get_conn()

        tracker = (
            self.postgresql_handler.get_latest_batch_info(
                connection=postgresql_connection,
                service_name="point_service",
                table_name="WalletTransaction",
            )
            if is_full_dump
            else None
        )

        if is_full_dump:
            self.prepare_wallet_transaction_temp_table()

        total_records = (
            self.mssql_handler.get_table_total_records(count_query_string)
            if tracker is None
            else tracker[0]
        )
        total_batches = math.ceil(total_records / self.batch_size)
        offsets = get_query_offsets(
            total_records=total_records,
            batch_size=self.batch_size,
            starting_offset=(
                0 if tracker is None else (tracker[1] - 1) * self.batch_size
            ),
        )
        completed_batches = tracker[2] if tracker is not None else []

        is_migration_succeeded = False

        try:
            futures = []
            mast_movement_cache = QueryCache(ttl_seconds=MAST_Movement_CACHE_TIME)

            batch_generator = self.mssql_handler.generate_batches(
                connection=mssql_connection,
                query_string=select_query_string,
                total_records=total_records,
                batch_size=self.batch_size,
                offsets=offsets,
                completed_batches=completed_batches,
            )

            with ThreadPoolExecutor(max_workers=self.executor_max_workers) as executor:
                semaphore = threading.Semaphore(self.executor_max_workers)

                while True:
                    semaphore.acquire()

                    try:
                        batch, batch_no = next(batch_generator)
                    except StopIteration:
                        break

                    future = executor.submit(
                        self.process_batch,
                        postgresql_connection,
                        batch,
                        batch_no,
                        total_batches,
                        mast_movement_cache,
                        total_records,
                        is_full_dump,
                    )
                    futures.append(future)
                    future.add_done_callback(lambda _: semaphore.release())

                for future in as_completed(futures):
                    future.result()

            logger.info(
                f"succesfully processed {total_records} records into WalletTransaction"
            )

            logger.info(f"started cleaning up batch tracker...")
            self.postgresql_handler.cleanup_batch_tracker(
                connection=postgresql_connection,
                service_name="point_service",
                table_name="WalletTransaction",
            )
            logger.info(f"finished cleaning up batch tracker.")

            is_migration_succeeded = True

        except Exception as error:
            logger.error(f"an error has occured: {error}")

            if postgresql_connection:
                logger.info("a postgresql connection is found, rolling back...")
                postgresql_connection.rollback()
                logger.info("successfully rolled back.")

            raise error
        finally:
            logger.info("started inserting migration result log...")
            if is_migration_succeeded:
                insert_migration_result(
                    postgresql_handler=self.postgresql_handler,
                    dag_name=f"point_service_{'full_dump' if is_full_dump else 'incremental'}_migration_wallet_transaction",
                    migration_type="FULL_DUMP" if is_full_dump else "INCREMENTAL",
                    source_table="LVHeader",
                    source_table_count=total_records,
                    destination_table="WalletTransaction",
                    destination_table_count=total_records,
                    validation_type="COMPLETENESS",
                    validation_result=100,
                    created_at=created_at,
                    incremental_date=incremental_date,
                )
            else:
                tracker = self.postgresql_handler.get_latest_batch_info(
                    connection=postgresql_connection,
                    service_name="point_service",
                    table_name="WalletTransaction",
                )

                destination_table_count = (
                    0
                    if tracker is None
                    else (
                        ((tracker[1] - 1) * self.batch_size)
                        if (tracker[1] - 1) * self.batch_size <= total_records
                        else total_records
                    )
                )

                total_processed = (
                    0 if tracker is None else len(tracker[2]) * self.batch_size
                )

                if tracker is not None and total_batches in tracker[2]:
                    total_processed -= self.batch_size + total_records % self.batch_size

                insert_migration_result(
                    postgresql_handler=self.postgresql_handler,
                    dag_name=f"point_service_{'full_dump' if is_full_dump else 'incremental'}_migration_wallet_transaction",
                    migration_type="FULL_DUMP" if is_full_dump else "INCREMENTAL",
                    source_table="LVHeader",
                    source_table_count=total_records,
                    destination_table="WalletTransaction",
                    destination_table_count=destination_table_count,
                    validation_type="COMPLETENESS",
                    validation_result=(
                        0 if tracker is None else total_processed / total_records * 100
                    ),
                    created_at=created_at,
                    incremental_date=incremental_date,
                )
            logger.info("finished inserting migration result log.")

            if mssql_connection:
                mssql_connection.close()
            if postgresql_connection:
                postgresql_connection.close()

    def migrate_full_dump(self) -> None:
        """
        The main function for WalletTransaction full dump migration flow.

        Args:
            None

        Returns:
            None
        """
        full_dump_count_query_string = self.get_count_query_string(is_full_dump=True)
        full_dump_select_query_string = self.get_select_query_string(is_full_dump=True)

        self.migrate(
            count_query_string=full_dump_count_query_string,
            select_query_string=full_dump_select_query_string,
            is_full_dump=True,
        )

    def migrate_incremental(self) -> None:
        """
        The main function for WalletTransaction incremental migration flow.

        Args:
            None

        Returns:
            None
        """
        incremental_count_query_string = self.get_count_query_string(is_full_dump=False)
        incremental_select_query_string = self.get_select_query_string(
            is_full_dump=False
        )

        self.migrate(
            count_query_string=incremental_count_query_string,
            select_query_string=incremental_select_query_string,
            is_full_dump=False,
        )
