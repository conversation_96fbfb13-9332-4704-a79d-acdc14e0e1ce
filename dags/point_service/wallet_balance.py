import math
import os
import threading
from concurrent.futures import as_completed, ThreadPoolExecutor
from datetime import datetime, timedelta

import pandas as pd
from psycopg2.extensions import connection as postgres_connection

from common_helpers.database_services import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PostgresHandler
from common_helpers.utils import get_query_offsets, insert_migration_result
from common_helpers.logging import get_logger

logger = get_logger()


class WalletBalance:
    def __init__(
        self,
        batch_size: int,
        executor_max_workers: int,
        mssql_handler: MSSQLHandler,
        postgresql_handler: PostgresHandler,
        incremental_query_date: str = None,
    ) -> None:
        self.batch_size = batch_size
        self.executor_max_workers = executor_max_workers
        self.incremental_query_date = incremental_query_date
        self.destination_insert_query = """
            INSERT INTO
                "point_service"."WalletBalance" (
                    "id",
                    "memberId",
                    "walletCode",
                    "expiredAt",
                    "amount",
                    "createdAt",
                    "updatedAt"
                )
            VALUES (%s, %s, %s, %s, %s, NOW () AT TIME ZONE 'UTC', NOW () AT TIME ZONE 'UTC')
            ON CONFLICT ("id") DO UPDATE SET amount = EXCLUDED.amount, "updatedAt" = EXCLUDED."updatedAt";
        """
        self.mssql_handler = mssql_handler
        self.postgresql_handler = postgresql_handler

    def get_wallet_code_mapping_case_statement(self):
        df = pd.read_csv(
            os.path.join(
                "dags",
                "data",
                "wallet_mapping.csv",
            )
        )

        result_dict = df.groupby("WalletCode")["Value Code"].apply(list).to_dict()

        case_statement = "CASE"

        for key, values in result_dict.items():
            values_str = ", ".join(f"'{value}'" for value in values)
            case_statement += f" WHEN ValueCode IN ({values_str}) THEN '{key}'"

        case_statement += " END"

        return case_statement

    def get_count_query_string(self) -> str:
        return """
                SELECT
                    COUNT(*)
                FROM (
                    SELECT
                        LVMainKey
                    FROM
                        snapshot_lv_data_28052025
                    WHERE
                        ValueCode IN (
                            'AP001',
                            'EP001',
                            'EP002',
                            'EP003',
                            'EP004',
                            'EP005',
                            'EP006',
                            'EP007',
                            'EP008',
                            'EP009',
                            'EP010',
                            'KPC01',
                            'KPO02',
                            'CR001',
                            'PT001'
                        )
                ) AS total_rows;
            """

    def get_select_query_string(self) -> str:
        wallet_code = self.get_wallet_code_mapping_case_statement()

        return f"""
                SELECT
                    LVMainKey AS id,
                    LVNumber AS member_id,
                    {wallet_code} AS wallet_code,
                    CASE 
                        WHEN [ExpireDate] = CAST([ExpireDate] AS DATE)
                        THEN DATEADD(
                            MILLISECOND,
                            61199999, 
                            CAST([ExpireDate] AS DATETIME2(3))
                        )
                        ELSE DATEADD (HOUR, -7, [ExpireDate])
                    END AS expired_at,
                    LVValue AS amount
                FROM
                    snapshot_lv_data_28052025
                WHERE
                    ValueCode IN (
                        'AP001',
                        'EP001',
                        'EP002',
                        'EP003',
                        'EP004',
                        'EP005',
                        'EP006',
                        'EP007',
                        'EP008',
                        'EP009',
                        'EP010',
                        'KPC01',
                        'KPO02',
                        'CR001',
                        'PT001'
                    )
                ORDER BY LVMainKey
                OFFSET
                    %s ROWS
                FETCH NEXT
                    %s ROWS ONLY;
            """

    def insert_batch_to_destination(
        self,
        connection: postgres_connection,
        batch: list[tuple],
    ) -> None:
        """
        Insert a batch to destination table.

        Args:
            connection (postgres_connection): A PostgreSQL connection.
            batch (list): A list of  records to insert to destination table.

        Returns:
            None
        """
        self.postgresql_handler.execute_with_rollback(
            connection, self.destination_insert_query, batch
        )

    def process_batch(
        self,
        connection: postgres_connection,
        batch: list[tuple],
        batch_no: int,
        total_batches: int,
        total_records: int,
    ) -> None:
        """
        Transform queried result from source table and insert them to a new table.

        Args:
            connection (postgres_connection): A PostgreSQL connection.
            batch (list): A batch to process.
            batch_no (int): The current batch's number, used only for logging.
            total_batches (int): The total number of batches to process, used only for logging.

        Returns:
            None
        """
        logger.info(
            f"started transforming and inserting batch {batch_no}/{total_batches} (size {len(batch)})..."
        )
        self.insert_batch_to_destination(
            connection=connection,
            batch=batch,
        )
        logger.info(
            f"successfully transformed and inserted batch {batch_no}/{total_batches} (size {len(batch)})."
        )
        self.postgresql_handler.update_batch_tracker(
            connection=connection,
            service_name="point_service",
            table_name="WalletBalance",
            total_records=total_records,
            batch_no=batch_no,
        )

    def migrate(
        self,
        count_query_string: str,
        select_query_string: str,
        is_full_dump: bool = True,
    ) -> None:
        """
        The main function for WalletBalance migration flow.

        Args:
            None

        Returns:
            None
        """
        created_at = datetime.now()
        incremental_date = None

        mssql_connection = self.mssql_handler.hook.get_conn()
        postgresql_connection = self.postgresql_handler.hook.get_conn()

        tracker = self.postgresql_handler.get_latest_batch_info(
            connection=postgresql_connection,
            service_name="point_service",
            table_name="WalletBalance",
        )

        total_records = (
            self.mssql_handler.get_table_total_records(count_query_string)
            if tracker is None
            else tracker[0]
        )
        total_batches = math.ceil(total_records / self.batch_size)
        offsets = get_query_offsets(
            total_records=total_records,
            batch_size=self.batch_size,
            starting_offset=(
                0 if tracker is None else (tracker[1] - 1) * self.batch_size
            ),
        )
        completed_batches = tracker[2] if tracker is not None else []

        is_migration_succeeded = False

        try:
            futures = []

            batch_generator = self.mssql_handler.generate_batches(
                connection=mssql_connection,
                query_string=select_query_string,
                total_records=total_records,
                batch_size=self.batch_size,
                offsets=offsets,
                completed_batches=completed_batches,
            )

            with ThreadPoolExecutor(max_workers=self.executor_max_workers) as executor:
                semaphore = threading.Semaphore(self.executor_max_workers)

                while True:
                    semaphore.acquire()

                    try:
                        batch, batch_no = next(batch_generator)
                    except StopIteration:
                        break

                    future = executor.submit(
                        self.process_batch,
                        postgresql_connection,
                        batch,
                        batch_no,
                        total_batches,
                        total_records,
                    )
                    futures.append(future)
                    future.add_done_callback(lambda _: semaphore.release())

                for future in as_completed(futures):
                    future.result()

            logger.info(
                f"succesfully processed {total_records} records into WalletBalance"
            )

            logger.info(f"started cleaning up batch tracker...")
            self.postgresql_handler.cleanup_batch_tracker(
                connection=postgresql_connection,
                service_name="point_service",
                table_name="WalletBalance",
            )
            logger.info(f"finished cleaning up batch tracker.")

            is_migration_succeeded = True

        except Exception as error:
            logger.error(f"an error has occured: {error}")

            if postgresql_connection:
                logger.info("a postgresql connection is found, rolling back...")
                postgresql_connection.rollback()
                logger.info("successfully rolled back.")

            raise error
        finally:
            logger.info("started inserting migration result log...")
            if is_migration_succeeded:
                insert_migration_result(
                    postgresql_handler=self.postgresql_handler,
                    dag_name=f"point_service_{'full_dump' if is_full_dump else 'incremental'}_migration_wallet_balance",
                    migration_type="FULL_DUMP" if is_full_dump else "INCREMENTAL",
                    source_table="LVData",
                    source_table_count=total_records,
                    destination_table="WalletBalance",
                    destination_table_count=total_records,
                    validation_type="COMPLETENESS",
                    validation_result=100,
                    created_at=created_at,
                    incremental_date=incremental_date,
                )
            else:
                tracker = self.postgresql_handler.get_latest_batch_info(
                    connection=postgresql_connection,
                    service_name="point_service",
                    table_name="WalletBalance",
                )

                destination_table_count = (
                    0
                    if tracker is None
                    else (
                        ((tracker[1] - 1) * self.batch_size)
                        if (tracker[1] - 1) * self.batch_size <= total_records
                        else total_records
                    )
                )

                total_processed = (
                    0 if tracker is None else len(tracker[2]) * self.batch_size
                )

                if tracker is not None and total_batches in tracker[2]:
                    total_processed -= self.batch_size + total_records % self.batch_size

                insert_migration_result(
                    postgresql_handler=self.postgresql_handler,
                    dag_name=f"point_service_{'full_dump' if is_full_dump else 'incremental'}_migration_wallet_balance",
                    migration_type="FULL_DUMP" if is_full_dump else "INCREMENTAL",
                    source_table="LVData",
                    source_table_count=total_records,
                    destination_table="WalletBalance",
                    destination_table_count=destination_table_count,
                    validation_type="COMPLETENESS",
                    validation_result=(
                        0 if tracker is None else total_processed / total_records * 100
                    ),
                    created_at=created_at,
                    incremental_date=incremental_date,
                )
            logger.info("finished inserting migration result log.")

            if mssql_connection:
                mssql_connection.close()
            if postgresql_connection:
                postgresql_connection.close()

    def migrate_full_dump(self) -> None:
        """
        The main function for WalletBalance full dump migration flow.

        Args:
            None

        Returns:
            None
        """
        full_dump_count_query_string = self.get_count_query_string()
        full_dump_select_query_string = self.get_select_query_string()

        self.migrate(
            count_query_string=full_dump_count_query_string,
            select_query_string=full_dump_select_query_string,
            is_full_dump=True,
        )

    def migrate_incremental(self) -> None:
        """
        The main function for WalletBalance incremental migration flow.

        Args:
            None

        Returns:
            None
        """
        incremental_count_query_string = self.get_count_query_string()
        incremental_select_query_string = self.get_select_query_string()

        self.migrate(
            count_query_string=incremental_count_query_string,
            select_query_string=incremental_select_query_string,
            is_full_dump=False,
        )

    def validate_migration(self) -> tuple[bool, float]:
        """
        Perform validation of the migration by checking record counts between source and destination.
        This is a simplified version that only validates the number of records.

        Returns:
            tuple[bool, float]: (is_valid, validation_percentage)
        """
        logger.info("Starting migration validation (record count only)...")

        mssql_connection = self.mssql_handler.hook.get_conn()
        postgresql_connection = self.postgresql_handler.hook.get_conn()

        try:
            # Count source records
            source_count_query = """
                SELECT COUNT(*)
                FROM snapshot_lv_data_28052025
                WHERE ValueCode IN (
                    'AP001', 'EP001', 'EP002', 'EP003', 'EP004',
                    'EP005', 'EP006', 'EP007', 'EP008', 'EP009',
                    'EP010', 'KPC01', 'KPO02', 'CR001', 'PT001'
                )
            """

            # Count destination records
            destination_count_query = """
                SELECT COUNT(*)
                FROM "point_service"."WalletBalance"
            """

            with mssql_connection.cursor() as cursor:
                cursor.execute(source_count_query)
                source_count = cursor.fetchone()[0]

            with postgresql_connection.cursor() as cursor:
                cursor.execute(destination_count_query)
                destination_count = cursor.fetchone()[0]

            # Validate counts
            is_valid = source_count == destination_count
            validation_percentage = 100 if is_valid else 0

            # Log results
            logger.info(f"Record count validation results:")
            logger.info(f"  Source count: {source_count}")
            logger.info(f"  Destination count: {destination_count}")
            logger.info(f"  Valid: {is_valid}")
            logger.info(f"  Validation percentage: {validation_percentage}%")

            if not is_valid:
                logger.error(
                    f"Record count mismatch: {abs(source_count - destination_count)} records difference"
                )

            return is_valid, validation_percentage

        except Exception as error:
            logger.error(f"Error during migration validation: {error}")
            return False, 0
        finally:
            if mssql_connection:
                mssql_connection.close()
            if postgresql_connection:
                postgresql_connection.close()
