CREATE TABLE IF NOT EXISTS staging_point_service."PreWalletBalanceTemp" (
	id text NOT NULL,
	"memberId" text NOT NULL,
	"walletCode" text NOT NULL,
	amount numeric(16, 2) DEFAULT 0.00 NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"expiredAt" timestamp(3) NULL,
	CONSTRAINT "PreWalletBalanceTemp_pkey" PRIMARY KEY (id)
);

WITH member_nocashwallet AS 
(
--self join but filter on walletcode= 'CASH_WALLET'
--if this is no member it will insert nothing
-- SELECT 
-- max(wb.id) as id,
-- (wb."memberId") as "memberId"

-- FROM staging_point_service."WalletBalance" wb
-- INNER JOIN staging_loyalty_service."Member" m on wb."memberId" = m."gwlNo"
-- LEFT JOIN staging_point_service."WalletBalance" wb_cash ON wb."memberId" = wb_cash."memberId" 
--     AND wb_cash."walletCode" = 'CASH_WALLET'
-- WHERE wb_cash."memberId" IS null
-- group by wb."memberId"
-- 5 seconds query
-- 454261 no cash wallet

-- Fixed: 2025-05-22
SELECT
    'DM' as id,
    m.id as "memberId"
from (
	select id
	from staging_loyalty_service."Member"
) m
left join (
	select "memberId"
	from staging_point_service."WalletBalance"
	where "walletCode" = 'CASH_WALLET'
) wb
on m.id = wb."memberId"
where wb."memberId" is null
group by m.id
)
--select count(*) from member_nocashwallet -- 454261 no cash wallet


-- This is the table to be inserted the new create CASH_WALLET in list of member_nocashwallet, then uinion all with the existing table(staging_point_service."WalletBalance" to insert to  staging_point_service."WalletBalanceTemp")
INSERT INTO staging_point_service."PreWalletBalanceTemp" (id, "memberId", "walletCode", amount, "createdAt", "updatedAt", "expiredAt")
SELECT
    concat(m.id,'_',m."memberId",'_','CASH_WALLET') as id,
    m."memberId",
    'CASH_WALLET',
    0.00,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    NULL
FROM member_nocashwallet AS m
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "walletCode" = EXCLUDED."walletCode",
    amount = EXCLUDED.amount,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "expiredAt" = EXCLUDED."expiredAt";
