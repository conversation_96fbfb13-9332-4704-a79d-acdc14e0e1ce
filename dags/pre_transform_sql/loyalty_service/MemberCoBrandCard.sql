INSERT into staging_loyalty_service."MemberCoBrandCard" (
   id,
   "memberId",
   "coBrandId",
   "cardNo",
   "memberCoBrandCardImportId",
   "status",
   "createdAt",
   "createdBy",
   "updatedAt",
   "updatedBy",
   "cardReason"
)
SELECT 
    id,
	"memberId",
	"cardTypeCode" as "coBrandId",
    case when length("embossNo") = 16 
		 then concat(substring("embossNo",1,8), 'xxxx', substring("embossNo",13,4))
		 else "embossNo"
		 end as "cardNo",
	'MIGRATION_COBRAND_IMPORT' as "memberCoBrandCardImportId",
    CASE WHEN "cardStatus" in ('ACTIVE', 'NEW CARD') THEN 'ACTIVE'
         WHEN "cardStatus" = 'Canceled Card' THEN 'INACTIVE'
         ELSE 'INACTIVE' END as "status",
	"createdAt",
    jsonb_build_object(
        'id', null, 
        'name', 'SYSTEM', 
        'email', null
    ) as "createdBy",
	"updatedAt",
    jsonb_build_object(
        'id', null, 
        'name', 'SYSTEM', 
        'email', null
    ) as "updatedBy",
    CASE WHEN "cardReason" in ('บัตรหมดอายุ (ยกเลิก)', 'ยกเลิกบัตรเครดิตร่วม KPG-SCB', 'บัตรเครดิตร่วมหมดอายุ (ยกเลิก)', 'ยกเลิกบัตรเครดิตร่วม KPG - KBANK') THEN 'CLOSED'
    	 WHEN "cardReason" in ('สมาชิกใหม่ (GOLD CARD)', 'สมาชิกบัตรเครดิตร่วม KPG-SCB') THEN 'NEW_CARD'
    	 WHEN "cardReason" = 'ออกบัตร CONDITION ผิด (ยกเลิก)' THEN 'CLOSED'
    	 when "cardReason" in ('ออกบัตรใหม่ (ต่ออายุสถานะภาพ)', 'ออกบัตรใหม่ (ทดแทน)', 'สมาชิกสมัครใหม่ (SILVER CARD)', 'ออกบัตรใหม่ (SPECIAL UPGRADE)', 'บัตรใหม่ (UPGRADE BY PROMOTION)') then 'RE_ISSUED_NEW_CARD_NO'
   	end as "cardReason"
FROM staging_loyalty_service."MemberLegacyCoBrandHistory" -- from MemberLegacyCoBrandHistory (other table)
WHERE TRIM(source_cardtypecode) = 'SCB'
ON CONFLICT (id) DO UPDATE SET
   "memberId" = EXCLUDED."memberId",
   "coBrandId" = EXCLUDED."coBrandId",
   "cardNo" = EXCLUDED."cardNo",
   "memberCoBrandCardImportId" = EXCLUDED."memberCoBrandCardImportId",
   "status" = EXCLUDED."status",
   "createdAt" = EXCLUDED."createdAt",
   "createdBy" = EXCLUDED."createdBy",
   "updatedAt" = EXCLUDED."updatedAt",
   "updatedBy" = EXCLUDED."updatedBy";
