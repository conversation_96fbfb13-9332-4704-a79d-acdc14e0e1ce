-- DROP TABLE IF EXISTS public.member_reason CASCADE

CREATE TABLE IF NOT EXISTS public.member_reason (
    "memberId" text not null PRIMARY KEY,
    reason text null DEFAULT NULL
);


WITH cardtypecode_cobrand AS (
    SELECT 'BVP05' AS cardTypeCode, 5 AS value
    UNION ALL
    SELECT 'SVP05', 5
    UNION ALL
    SELECT 'SVP10', 4
    UNION ALL
    SELECT 'BVS', 3
    UNION ALL
    SELECT 'SVP15', 3
    UNION ALL
    SELECT 'SVP20', 3
    UNION ALL
    SELECT 'STRIB', 2
    UNION ALL
    SELECT 'SPLD', 2
    UNION ALL
    SELECT 'SVP30', 1
)

-- ,
-- max_cardtypecode_cobrand as 
-- (SELECT  
-- 	"memberId"
-- 	,"cardTypeCode"
-- 	,value
-- FROM (select 
-- 		mlch."memberId"
-- 		,mlch."cardTypeCode"
-- 		,cc.value
-- 		,ROW_NUMBER() OVER (PARTITION BY mlch."memberId" ORDER BY cc.value DESC) AS rn
-- 		from
-- 		staging_loyalty_service."MemberLegacyCoBrandHistory" as mlch
-- 		LEFT JOIN
-- 	    cardtypecode_cobrand AS cc ON mlch."cardTypeCode" = cc.cardTypeCode
-- 		)
-- WHERE  	rn = 1
-- 	)
--select * from max_cardtypecode_cobrand
,
cardtypecode_tier as
(SELECT 'N_NY' AS cardTypeCode, 10 AS value
    UNION ALL
    SELECT 'P_CR', 10
    UNION ALL
    SELECT 'N_SL', 9
    UNION ALL
    SELECT 'OV_SL', 9
    UNION ALL
    SELECT 'OT_SL', 9
    UNION ALL
    SELECT 'N_CR', 8
    UNION ALL
    SELECT 'N_OX', 7
    UNION ALL
    SELECT 'OT_OX', 7
    UNION ALL
    SELECT 'OV_OX', 7
    UNION ALL
    SELECT 'IN_V1', 6
    UNION ALL
    SELECT 'IN_V2', 5
    UNION ALL
    SELECT 'IN_V3', 5
    UNION ALL
    SELECT 'IN_K', 4
    UNION ALL
    SELECT 'VEGA', 3
    UNION ALL
    SELECT 'NVVIP', 2
    UNION ALL
    SELECT 'C_STF', 1
    UNION ALL
    SELECT 'CS_MS', 1
    )
,
max_cardtypecode_tier as

(SELECT 
	"memberId",
    "cardTypeCode",
    value
FROM 	(select
		mlth."memberId"
		,mlth."cardTypeCode"
		,ct.value
		,ROW_NUMBER() OVER (PARTITION BY mlth."memberId" ORDER BY ct.value DESC) AS rn
	
		FROM staging_loyalty_service."MemberLegacyTierHistory" as mlth
		LEFT JOIN
	    cardtypecode_tier AS ct ON mlth."cardTypeCode" = ct.cardTypeCode
		)
WHERE rn = 1
)
--select * from max_cardtypecode_tier

,
member_data as (
SELECT
    t."memberId"
    ,t."isActive"
    ,case when susp."memberId" is not null then true else false end AS is_blacklisted
	,case when mexp."memberId" is not null then true else false end AS is_expired_notmigrate
    ,case when kbank."memberId" is not null then true else false end as has_kbank
    ,case when other_bank."memberId" is not null then true else false end as has_other_cobrand
	,case when mct."memberId" is null then true else false end AS not_lv
FROM
    public.tiertransformed t
LEFT JOIN
    public.members_blacklist susp ON t."memberId" = susp."memberId"
LEFT JOIN
    public.members_expired_notmigrate mexp ON t."memberId" = mexp."memberId"
LEFT JOIN (
    select "memberId"
	from staging_loyalty_service."MemberLegacyCoBrandHistory"
	where "cardTypeCode" in ('BVP05', 'BVP10', 'BVP15', 'BVP20', 'BVS')
	group by "memberId"
) kbank
ON t."memberId" = kbank."memberId"
LEFT JOIN (
    select "memberId"
	from staging_loyalty_service."MemberLegacyCoBrandHistory"
	where "cardTypeCode" not in ('BVP05', 'BVP10', 'BVP15', 'BVP20', 'BVS')
        and "cardStatus" = 'ACTIVE'
	group by "memberId"
) other_bank
ON t."memberId" = other_bank."memberId"
LEFT JOIN
    max_cardtypecode_tier mct ON t."memberId" = mct."memberId"
)

--select * from member_data limit 100
,
label_reason AS (
    SELECT
        "memberId",
        CASE
            WHEN ("isActive" = FALSE AND is_expired_notmigrate ) THEN 'NOT MIGRATE' --1
            WHEN (has_kbank AND NOT has_other_cobrand AND not_lv) THEN 'NOT MIGRATE' --2
            WHEN is_blacklisted THEN 'Suspended' --3
            WHEN "isActive" = FALSE THEN 'SMC Expired' --4
            ELSE NULL --5
        END AS reason
    FROM member_data
)
--select * from label_reason limit 10

INSERT INTO public.member_reason ("memberId", reason)
SELECT * FROM label_reason
ON CONFLICT ("memberId") DO UPDATE set
reason = EXCLUDED.reason