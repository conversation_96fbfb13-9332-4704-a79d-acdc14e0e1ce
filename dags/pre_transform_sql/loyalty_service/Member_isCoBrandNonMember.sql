-- DROP TABLE IF EXISTS public.member_iscobrandnonmember CASCADE

CREATE TABLE IF NOT EXISTS public.member_iscobrandnonmember (
    "memberId" text not null PRIMARY KEY,
    "isCoBrandNonMember" boolean null
);

with iscobrandnonmember as
(
SELECT 
	t."memberId" 
    ,CASE 
			WHEN MAX(CASE WHEN mlth."cardStatus" = 'ACTIVE' THEN 1 ELSE 0 END) = 1 THEN false
			WHEN MAX(CASE WHEN mlch."cardStatus" = 'ACTIVE' AND TRIM(mlch.source_cardtypecode) = 'SCB' THEN 1 ELSE 0 END) = 1 THEN true
			ELSE false
			END AS "isCoBrandNonMember"

FROM public.tiertransformed t
INNER JOIN public.member_reason as member_reason ON t."memberId"= member_reason."memberId"
LEFT JOIN staging_loyalty_service."MemberLegacyTierHistory" mlth ON mlth."memberId" = t."memberId" AND mlth."cardStatus" = 'ACTIVE'
LEFT JOIN staging_loyalty_service."MemberLegacyCoBrandHistory" mlch ON mlch."memberId" = t."memberId" AND mlch."cardStatus" = 'ACTIVE'
WHERE (trim(lower(member_reason.reason)) <> 'not migrate' or (member_reason.reason is null))
--WHERE mlth."cardStatus" = 'ACTIVE' OR mlch."cardStatus" = 'ACTIVE'
group by t."memberId"
order by t."memberId"
)

INSERT INTO public.member_iscobrandnonmember ("memberId", "isCoBrandNonMember")
SELECT * FROM iscobrandnonmember
ON CONFLICT ("memberId") DO UPDATE set
"isCoBrandNonMember" = EXCLUDED."isCoBrandNonMember"