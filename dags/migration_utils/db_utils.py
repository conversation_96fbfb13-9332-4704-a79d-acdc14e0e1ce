from urllib.parse import urlparse
from airflow.providers.postgres.hooks.postgres import PostgresHook
from airflow.hooks.base import BaseHook

def convert_pg_connection_string(pg_url):
    parsed_url = urlparse(pg_url)
    return (f"host={parsed_url.hostname} dbname={parsed_url.path.lstrip('/')} "
            f"user={parsed_url.username} password={parsed_url.password}")

def get_total_count(postgres_hook, dblink_conn, table_name):
    '''Get total count of records in source table'''
    count_query = f'''
    SELECT * FROM dblink('{dblink_conn}', 
        'SELECT COUNT(*) FROM {table_name}'
    ) AS count_result(count bigint)
    '''
    result = postgres_hook.get_first(count_query)
    return result[0] if result else 0

def get_total_count_stg(postgres_hook, table_name):
    count_query = f'''SELECT COUNT(*) FROM staging_{table_name}'''
    result = postgres_hook.get_first(count_query)
    return result[0] if result else 0

def get_total_count_gwl(postgres_hook, table_name):
    count_query = f'''SELECT COUNT(*) FROM {table_name}'''
    result = postgres_hook.get_first(count_query)
    return result[0] if result else 0

def get_p_key(table_name):
    schema_name_sql = table_name.split('.')[0]
    table_name_sql = table_name.split('.')[-1].replace("\"", '').lower()
    try:
        postgres_hook = PostgresHook(postgres_conn_id='temp_db_conn')
        conn = postgres_hook.get_conn()
        conn.set_session(autocommit=True)
        cursor = conn.cursor()
        
        cursor.execute(f'''
                select 
                    key_column as p_key
                    from
                        (select kcu.table_schema,
                            kcu.table_name,
                            tco.constraint_name,
                            kcu.ordinal_position as position,
                            kcu.column_name as key_column
                        from information_schema.table_constraints tco
                        join information_schema.key_column_usage kcu 
                            on kcu.constraint_name = tco.constraint_name
                            and kcu.constraint_schema = tco.constraint_schema
                            and kcu.constraint_name = tco.constraint_name
                        where tco.constraint_type = 'PRIMARY KEY'
                        order by kcu.table_schema,
                                kcu.table_name,
                                position
                        ) as pkey
                where table_schema = '{schema_name_sql}' and LOWER(table_name) = '{table_name_sql}'
            ''')
        pk_columns = [row[0] for row in cursor.fetchall()]

        if not pk_columns:
            order_by_clause = ""
        else:
            pk_columns_str = ', '.join(f'"{col}"' for col in pk_columns)
            order_by_clause = f" ORDER BY {pk_columns_str}"

        return order_by_clause
    except Exception as e:
        raise Exception(f"Failed to get primary key for table {table_name}. Error: {str(e)}")

def get_p_key_stg(table_name):
    schema_name_sql = table_name.split('.')[0]
    table_name_sql = table_name.split('.')[-1].replace("\"", '').lower()
    try:
        postgres_hook = PostgresHook(postgres_conn_id='gwl_db_conn')
        conn = postgres_hook.get_conn()
        conn.set_session(autocommit=True)
        cursor = conn.cursor()
        
        cursor.execute(f'''
                select 
                    key_column as p_key
                    from
                        (select kcu.table_schema,
                            kcu.table_name,
                            tco.constraint_name,
                            kcu.ordinal_position as position,
                            kcu.column_name as key_column
                        from information_schema.table_constraints tco
                        join information_schema.key_column_usage kcu 
                            on kcu.constraint_name = tco.constraint_name
                            and kcu.constraint_schema = tco.constraint_schema
                            and kcu.constraint_name = tco.constraint_name
                        where tco.constraint_type = 'PRIMARY KEY'
                        order by kcu.table_schema,
                                kcu.table_name,
                                position
                        ) as pkey
                where table_schema = 'staging_{schema_name_sql}' and LOWER(table_name) = '{table_name_sql}'
            ''')
        pk_columns = [row[0] for row in cursor.fetchall()]

        if not pk_columns:
            order_by_clause = ""
        else:
            pk_columns_str = ', '.join(f'"{col}"' for col in pk_columns)
            order_by_clause = f" ORDER BY {pk_columns_str}"

        return order_by_clause
    except Exception as e:
        raise Exception(f"Failed to get primary key for table staging_{table_name}. Error: {str(e)}") 
    






def connection_db_with_psycopg2_via_postgres_hook():

    postgres_hook = PostgresHook(postgres_conn_id= 'gwl_db_conn')  # Replace with your connection ID
    print('postgres_hook successfully connected')

    try:
        conn_params = postgres_hook.get_connection('gwl_db_conn')
        print(conn_params)

        conn = psycopg2.connect(
            host=conn_params.host,
            port=conn_params.port,
            dbname=conn_params.schema,
            user=conn_params.login,
            password=conn_params.password
        )
        cursor = conn.cursor()

        # Execute your psycopg2 queries here
        cursor.execute("SELECT count(*) FROM staging_loyalty_service.member_11;")
        results = cursor.fetchall()
        print(results)

        cursor.close()
        conn.close()

    except Exception as e:
        print(f"Error connecting or querying with psycopg2: {e}")