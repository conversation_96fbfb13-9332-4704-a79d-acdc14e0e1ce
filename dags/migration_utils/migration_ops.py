import os
from airflow.providers.postgres.hooks.postgres import PostgresHook
from airflow.hooks.base import BaseHook
from .db_utils import convert_pg_connection_string, get_total_count, get_total_count_stg, get_total_count_gwl
from .sql_utils import (get_table_filepath_crossdb, get_table_filepath_pre_transform, 
                       get_table_filepath_interdb, read_sql_file_crossdb, read_sql_file_interdb,
                       read_sql_file_crossdb_dailysync, get_table_filepath_remove_dailysync,
                       read_sql_file_crossdb_dailysync_for_delete, get_table_filepath_pre_transform_dailysync,
                       read_sql_file_interdb_dailysync, get_table_filepath_cross_gwl_uat,
                       read_sql_file_crossdb_dailysync_for_incremental_count,
                       get_table_filepath_remove_seperatedservice_dailysync,
                       read_sql_file_crossdb_dailysync_seperatedservice_for_delete)
import logging
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def cleanup_old_staging_data(table_name, days_to_keep=30):
    """Clean up old staging data based on sync_timestamp"""
    try:
        postgres_hook = PostgresHook(postgres_conn_id='gwl_db_conn')
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        
        # Delete old records
        delete_query = f"""
            DELETE FROM {table_name}
            WHERE sync_timestamp < '{cutoff_date}'
        """
        deleted_count = postgres_hook.run(delete_query, with_rowcount=True)
        logger.info(f"Cleaned up {deleted_count} old records from {table_name}")
        
        # Vacuum the table to reclaim space
        postgres_hook.run(f"VACUUM ANALYZE {table_name}")
        
    except Exception as e:
        logger.error(f"Error cleaning up old data from {table_name}: {str(e)}")
        raise

def add_sync_timestamp_column(table_name):
    """Add sync_timestamp column if it doesn't exist"""
    try:
        postgres_hook = PostgresHook(postgres_conn_id='gwl_db_conn')
        postgres_hook.run(f"""
            DO $$ 
            BEGIN
                IF NOT EXISTS (
                    SELECT 1 
                    FROM information_schema.columns 
                    WHERE table_name = '{table_name}' 
                    AND column_name = 'sync_timestamp'
                ) THEN
                    ALTER TABLE {table_name} 
                    ADD COLUMN sync_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
                END IF;
            END $$;
        """)
        logger.info(f"Ensured sync_timestamp column exists in {table_name}")
    except Exception as e:
        logger.error(f"Error adding sync_timestamp column to {table_name}: {str(e)}")
        raise

def fetch_insert_crossdb_with_dblink(table_name, is_incremental=False):
    '''Process a single table with chunking'''
    conn = BaseHook.get_connection('temp_db_conn')
    temp_db_dblink = convert_pg_connection_string(conn.get_uri())
    sql_filepath = get_table_filepath_crossdb(table_name)
    
    try:
        postgres_hook = PostgresHook(postgres_conn_id='gwl_db_conn')
        
        # Get total count first
        total_count = get_total_count(postgres_hook, temp_db_dblink, table_name.replace('"MemberLegacyCoBrandHistory"', '"MemberLegacyCobrandHistory"'))
        if total_count == 0:
            logger.warning(f"No data found in source table {table_name}")
            return
            
        logger.info(f"Processing {table_name} with {total_count} total records")
        
        # Process in chunks
        # chunk_size = 800000
        chunk_size = total_count
        total_chunks = (total_count + chunk_size - 1) // chunk_size
        
        for chunk_num in range(total_chunks):
            offset = chunk_num * chunk_size
            remaining = total_count - offset
            current_chunk = min(chunk_size, remaining)
            
            try:
                # Get SQL with proper chunk parameters
                chunk_query = read_sql_file_crossdb(sql_filepath, table_name, temp_db_dblink, current_chunk, offset)
                
                # Modify query for incremental loading if needed
                if is_incremental:
                    staging_table = f'''staging_{table_name.split('.')[-1].replace('"', '')}'''
                    # Modify the SELECT part to include incremental logic
                    select_part = chunk_query.split("FROM")[0]
                    from_part = chunk_query.split("FROM")[1]
                    
                    # Add incremental condition to the SELECT
                    chunk_query = f"""
                    {select_part}
                    FROM (
                        SELECT t1.* 
                        FROM dblink('{temp_db_dblink}', 'SELECT * FROM {table_name}') AS t1
                        WHERE NOT EXISTS (
                            SELECT 1 FROM {staging_table} stg 
                            WHERE stg.id = t1.id 
                            AND stg.sync_timestamp > CURRENT_TIMESTAMP - INTERVAL '1 day'
                        )
                    ) t1
                    {from_part.split("AS t1")[1]}
                    """
                    
                    # Log the incremental query for debugging
                    logger.debug(f"Incremental query for {table_name}: {chunk_query}")
                
                logger.info(f"Processing chunk {chunk_num + 1}/{total_chunks} for {table_name}")
                logger.info(f"Inserting {current_chunk} rows starting from offset {offset}")
                
                # Execute chunk insert
                postgres_hook.run(chunk_query)
                
                processed = offset + current_chunk
                logger.info(f"Progress: {processed}/{total_count} rows ({(processed/total_count)*100:.2f}%)")
                
            except Exception as e:
                logger.error(f"Error processing chunk {chunk_num + 1} for {table_name}: {str(e)}")
                raise
            
        logger.info(f"Successfully processed all {total_count} rows for {table_name}")
        logger.info(f"target table count: {postgres_hook.get_first(f'''SELECT COUNT(*) FROM {table_name}''')[0]}")
        
        # Clean up old staging data after successful processing
        # if is_incremental:
        #     cleanup_old_staging_data(staging_table)
        
    except Exception as e:
        logger.error(f"Error processing table {table_name}: {str(e)}")
        raise


def fetch_insert_crossdb_with_dblink_dailysync(table_name, is_incremental=True, **kwargs):
    '''Process a single table with chunking with in start_ts and end_ts'''
    conn = BaseHook.get_connection('temp_db_conn')
    temp_db_dblink = convert_pg_connection_string(conn.get_uri())
    sql_filepath = get_table_filepath_crossdb(table_name)
    
    try:
        postgres_hook = PostgresHook(postgres_conn_id='gwl_db_conn')
        
        # Get total count first
        total_count = get_total_count(postgres_hook, temp_db_dblink, table_name.replace('"MemberLegacyCoBrandHistory"', '"MemberLegacyCobrandHistory"'))
        if total_count == 0:
            logger.warning(f"No data found in source table {table_name}")
            return
            
        logger.info(f"Processing {table_name} with {total_count} total records but this is not incremental count")
        
        # Process in chunks
        # chunk_size = 800000
        chunk_size = total_count
        total_chunks = (total_count + chunk_size - 1) // chunk_size
        
        for chunk_num in range(total_chunks):
            offset = chunk_num * chunk_size
            remaining = total_count - offset
            current_chunk = min(chunk_size, remaining)
            
            try:
                # count for the incremental process
                incremental_count_sql = read_sql_file_crossdb_dailysync_for_incremental_count(sql_filepath, table_name, temp_db_dblink, current_chunk, offset, **kwargs, is_incremental=True)
                incremental_count = postgres_hook.get_first(incremental_count_sql)
                logger.info(f"incremental Processing {table_name} with {incremental_count} incremental records")

                # Get SQL with proper chunk parameters
                chunk_query = read_sql_file_crossdb_dailysync(sql_filepath, table_name, temp_db_dblink, current_chunk, offset, **kwargs)
                
                logger.info(f"Processing chunk {chunk_num + 1}/{total_chunks} for {table_name}")
                logger.info(f"Inserting {current_chunk} rows starting from offset {offset}")
                # Execute chunk insert
                postgres_hook.run(chunk_query)
                
                processed = offset + current_chunk
                logger.info(f"Progress: {processed}/{total_count} rows ({(processed/total_count)*100:.2f}%)")
                
            except Exception as e:
                logger.error(f"Error processing chunk {chunk_num + 1} for {table_name}: {str(e)}")
                raise
            
        logger.info(f"Successfully processed all {total_count} rows for {table_name}")
        logger.info(f"target table count: {postgres_hook.get_first(f'''SELECT COUNT(*) FROM {table_name}''')[0]}")
        
        # Clean up old staging data after successful processing
        # if is_incremental:
        #     cleanup_old_staging_data(staging_table)
        
    except Exception as e:
        logger.error(f"Error processing table {table_name}: {str(e)}")
        raise






def fetch_detect_remove_crossdb_dailysync(table_name, **kwargs):
    '''fethch from cross db and detect remove member from daily sync update'''
    conn = BaseHook.get_connection('temp_db_conn')
    temp_db_dblink = convert_pg_connection_string(conn.get_uri())
    sql_filepath = get_table_filepath_remove_dailysync(table_name)
    
    try:
        postgres_hook = PostgresHook(postgres_conn_id='gwl_db_conn')
        
        # Get total count first
        total_count = get_total_count(postgres_hook, temp_db_dblink, table_name.replace('"MemberLegacyCoBrandHistory"', '"MemberLegacyCobrandHistory"'))
        if total_count == 0:
            logger.warning(f"No data found in source table {table_name}")
            return
            
        logger.info(f"Processing delete at {table_name} from list of member remove starting with {total_count} total records")
        
        # Process in chunks
        # chunk_size = 800000
        chunk_size = total_count
        total_chunks = (total_count + chunk_size - 1) // chunk_size
        
        for chunk_num in range(total_chunks):
            offset = chunk_num * chunk_size
            remaining = total_count - offset
            current_chunk = min(chunk_size, remaining)
            
            try:
                # Get SQL with proper chunk parameters
                chunk_query = read_sql_file_crossdb_dailysync_for_delete(sql_filepath, table_name, temp_db_dblink, current_chunk, offset, **kwargs)

                # Extract the transformation column clause from the original SQL
                start_index = chunk_query.find("WITH")
                end_index = chunk_query.find("ON CONFLICT")
                selected_rows_to_delete = chunk_query[start_index:end_index].strip()

                chunk_query = f'''
                {selected_rows_to_delete}
                '''
                
                logger.info(f"Processing chunk {chunk_num + 1}/{total_chunks} for {table_name}")
                logger.info(f"Inserting {current_chunk} rows starting from offset {offset}")
                
                # Execute chunk insert
                postgres_hook.run(chunk_query)


                
                processed = offset + current_chunk
                logger.info(f"Progress: {processed}/{total_count} rows ({(processed/total_count)*100:.2f}%)")
                
            except Exception as e:
                logger.error(f"Error processing chunk {chunk_num + 1} for {table_name}: {str(e)}")
                raise
            
        logger.info(f"Successfully processed all for {table_name}")
        logger.info(f"target table count after processed: {postgres_hook.get_first(f'''SELECT COUNT(*) FROM {table_name}''')[0]}")
        
        # Clean up old staging data after successful processing
        # if is_incremental:
        #     cleanup_old_staging_data(staging_table)
        
    except Exception as e:
        logger.error(f"Error processing table {table_name}: {str(e)}")
        raise

def fetch_detect_remove_crossdb_dailysync_gwl_prod(table_name, prod_service_conn_name,  **kwargs):
    '''fethch from cross db and detect remove member from daily sync update'''
    conn = BaseHook.get_connection('temp_db_conn')
    temp_db_dblink = convert_pg_connection_string(conn.get_uri())
    conn = BaseHook.get_connection('gwl_db_conn')
    gwl_db_dblink = convert_pg_connection_string(conn.get_uri())
    sql_filepath = get_table_filepath_remove_seperatedservice_dailysync(table_name)
    
    try:
        postgres_hook = PostgresHook(postgres_conn_id=prod_service_conn_name)
        
        # Get total count first
        total_count = get_total_count(postgres_hook, temp_db_dblink, table_name.replace('"MemberLegacyCoBrandHistory"', '"MemberLegacyCobrandHistory"'))
        if total_count == 0:
            logger.warning(f"No data found in source table {table_name}")
            return
            
        logger.info(f"Processing delete at {table_name} from list of member remove starting with {total_count} total records")
        
        # Process in chunks
        # chunk_size = 800000
        chunk_size = total_count
        total_chunks = (total_count + chunk_size - 1) // chunk_size
        
        for chunk_num in range(total_chunks):
            offset = chunk_num * chunk_size
            remaining = total_count - offset
            current_chunk = min(chunk_size, remaining)
            
            try:
                # Get SQL with proper chunk parameters
                chunk_query = read_sql_file_crossdb_dailysync_seperatedservice_for_delete(sql_filepath, table_name, temp_db_dblink, gwl_db_dblink, current_chunk, offset, **kwargs)

                # Extract the transformation column clause from the original SQL
                start_index = chunk_query.find("WITH")
                end_index = chunk_query.find("ON CONFLICT")
                selected_rows_to_delete = chunk_query[start_index:end_index].strip()

                chunk_query = f'''
                {selected_rows_to_delete}
                '''
                
                logger.info(f"Processing chunk {chunk_num + 1}/{total_chunks} for {table_name}")
                # logger.info(f"Inserting {current_chunk} rows starting from offset {offset}")
                
                # Execute chunk insert
                postgres_hook.run(chunk_query)


                
                processed = offset + current_chunk
                logger.info(f"Progress: {processed}/{total_count} rows ({(processed/total_count)*100:.2f}%)")
                
            except Exception as e:
                logger.error(f"Error processing chunk {chunk_num + 1} for {table_name}: {str(e)}")
                raise
          
        target_table_name = table_name.replace('loyalty_service', 'public').replace('engagement_service', 'public').replace('partner_service', 'public').replace('point_service', 'public')
        logger.info(f"Successfully processed all for {target_table_name}")
        logger.info(f"target table count after processed: {postgres_hook.get_first(f'''SELECT COUNT(*) FROM {target_table_name}''')[0]}")
        
        # Clean up old staging data after successful processing
        # if is_incremental:
        #     cleanup_old_staging_data(staging_table)
        
    except Exception as e:
        logger.error(f"Error processing table {table_name}: {str(e)}")
        raise

def fetch_transform_insert_interdb(table_name):
    sql_filepath = get_table_filepath_interdb(table_name)
    print(sql_filepath)
    
    try:
        postgres_hook = PostgresHook(postgres_conn_id='gwl_db_conn')
        
        # Get total count first
        total_count = get_total_count_stg(postgres_hook, table_name)
        if total_count == 0:
            print(f"No data found in source table staging_{table_name}")
            # return
            
        print(f"Processing staging_{table_name} to target table with {total_count} total records")
        
        # Process in chunks
        # chunk_size = 1000000
        chunk_size = total_count
        if total_count == 0:
            total_chunks = 1
        else:
            total_chunks = (total_count + chunk_size - 1) // chunk_size
        
        for chunk_num in range(total_chunks):
            offset = chunk_num * chunk_size
            remaining = total_count - offset
            current_chunk = min(chunk_size, remaining)
            
            try:
                # Get SQL with proper chunk parameters
                chunk_query = read_sql_file_interdb(sql_filepath, table_name, current_chunk, offset)
                print(f"Processing chunk {chunk_num + 1}/{total_chunks} for staging_{table_name}")
                print(f"Inserting {current_chunk} rows starting from offset {offset}")
                
                # Execute chunk insert
                postgres_hook.run(chunk_query)
                
                processed = offset + current_chunk
                print(f"Progress: {processed}/{total_count} rows ({(processed/total_count)*100:.2f}%)" if total_count != 0 else "Progress: 100%")
                
            except Exception as e:
                print(f"Error processing chunk {chunk_num + 1} for staging_{table_name}: {str(e)}")
                raise
            
        print(f"Successfully processed all {total_count} rows for staging_{table_name}")
        print(f"gwl target table {table_name} count: {get_total_count_gwl(postgres_hook, table_name)} rows")
    except Exception as e:
        print(f"Error processing table staging_{table_name}: {str(e)}")
        raise

def fetch_transform_insert_cross_gwl_uat(table_name, uat_service_conn_name, is_incremental=False):
    conn = BaseHook.get_connection('gwl_db_conn')
    gwl_db_dblink = convert_pg_connection_string(conn.get_uri())
    sql_filepath = get_table_filepath_cross_gwl_uat(table_name)
    
    try:
        postgres_hook = PostgresHook(postgres_conn_id=uat_service_conn_name)
        
        # Get total count first
        total_count = get_total_count(postgres_hook, gwl_db_dblink, table_name.replace('"MemberLegacyCoBrandHistory"', '"MemberLegacyCobrandHistory"'))
        if total_count == 0:
            logger.warning(f"No data found in source table {table_name}")
            return
            
        logger.info(f"Processing {table_name} with {total_count} total records")
        
        # Process in chunks
        # chunk_size = 800000
        chunk_size = total_count
        total_chunks = (total_count + chunk_size - 1) // chunk_size
        
        for chunk_num in range(total_chunks):
            offset = chunk_num * chunk_size
            remaining = total_count - offset
            current_chunk = min(chunk_size, remaining)
            
            try:
                # Get SQL with proper chunk parameters
                chunk_query = read_sql_file_crossdb(sql_filepath, table_name, gwl_db_dblink, current_chunk, offset)
                
                # Modify query for incremental loading if needed
                if is_incremental:
                    staging_table = f'''{table_name.split('.')[-1].replace('"', '')}'''
                    # Modify the SELECT part to include incremental logic
                    select_part = chunk_query.split("FROM")[0]
                    from_part = chunk_query.split("FROM")[1]
                    
                    # Add incremental condition to the SELECT
                    chunk_query = f"""
                    {select_part}
                    FROM (
                        SELECT t1.* 
                        FROM dblink('{gwl_db_dblink}', 'SELECT * FROM {table_name}') AS t1
                        WHERE NOT EXISTS (
                            SELECT 1 FROM {staging_table} stg 
                            WHERE stg.id = t1.id 
                            AND stg.sync_timestamp > CURRENT_TIMESTAMP - INTERVAL '1 day'
                        )
                    ) t1
                    {from_part.split("AS t1")[1]}
                    """
                    
                    # Log the incremental query for debugging
                    logger.debug(f"Incremental query for {table_name}: {chunk_query}")
                
                logger.info(f"Processing chunk {chunk_num + 1}/{total_chunks} for {table_name}")
                logger.info(f"Inserting {current_chunk} rows starting from offset {offset}")
                
                # Execute chunk insert
                postgres_hook.run(chunk_query)
                
                processed = offset + current_chunk
                logger.info(f"Progress: {processed}/{total_count} rows ({(processed/total_count)*100:.2f}%)")
                
            except Exception as e:
                logger.error(f"Error processing chunk {chunk_num + 1} for {table_name}: {str(e)}")
                raise
        table_name_for_count = table_name.split('.')[-1]    
        logger.info(f"Successfully processed all {total_count} rows for {table_name}")
        logger.info(f"target table count: {postgres_hook.get_first(f'''SELECT COUNT(*) FROM {table_name_for_count}''')[0]}")
        
        # Clean up old staging data after successful processing
        # if is_incremental:
        #     cleanup_old_staging_data(staging_table)
        
    except Exception as e:
        logger.error(f"Error processing table {table_name}: {str(e)}")
        raise



def fetch_transform_insert_cross_gwl_prod(table_name, prod_service_conn_name, is_incremental=False):
    conn = BaseHook.get_connection('gwl_db_conn')
    gwl_db_dblink = convert_pg_connection_string(conn.get_uri())
    sql_filepath = get_table_filepath_cross_gwl_uat(table_name)
    
    try:
        postgres_hook = PostgresHook(postgres_conn_id=prod_service_conn_name)
        
        # Get total count first
        total_count = get_total_count(postgres_hook, gwl_db_dblink, table_name)
        if total_count == 0:
            logger.warning(f"No data found in source table {table_name}")
            return
            
        logger.info(f"Processing {table_name} with {total_count} total records")
        
        # Process in chunks
        # chunk_size = 800000
        chunk_size = total_count
        total_chunks = (total_count + chunk_size - 1) // chunk_size
        
        for chunk_num in range(total_chunks):
            offset = chunk_num * chunk_size
            remaining = total_count - offset
            current_chunk = min(chunk_size, remaining)
            
            try:
                # Get SQL with proper chunk parameters
                chunk_query = read_sql_file_crossdb(sql_filepath, table_name, gwl_db_dblink, current_chunk, offset)
                
                # Modify query for incremental loading if needed
                if is_incremental:
                    staging_table = f'''{table_name.split('.')[-1].replace('"', '')}'''
                    # Modify the SELECT part to include incremental logic
                    select_part = chunk_query.split("FROM")[0]
                    from_part = chunk_query.split("FROM")[1]
                    
                    # Add incremental condition to the SELECT
                    chunk_query = f"""
                    {select_part}
                    FROM (
                        SELECT t1.* 
                        FROM dblink('{gwl_db_dblink}', 'SELECT * FROM {table_name}') AS t1
                        WHERE NOT EXISTS (
                            SELECT 1 FROM {staging_table} stg 
                            WHERE stg.id = t1.id 
                            AND stg.sync_timestamp > CURRENT_TIMESTAMP - INTERVAL '1 day'
                        )
                    ) t1
                    {from_part.split("AS t1")[1]}
                    """
                    
                    # Log the incremental query for debugging
                    logger.debug(f"Incremental query for {table_name}: {chunk_query}")
                
                logger.info(f"Processing chunk {chunk_num + 1}/{total_chunks} for {table_name}")
                logger.info(f"Inserting {current_chunk} rows starting from offset {offset}")
                
                # Execute chunk insert
                postgres_hook.run(chunk_query)
                
                processed = offset + current_chunk
                logger.info(f"Progress: {processed}/{total_count} rows ({(processed/total_count)*100:.2f}%)")
                
            except Exception as e:
                logger.error(f"Error processing chunk {chunk_num + 1} for {table_name}: {str(e)}")
                raise
        table_name_for_count = table_name.split('.')[-1]    
        logger.info(f"Successfully processed all {total_count} rows for {table_name} in service {prod_service_conn_name}")
        logger.info(f"target table count: {postgres_hook.get_first(f'''SELECT COUNT(*) FROM {table_name_for_count}''')[0]}")

        
        # Clean up old staging data after successful processing
        # if is_incremental:
        #     cleanup_old_staging_data(staging_table)
        
    except Exception as e:
        logger.error(f"Error processing table {table_name}: {str(e)}")
        raise




def fetch_transform_insert_cross_gwl_prod_dailysync(table_name, prod_service_conn_name, is_incremental=False, **kwargs):
    conn = BaseHook.get_connection('gwl_db_conn')
    gwl_db_dblink = convert_pg_connection_string(conn.get_uri())
    sql_filepath = get_table_filepath_cross_gwl_uat(table_name)
    
    try:
        postgres_hook = PostgresHook(postgres_conn_id=prod_service_conn_name)
        
        # Get total count first
        total_count = get_total_count(postgres_hook, gwl_db_dblink, table_name)
        if total_count == 0:
            logger.warning(f"No data found in source table {table_name}")
            return
            
        logger.info(f"Processing {table_name} with {total_count} total records")
        
        # Process in chunks
        # chunk_size = 800000
        chunk_size = total_count
        total_chunks = (total_count + chunk_size - 1) // chunk_size
        
        for chunk_num in range(total_chunks):
            offset = chunk_num * chunk_size
            remaining = total_count - offset
            current_chunk = min(chunk_size, remaining)
            
            try:
                # Get SQL with proper chunk parameters
                chunk_query = read_sql_file_crossdb_dailysync(sql_filepath, table_name, gwl_db_dblink, current_chunk, offset, **kwargs)
                # need to modify the chunk_query for incremental loading
                
                
                
                # Execute chunk insert
                postgres_hook.run(chunk_query)
                
                processed = offset + current_chunk
                logger.info(f"Progress: {processed}/{total_count} rows ({(processed/total_count)*100:.2f}%)")
                
            except Exception as e:
                logger.error(f"Error processing chunk {chunk_num + 1} for {table_name}: {str(e)}")
                raise
        table_name_for_count = table_name.split('.')[-1]    
        logger.info(f"Successfully processed all {total_count} rows for {table_name} in service {prod_service_conn_name}")
        logger.info(f"target table count: {postgres_hook.get_first(f'''SELECT COUNT(*) FROM {table_name_for_count}''')[0]}")

        
        # Clean up old staging data after successful processing
        # if is_incremental:
        #     cleanup_old_staging_data(staging_table)
        
    except Exception as e:
        logger.error(f"Error processing table {table_name}: {str(e)}")
        raise




def fetch_transform_insert_interdb_dailysync(table_name, **kwargs):
    sql_filepath = get_table_filepath_interdb(table_name)
    print(sql_filepath)
    
    try:
        postgres_hook = PostgresHook(postgres_conn_id='gwl_db_conn')
        
        # Get total count first
        total_count = get_total_count_stg(postgres_hook, table_name)
        if total_count == 0:
            print(f"No data found in source table staging_{table_name}")
            # return
            
        print(f"Processing staging_{table_name} to target table with {total_count} total records")
        
        # Process in chunks
        # chunk_size = 1000000
        chunk_size = total_count
        if total_count == 0:
            total_chunks = 1
        else:
            total_chunks = (total_count + chunk_size - 1) // chunk_size
        
        for chunk_num in range(total_chunks):
            offset = chunk_num * chunk_size
            remaining = total_count - offset
            current_chunk = min(chunk_size, remaining)
            
            try:
                # Get SQL with proper chunk parameters
                chunk_query = read_sql_file_interdb_dailysync(sql_filepath, table_name, current_chunk, offset, **kwargs)
                print(f"Processing chunk {chunk_num + 1}/{total_chunks} for staging_{table_name}")
                print(f"Inserting {current_chunk} rows starting from offset {offset}")
                
                # Execute chunk insert
                postgres_hook.run(chunk_query)
                
                processed = offset + current_chunk
                print(f"Progress: {processed}/{total_count} rows ({(processed/total_count)*100:.2f}%)" if total_count != 0 else "Progress: 100%")
                
            except Exception as e:
                print(f"Error processing chunk {chunk_num + 1} for staging_{table_name}: {str(e)}")
                raise
            
        print(f"Successfully processed all {total_count} rows for staging_{table_name}")
        print(f"gwl target table {table_name} count: {get_total_count_gwl(postgres_hook, table_name)} rows")
    except Exception as e:
        print(f"Error processing table staging_{table_name}: {str(e)}")
        raise

def make_pre_transform_table(table_name):
    sql_filepath = get_table_filepath_pre_transform(table_name)
    print(sql_filepath)

    if os.path.isfile(sql_filepath):
        print(f"SQL file  founded: {sql_filepath}")

        try:
            with open(sql_filepath, "r") as file:
                pre_transfrom_sql = file.read()
        
        except FileNotFoundError:
            raise FileNotFoundError(f"SQL file not found: {sql_filepath}")
        except Exception as e:
            raise Exception(f"Error reading SQL file {sql_filepath}: {str(e)}")
                    
        # Execute chunk insert
        postgres_hook = PostgresHook(postgres_conn_id='gwl_db_conn')
        postgres_hook.run(pre_transfrom_sql)
        logger.info(f"Successfully processed {sql_filepath}")
    else:
        print(f"!!!!!!!! SQL file NOT found !!!!!!!! : {sql_filepath}")
        raise FileNotFoundError(f"SQL file not found: {sql_filepath}")
    
def make_pre_transform_table_dailysync(table_name, **kwargs):
    sql_filepath = get_table_filepath_pre_transform_dailysync(table_name)
    print(sql_filepath)

    start_ts    = kwargs['params'].get('start_timestamps', '')   
    end_ts      = kwargs['params'].get('end_timestamps', '')  

    if os.path.isfile(sql_filepath):
        print(f"SQL file  founded: {sql_filepath}")

        try:
            with open(sql_filepath, "r") as file:
                pre_transfrom_sql = file.read()

                # Replace the 'start_timestamps' and 'end_timestamps' in the SQL script it will replace all occurrences of the specified substring with the new substring. However, you can use the optional count parameter to limit the number of replacements. With count parameter:string.replace(old, new, count)  
                pre_transfrom_sql = pre_transfrom_sql.replace('start_timestamps', start_ts)
                pre_transfrom_sql = pre_transfrom_sql.replace('end_timestamps', end_ts)
                print(pre_transfrom_sql)
        
        except FileNotFoundError:
            raise FileNotFoundError(f"SQL file not found: {sql_filepath}")
        except Exception as e:
            raise Exception(f"Error reading SQL file {sql_filepath}: {str(e)}")
                    
        # Execute chunk insert
        postgres_hook = PostgresHook(postgres_conn_id='gwl_db_conn')
        postgres_hook.run(pre_transfrom_sql)
        logger.info(f"Successfully processed {sql_filepath}")
    else:
        print(f"!!!!!!!! SQL file NOT found !!!!!!!! : {sql_filepath}")
        raise FileNotFoundError(f"SQL file not found: {sql_filepath}")

def truncate_tables(table_list):
    try:
        postgres_hook = PostgresHook(postgres_conn_id='gwl_db_conn')
        postgres_hook.run(f"TRUNCATE TABLE {table_list} cascade;")
        print(f"Successfully truncated all tables: {table_list}")

    except Exception as e:
        raise Exception(f"Error truncating tables: {str(e)}") 