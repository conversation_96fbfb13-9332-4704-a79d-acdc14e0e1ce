# ALL_MIGRATION_TABLES = [    
#                             'engagement_service."Member"'
#                             ,'engagement_service."MemberCoupon"'
#                             ,'engagement_service."MemberCouponActivity"'
#                             # ,'engagement_service."Privilege"'                 # MasterTable
#                             ,'engagement_service."MemberPrivilege"'
#                             ,'engagement_service."MemberPrivilegeHistory"'
#                             ,'engagement_service."PrivilegeCostAllocation"'
#                             ,'engagement_service."RedemptionTransaction"'
#                             ,'engagement_service."RewardCategory"'
#                             ,'engagement_service."RewardCostCenter"'
#                             ,'engagement_service."Reward"'
#                             ,'engagement_service."RewardCriteriaValue"'
#                             ,'engagement_service."RewardCostAllocation"'
#                             # ,'partner_service."Partner"'                      # MasterTable
#                             # ,'partner_service."Brand"'                        # MasterTable
#                             # ,'partner_service."Branch"'                       # MasterTable
#                             ,'partner_service."WalletEligibleBranch"'
#                             # ,'partner_service."PartnerBrand"'                 # MasterTable
#                             ,'partner_service."ProductCategory"'
#                             ,'partner_service."ProductBrand"'           
#                             ,'partner_service."Product"'
#                             ,'partner_service."CostCenter"'
#                             ,'partner_service."Import"'
#                             # ,'partner_service."PaymentMethod"'                # MasterTable
#                             # ,'partner_service."WalletPaymentMethod"'          # MasterTable
#                             ,'partner_service."SalesTransaction"'                 #big table
#                             ,'partner_service."SalesTransactionWalletActivity"'
#                             ,'partner_service."SalesTransactionPayment"'          #big table
#                             ,'partner_service."SalesTransactionItem"'              #big table
#                             ,'partner_service."SalesTransactionBurnPayment"'
#                             ,'partner_service."SalesTransactionItemBurnPayment"'
#                             ,'partner_service."SalesTransactionCoupon"'
#                             ,'partner_service."SalesTransactionItemCoupon"'
#                             # ,'loyalty_service."RegisterChannel"'                # MasterTable
#                             # ,'loyalty_service."RegisterLocation"'               # MasterTable
#                             # ,'loyalty_service."Tier"'                           # MasterTable
#                             # ,'loyalty_service."UpgradeGroup"'                   # MasterTable
#                             # ,'loyalty_service."UpgradeReason"'                  # MasterTable
#                             ,'loyalty_service."Member"'                 #big table
#                             ,'loyalty_service."MemberLegacyTierHistory"'
#                             ,'loyalty_service."MemberLog"'
#                             ,'loyalty_service."MemberProfile"'          #big table
#                             ,'loyalty_service."SubSegment"'
#                             ,'loyalty_service."MemberSubSegment"'
#                             ,'loyalty_service."PrivilegeTier"'
#                             ,'loyalty_service."TierLog"'
#                             ,'loyalty_service."TierUpdateRequest"'
#                             ,'loyalty_service."SalesTransaction"'       #big table
#                             # ,'loyalty_service."StaffCompany"'                  # MasterTable
#                             # ,'loyalty_service."StaffLevel"'                    # MasterTable
#                             ,'loyalty_service."StaffProfile"'
#                             ,'loyalty_service."MemberTierHistory"'
#                             ,'loyalty_service."MemberLegacyCoBrandHistory"'
#                             # ,'loyalty_service."Bank"'                          # MasterTable
#                             # ,'loyalty_service."CardType"'                      # MasterTable
#                             # ,'loyalty_service."CoBrand"'                       # MasterTable
#                             ,'loyalty_service."CoBrandPrivilege"'
#                             ,'loyalty_service."MemberCoBrandCardImport"'
#                             ,'loyalty_service."MemberCoBrandCard"'
#                             ,'loyalty_service."CoBrandLog"'
#                             ,'loyalty_service."CoBrandCardLog"'
#                             # ,'point_service."PointSetting"'                    # MasterTable
#                             ,'point_service."PointSettingLog"'
#                             ,'point_service."PointSettingUpdateRequest"'
#                             # ,'point_service."WalletType"'                      # MasterTable
#                             ,'point_service."Wallet"'                  # MasterTable but upsert
#                             ,'point_service."WalletLog"'
#                             # ,'point_service."AdjustmentReason"'                # MasterTable
#                             # ,'point_service."WalletAdjustmentReason"'          # MasterTable
#                             ,'point_service."WalletAdjustmentTransaction"'
#                             ,'point_service."WalletActivity"'           #big table
#                             ,'point_service."WalletBalance"'
#                             ,'point_service."WalletTransaction"'        #big table bigest?
#                             ,'point_service."VoidWalletActivity"'
# ]
ALL_MIGRATION_TABLES = [    
                            # ,'engagement_service."Privilege"'                 # MasterTable
                            # ,'partner_service."Partner"'                      # MasterTable
                            # ,'partner_service."Brand"'                        # MasterTable
                            # ,'partner_service."Branch"'                       # MasterTable
                            # ,'partner_service."PartnerBrand"'                 # MasterTable
                            # ,'partner_service."PaymentMethod"'                # MasterTable
                            # ,'partner_service."WalletPaymentMethod"'          # MasterTable
                            # ,'loyalty_service."RegisterChannel"'                # MasterTable
                            # ,'loyalty_service."RegisterLocation"'               # MasterTable
                            # ,'loyalty_service."Tier"'                           # MasterTable
                            # ,'loyalty_service."UpgradeGroup"'                   # MasterTable
                            # ,'loyalty_service."UpgradeReason"'                  # MasterTable
                            # ,'loyalty_service."StaffCompany"'                  # MasterTable
                            # ,'loyalty_service."StaffLevel"'                    # MasterTable
                            # ,'loyalty_service."Bank"'                          # MasterTable
                            # ,'loyalty_service."CardType"'                      # MasterTable
                            # ,'loyalty_service."CoBrand"'                       # MasterTable
                            # ,'loyalty_service."MemberCoBrandCardImport"'
                            # ,'point_service."PointSetting"'                    # MasterTable
                            # ,'point_service."AdjustmentReason"'                # MasterTable
                            # ,'point_service."WalletAdjustmentReason"'          # MasterTable
                            # ,'point_service."WalletType"'                      # MasterTable
                            # ,'point_service."Wallet"'                          # MasterTable # not upsert anymore
                                                        
                            #### No data on TEMP #####
                            # ,'engagement_service."MemberPrivilegeLog"'
                            # ,'engagement_service."CouponClaimTransaction"'
                            # ,'engagement_service."RewardEarnTransaction"'
                            # ,'engagement_service."MemberActivityRewardCriteria"'
                            # ,'engagement_service."ActivityRewardTask"'
                            # ,'engagement_service."RedemptionTransaction"'
                            # ,'engagement_service."Reward"'
                            # ,'engagement_service."RewardCriteriaValue"'
                            # ,'engagement_service."RewardCategory"'
                            # ,'partner_service."SalesTransactionItemBurnPayment"'
                            # ,'partner_service."SalesTransactionCoupon"'
                            # ,'partner_service."SalesTransactionItemCoupon"'
                            # ,'partner_service."WalletEligibleBranch"'
                            # ,'partner_service."CostCenter"'
                            # ,'partner_service."ImportHistory"'
                            # ,'partner_service."RefundSalesTransactionWalletActivity"'
                            # ,'partner_service."ProductCategory"'
                            # ,'partner_service."ProductBrand"' 
                            # ,'partner_service."Product"'
                            # ,'engagement_service."SalesTransactionCouponUseLater"'
                            # ,'engagement_service."SalesTransactionCouponAdjustment"'
                            # ,'engagement_service."PartnerEligibility"'
                            # ,'engagement_service."PaymentEligibility"'
                            # ,'loyalty_service."MemberLog"'
                            # ,'loyalty_service."SubSegment"'
                            # ,'loyalty_service."MemberSubSegment"'
                            # ,'loyalty_service."TierLog"'
                            # ,'loyalty_service."TierUpdateRequest"'
                            # ,'loyalty_service."PrivilegeTier"'
                            # ,'loyalty_service."MemberTierHistory"'
                            # ,'loyalty_service."CoBrandCardLog"'
                            
                            # ,'loyalty_service."CoBrandPrivilege"'
                            # ,'loyalty_service."CoBrandLog"'
                            # ,'point_service."WalletLog"'
                            # ,'point_service."VoidWalletActivity"'
                            # ,'point_service."PointSettingLog"'
                            # ,'point_service."PointSettingUpdateRequest"'
                            
                            ### Not in ER #####
                            # ,'engagement_service."Member"'
                            # ,'engagement_service."MemberPrivilegeHistory"'
                            # ,'engagement_service."RewardCostCenter"'
                        
                            #### Set 1 ####
                            'loyalty_service."Member"'                 #big table  

                            #### Set 2 ####
                            ,'engagement_service."MemberPrivilege"'
                            ,'engagement_service."MemberCoupon"'
                            ,'engagement_service."MemberCouponActivity"'
                            ,'loyalty_service."SalesTransaction"'       #big table
                            ,'loyalty_service."MemberProfile"'          #big table
                            ,'loyalty_service."StaffProfile"'
                            ,'loyalty_service."MemberCoBrandCard"'
                            ,'loyalty_service."MemberLegacyTierHistory"'
                            ,'loyalty_service."MemberLegacyCoBrandHistory"'
                            ,'partner_service."SalesTransaction"'                 #big table
                            ,'point_service."WalletAdjustmentTransaction"'
                            ,'point_service."WalletBalance"'

                            # #### Set 3 ####
                            ,'partner_service."RefundSalesTransaction"'
                            ,'partner_service."SalesTransactionItem"'              #big table
                            ,'partner_service."SalesTransactionBurnPayment"'
                            ,'partner_service."SalesTransactionPayment"'          #big table
                            ,'point_service."WalletActivity"'           #big table
                            ,'loyalty_service."RefundSalesTransaction"'

                            # #### Set 4 ####
                            ,'partner_service."RefundSalesTransactionItem"'
                            ,'partner_service."SalesTransactionWalletActivity"'
                            ,'point_service."WalletTransaction"'        #big table bigest?    
]
NOT_IN_TEMP_TABLES = [
                            'loyalty_service."MemberCoBrandCard"'
                            ,'loyalty_service."RefundSalesTransaction"'
                            ,'partner_service."RefundSalesTransactionItem"'
                            ,'engagement_service."MemberCoupon"'
                            ,'engagement_service."MemberCouponActivity"'
]

DELETE_MEMBER_TABLES = [
                            'loyalty_service."Member"'
                            ,'engagement_service."MemberPrivilege"'
                            ,'engagement_service."MemberCoupon"'
                            ,'engagement_service."MemberCouponActivity"'
                            ,'partner_service."SalesTransaction"'
                            ,'point_service."WalletActivity"'
                            ,'point_service."WalletBalance"'
                            
]
                         

TESTORDERCHUNK_TABLES = [    'partner_service."ProductBrand"'
                            ,'partner_service."SalesTransactionItem"'
]

PRE_TRANSFORM_TABLES = [    
                            'loyalty_service."Member_tiertransform"'
                            ,'loyalty_service."Member_reason"'
                            ,'loyalty_service."MemberCoBrandCard"'
                            ,'loyalty_service."Member_isCoBrandNonMember"'
                            ,'partner_service."SalesTransaction_refundStatus"'                            
                            ,'point_service."WalletActivityPriorListJson"'
                            ,'point_service."WalletActivityListJson"'
                            ,'partner_service."SalesTransactionWalletActivityTemp"'
                            ,'point_service."PreWalletBalanceTemp"'
                            ,'point_service."WalletBalanceTemp"'  
                            ,'partner_service."RefundSalesTransactionItemId"'                                    
]

GEN_ULID_TABLES = [
    # {
    #     'SOURCE_TABLE': 'staging_point_service."WalletBalanceTemp"',
    #     'TARGET_TABLE': 'staging_point_service."ulid_WalletBalance"',
    #     'PK_NAME': 'ulid_WalletBalance_pkey'
    # }
    {
        'SOURCE_TABLE': 'staging_loyalty_service."Member"',
        'TARGET_TABLE': 'staging_loyalty_service."ulid_member"',
        'PK_NAME': 'ulid_member_pkey'
    },
    {
        'SOURCE_TABLE': 'staging_point_service."WalletActivity"',
        'TARGET_TABLE': 'staging_point_service."ulid_WalletActivity"',
        'PK_NAME': 'ulid_WalletActivity_pkey'
    },
    {
        'SOURCE_TABLE': 'staging_point_service."WalletBalanceTemp"',
        'TARGET_TABLE': 'staging_point_service."ulid_WalletBalance"',
        'PK_NAME': 'ulid_WalletBalance_pkey'
    },
    {
        'SOURCE_TABLE': 'staging_point_service."WalletTransaction"',
        'TARGET_TABLE': 'staging_point_service."ulid_WalletTransaction"',
        'PK_NAME': 'ulid_WalletTransaction_pkey'
    },
    {
        'SOURCE_TABLE': 'staging_point_service."WalletAdjustmentTransaction"',
        'TARGET_TABLE': 'staging_point_service."ulid_WalletAdjustmentTransaction"',
        'PK_NAME': 'ulid_WalletAdjustmentTransaction_pkey'
    },
    {
        'SOURCE_TABLE': 'staging_engagement_service."MemberPrivilege"',
        'TARGET_TABLE': 'staging_engagement_service."ulid_MemberPrivilege"',
        'PK_NAME': 'ulid_MemberPrivilege_pkey'
    },
    {
        'SOURCE_TABLE': 'staging_partner_service."SalesTransactionBurnPayment"',
        'TARGET_TABLE': 'staging_partner_service."ulid_SalesTransactionBurnPayment"',
        'PK_NAME': 'ulid_SalesTransactionBurnPayment_pkey'
    },
    {
        'SOURCE_TABLE': 'public."RefundSalesTransactionItemId"',
        'TARGET_TABLE': 'staging_partner_service."ulid_RefundSalesTransactionItem"',
        'PK_NAME': 'ulid_RefundSalesTransactionItem_pkey'
    },
    {
        'SOURCE_TABLE': 'staging_partner_service."SalesTransactionWalletActivityTemp"',
        'TARGET_TABLE': 'staging_partner_service."ulid_SalesTransactionWalletActivity"',
        'PK_NAME': 'ulid_SalesTransactionWalletActivity_pkey'
    }
]

TRANSFORM_LOAD_TABLES = [    
                            # 'engagement_service."Member"'
                            # ,'engagement_service."MemberCoupon"'
                            # ,'engagement_service."MemberCouponActivity"'
                            # # ,'engagement_service."Privilege"'                 # MasterTable
                            # ,'engagement_service."MemberPrivilege"'
                            # ,'engagement_service."MemberPrivilegeHistory"'
                            # ,'engagement_service."PrivilegeCostAllocation"'
                            # ,'engagement_service."RedemptionTransaction"'
                            # ,'engagement_service."RewardCategory"'
                            # ,'engagement_service."RewardCostCenter"'
                            # ,'engagement_service."Reward"'
                            # ,'engagement_service."RewardCriteriaValue"'
                            # ,'engagement_service."RewardCostAllocation"'
                            # # ,'partner_service."Partner"'                      # MasterTable
                            # # ,'partner_service."Brand"'                        # MasterTable
                            # # ,'partner_service."Branch"'                       # MasterTable
                            # ,'partner_service."WalletEligibleBranch"'
                            # # ,'partner_service."PartnerBrand"'                 # MasterTable
                            # ,'partner_service."ProductCategory"'
                            # ,'partner_service."ProductBrand"'           
                            # ,'partner_service."Product"'
                            # ,'partner_service."CostCenter"'
                            # ,'partner_service."Import"'
                            # # ,'partner_service."PaymentMethod"'                # MasterTable
                            # # ,'partner_service."WalletPaymentMethod"'          # MasterTable
                            # ,'partner_service."SalesTransaction"'                 #big table
                            # ,'partner_service."SalesTransactionWalletActivity"'
                            # ,'partner_service."SalesTransactionPayment"'          #big table
                            # ,'partner_service."SalesTransactionItem"'              #big table
                            # ,'partner_service."SalesTransactionBurnPayment"'
                            # ,'partner_service."SalesTransactionItemBurnPayment"'
                            # ,'partner_service."SalesTransactionCoupon"'
                            # ,'partner_service."SalesTransactionItemCoupon"'
                            # ,'loyalty_service."RegisterChannel"'                # MasterTable
                            # ,'loyalty_service."RegisterLocation"'               # MasterTable
                            # ,'loyalty_service."Tier"'                           # MasterTable
                            # ,'loyalty_service."UpgradeGroup"'                   # MasterTable
                            # ,'loyalty_service."UpgradeReason"'                  # MasterTable
                            # ,'loyalty_service."Member"'                 #big table
                            # ,'loyalty_service."MemberLegacyTierHistory"'
                            # ,'loyalty_service."MemberLog"'
                            # ,'loyalty_service."MemberProfile"'          #big table
                            # ,'loyalty_service."SubSegment"'
                            # ,'loyalty_service."MemberSubSegment"'
                            # ,'loyalty_service."PrivilegeTier"'
                            # ,'loyalty_service."TierLog"'
                            # ,'loyalty_service."TierUpdateRequest"'
                            # ,'loyalty_service."SalesTransaction"'       #big table
                            # # ,'loyalty_service."StaffCompany"'                  # MasterTable
                            # # ,'loyalty_service."StaffLevel"'                    # MasterTable
                            # ,'loyalty_service."StaffProfile"'
                            # ,'loyalty_service."MemberTierHistory"'
                            # ,'loyalty_service."MemberLegacyCoBrandHistory"'
                            # # ,'loyalty_service."Bank"'                          # MasterTable
                            # # ,'loyalty_service."CardType"'                      # MasterTable
                            # # ,'loyalty_service."CoBrand"'                       # MasterTable
                            # ,'loyalty_service."CoBrandPrivilege"'
                            # ,'loyalty_service."MemberCoBrandCardImport"'
                            # ,'loyalty_service."MemberCoBrandCard"'
                            # ,'loyalty_service."CoBrandLog"'
                            # ,'loyalty_service."CoBrandCardLog"'
                            # # ,'point_service."PointSetting"'                    # MasterTable
                            # ,'point_service."PointSettingLog"'
                            # ,'point_service."PointSettingUpdateRequest"'
                            # # ,'point_service."WalletType"'                      # MasterTable
                            # ,'point_service."Wallet"'                  # MasterTable but upsert
                            # ,'point_service."WalletLog"'
                            # # ,'point_service."AdjustmentReason"'                # MasterTable
                            # # ,'point_service."WalletAdjustmentReason"'          # MasterTable
                            'point_service."WalletAdjustmentTransaction"'
                            ,'point_service."WalletActivity"'           #big table
                            ,'point_service."WalletBalance"'
                            ,'point_service."WalletTransaction"'
                            ,'point_service."VoidWalletActivity"'
]









BACKUP_TRANSFORM_LOAD_TABLES = [    
                            'engagement_service."Member"'
                            ,'engagement_service."MemberCoupon"'
                            ,'engagement_service."MemberCouponActivity"'
                            # ,'engagement_service."Privilege"'                 # MasterTable
                            ,'engagement_service."MemberPrivilege"'
                            ,'engagement_service."MemberPrivilegeHistory"'
                            ,'engagement_service."PrivilegeCostAllocation"'
                            ,'engagement_service."RedemptionTransaction"'
                            ,'engagement_service."RewardCategory"'
                            ,'engagement_service."RewardCostCenter"'
                            ,'engagement_service."Reward"'
                            ,'engagement_service."RewardCriteriaValue"'
                            ,'engagement_service."RewardCostAllocation"'
                            # ,'partner_service."Partner"'                      # MasterTable
                            # ,'partner_service."Brand"'                        # MasterTable
                            # ,'partner_service."Branch"'                       # MasterTable
                            ,'partner_service."WalletEligibleBranch"'
                            # ,'partner_service."PartnerBrand"'                 # MasterTable
                            ,'partner_service."ProductCategory"'
                            ,'partner_service."ProductBrand"'           
                            ,'partner_service."Product"'
                            ,'partner_service."CostCenter"'
                            ,'partner_service."Import"'
                            # ,'partner_service."PaymentMethod"'                # MasterTable
                            # ,'partner_service."WalletPaymentMethod"'          # MasterTable
                            ,'partner_service."SalesTransaction"'                 #big table
                            ,'partner_service."SalesTransactionWalletActivity"'
                            ,'partner_service."SalesTransactionPayment"'          #big table
                            ,'partner_service."SalesTransactionItem"'              #big table
                            ,'partner_service."SalesTransactionBurnPayment"'
                            ,'partner_service."SalesTransactionItemBurnPayment"'
                            ,'partner_service."SalesTransactionCoupon"'
                            ,'partner_service."SalesTransactionItemCoupon"'
                            # ,'loyalty_service."RegisterChannel"'                # MasterTable
                            # ,'loyalty_service."RegisterLocation"'               # MasterTable
                            # ,'loyalty_service."Tier"'                           # MasterTable
                            # ,'loyalty_service."UpgradeGroup"'                   # MasterTable
                            # ,'loyalty_service."UpgradeReason"'                  # MasterTable
                            ,'loyalty_service."Member"'                 #big table
                            ,'loyalty_service."MemberLegacyTierHistory"'
                            ,'loyalty_service."MemberLog"'
                            ,'loyalty_service."MemberProfile"'          #big table
                            ,'loyalty_service."SubSegment"'
                            ,'loyalty_service."MemberSubSegment"'
                            ,'loyalty_service."PrivilegeTier"'
                            ,'loyalty_service."TierLog"'
                            ,'loyalty_service."TierUpdateRequest"'
                            ,'loyalty_service."SalesTransaction"'       #big table
                            # ,'loyalty_service."StaffCompany"'                  # MasterTable
                            # ,'loyalty_service."StaffLevel"'                    # MasterTable
                            ,'loyalty_service."StaffProfile"'
                            ,'loyalty_service."MemberTierHistory"'
                            ,'loyalty_service."MemberLegacyCoBrandHistory"'
                            # ,'loyalty_service."Bank"'                          # MasterTable
                            # ,'loyalty_service."CardType"'                      # MasterTable
                            # ,'loyalty_service."CoBrand"'                       # MasterTable
                            ,'loyalty_service."CoBrandPrivilege"'
                            ,'loyalty_service."MemberCoBrandCardImport"'
                            ,'loyalty_service."MemberCoBrandCard"'
                            ,'loyalty_service."CoBrandLog"'
                            ,'loyalty_service."CoBrandCardLog"'
                            # ,'point_service."PointSetting"'                    # MasterTable
                            ,'point_service."PointSettingLog"'
                            ,'point_service."PointSettingUpdateRequest"'
                            # ,'point_service."WalletType"'                      # MasterTable
                            ,'point_service."Wallet"'                  # MasterTable but upsert
                            ,'point_service."WalletLog"'
                            # ,'point_service."AdjustmentReason"'                # MasterTable
                            # ,'point_service."WalletAdjustmentReason"'          # MasterTable
                            ,'point_service."WalletAdjustmentTransaction"'
                            ,'point_service."WalletActivity"'           #big table
                            ,'point_service."WalletBalance"'
                            ,'point_service."WalletTransaction"'        #big table bigest?
                            ,'point_service."VoidWalletActivity"'
]
