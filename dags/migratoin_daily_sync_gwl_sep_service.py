from datetime import datetime, timedelta
import pytz
from airflow import DAG
from airflow.operators.python import PythonOperator
from migration_utils.migration_ops import fetch_transform_insert_cross_gwl_prod_dailysync, fetch_detect_remove_crossdb_dailysync_gwl_prod
from collections import OrderedDict

def crossdb_gwl_to_sep_service_dailysync(table_name, conn_name, **kwargs):
    """
    This function is used to run SQL commands to migrate data from the GWL service to the new separated services.
    """
    fetch_transform_insert_cross_gwl_prod_dailysync(table_name, prod_service_conn_name=conn_name, **kwargs)
    print(f"Data for {table_name} migrated successfully.")


tables_to_migrate = OrderedDict([
    ('loyalty_service."Member"', 'loyalty_service_conn'),
    # ('engagement_service."MemberPrivilege"', 'engagement_service_conn'), # do not exist anymore at prod and uat
    ('engagement_service."MemberCoupon"', 'engagement_service_conn'),
    ('engagement_service."MemberCouponActivity"', 'engagement_service_conn'),
    ('loyalty_service."SalesTransaction"', 'loyalty_service_conn'),
    ('loyalty_service."MemberProfile"', 'loyalty_service_conn'),
    ('loyalty_service."StaffProfile"', 'loyalty_service_conn'),
    ('loyalty_service."MemberCoBrandCard"', 'loyalty_service_conn'),
    ('loyalty_service."MemberLegacyTierHistory"', 'loyalty_service_conn'),
    ('loyalty_service."MemberLegacyCoBrandHistory"', 'loyalty_service_conn'),
    ('partner_service."SalesTransaction"', 'partner_service_conn'),
    ('point_service."WalletAdjustmentTransaction"', 'point_service_conn'),
    ('point_service."WalletBalance"', 'point_service_conn'),
    ('partner_service."RefundSalesTransaction"', 'partner_service_conn'),
    ('partner_service."SalesTransactionItem"', 'partner_service_conn'),
    ('partner_service."SalesTransactionBurnPayment"', 'partner_service_conn'),
    ('partner_service."SalesTransactionPayment"', 'partner_service_conn'),
    ('point_service."WalletActivity"', 'point_service_conn'),
    ('loyalty_service."RefundSalesTransaction"', 'loyalty_service_conn'),
    ('partner_service."RefundSalesTransactionItem"', 'partner_service_conn'),
    ('partner_service."SalesTransactionWalletActivity"', 'partner_service_conn'),
    ('point_service."WalletTransaction"', 'point_service_conn'),
])


def remove_crossdb_gwl_to_sep_service_dailysync(table_name, conn_name, **kwargs):
    """
    Run detect member remove and remove from related tables for a specific service
    """
    fetch_detect_remove_crossdb_dailysync_gwl_prod(table_name, prod_service_conn_name=conn_name, **kwargs)
    print(f"Data for {table_name} removed successfully.")

table_to_remove = OrderedDict([
    ('loyalty_service."Member"', 'loyalty_service_conn'),
    # ('engagement_service."MemberPrivilege"', 'engagement_service_conn'),
    ('engagement_service."MemberCoupon"', 'engagement_service_conn'),
    ('engagement_service."MemberCouponActivity"', 'engagement_service_conn'),
    ('partner_service."SalesTransaction"', 'partner_service_conn'),
    ('point_service."WalletActivity"', 'point_service_conn'),
    ('point_service."WalletBalance"', 'point_service_conn')
])

with DAG(
    dag_id="direct_mig_gwl_sep_service_dailysync_dag",
    start_date=None,
    schedule_interval=None,
    catchup=False,
    tags=["gwl_sep_service_dailysync", "prod", 'dailysync'],
    params= {
        'start_timestamps': datetime.now(pytz.timezone('Asia/Bangkok')).astimezone(pytz.utc),
        'end_timestamps': datetime.now(pytz.timezone('Asia/Bangkok')).astimezone(pytz.utc),
    }
) as dag:

    previous_task = None
    for table, conn in tables_to_migrate.items():
        task = PythonOperator(
            task_id=f'''migrate_{table.replace('.', '_').replace('"', '')}''',
            python_callable=crossdb_gwl_to_sep_service_dailysync,
            op_args=[table, conn],
        )

        if previous_task is not None:
            previous_task >> task
        previous_task = task

    ### remove data ###
    for table, conn in table_to_remove.items():
        task = PythonOperator(
            task_id=f'''remove_{table.replace('.', '_').replace('"', '')}''',
            python_callable=remove_crossdb_gwl_to_sep_service_dailysync,
            op_args=[table, conn],
        )
        if previous_task is not None:
            previous_task >> task
        previous_task = task
