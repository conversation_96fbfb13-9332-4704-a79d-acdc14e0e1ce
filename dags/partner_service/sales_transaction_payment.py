import math
import threading
from concurrent.futures import as_completed, ThreadPoolExecutor
from datetime import datetime, timedelta

from psycopg2.extensions import connection as postgres_connection

from common_helpers.database_services import <PERSON>S<PERSON><PERSON><PERSON><PERSON>, PostgresHandler
from common_helpers.logging import get_logger
from common_helpers.utils import (
    generate_ulid,
    get_query_offsets,
    insert_migration_result,
)

logger = get_logger()


class SalesTransactionPayment:
    def __init__(
        self,
        batch_size: int,
        executor_max_workers: int,
        mssql_handler: MSSQLHandler,
        postgresql_handler: PostgresHandler,
        incremental_query_date: str = None,
    ) -> None:
        self.batch_size = batch_size
        self.executor_max_workers = executor_max_workers
        self.incremental_query_date = incremental_query_date
        self.mssql_handler = mssql_handler
        self.postgresql_handler = postgresql_handler
        self.destination_insert_query = """
            INSERT INTO "partner_service"."SalesTransactionPayment" (
                "id",
                "salesTransactionId",
                "paymentMethodId",
                "amount",
                "settings",
                "createdAt",
                "updatedAt"
            )
            VALUES (%s, %s, %s, %s, '{}', %s, NOW () AT TIME ZONE 'UTC');
        """

    def get_incremental_query_condition(self, date_field: str) -> str:
        """
        Generates a query condition for incremental migration, with specific date supported.

        Args:
            None

        Returns:
            str: A query condition string.
        """
        if self.incremental_query_date is None:
            return f"{date_field} >= DATEADD(DAY, -1, CAST(CAST(GETDATE() AS DATE) AS DATETIME)) AND {date_field} < CAST(CAST(GETDATE() AS DATE) AS DATETIME)"

        return f"{date_field} >= CAST('{self.incremental_query_date}' AS DATETIME) AND {date_field} < DATEADD(DAY, 1, CAST('{self.incremental_query_date}' AS DATETIME))"

    def prepare_sales_transaction_payment_temp_table(self):
        """
        Prepare SalesTransactionPayment result temp table for migration.

        Args:
            None

        Returns:
            None
        """

        create_sales_transaction_payment_temp_table_query = """
            IF OBJECT_ID('temp_sales_transaction_payment_for_full_dump_migration', 'U') IS NULL
            BEGIN
                SELECT
                    ROW_NUMBER() OVER (ORDER BY 
                        CASE
                            WHEN lvh.LVHeaderKey IS NOT NULL THEN lvh.LVHeaderKey
                            ELSE dbo.fn_generate_sales_transaction_id(ssp.key_search)
                        END,
                        TRIM(ssp.MethodCode),
                        ssp.Net,
                        ssh.DataDate
                    ) AS id,
                    CASE
                        WHEN lvh.LVHeaderKey IS NOT NULL THEN lvh.LVHeaderKey
                        ELSE dbo.fn_generate_sales_transaction_id(ssp.key_search)
                    END AS sales_transaction_id,
                    TRIM(ssp.MethodCode) AS payment_method_id,
                    ssp.Net AS amount,
                    ssh.DataDate AS created_at
                INTO temp_sales_transaction_payment_for_full_dump_migration
                FROM Newmember.dbo.SMCSalesPayment ssp
                JOIN Newmember.dbo.SMCSalesHeader ssh ON ssh.key_search = ssp.key_search
                LEFT JOIN LVHeader lvh ON ssh.key_search = lvh.KeySearch COLLATE SQL_Latin1_General_CP1_CI_AS
                WHERE
                    ssh.DataDate < CAST(CAST(DATEADD (DAY, -1, GETDATE()) AS DATE) AS DATETIME)
                    AND (
                        lvh.LVHeaderKey IS NULL
                        OR lvh.DocDate < CAST(CAST(DATEADD (DAY, -1, GETDATE()) AS DATE) AS DATETIME)
                    );

                CREATE INDEX ix_temp_sales_transaction_payment_for_full_dump_migration_id
                ON temp_sales_transaction_payment_for_full_dump_migration (id);
            END;
        """

        loyalty_value_handler = MSSQLHandler(
            conn_id="loyalty_value_smc_db_connection_id"
        )
        loyalty_value_connection = loyalty_value_handler.hook.get_conn()

        try:
            logger.info(
                f"started preparing SalesTransactionPayment temp table for migration..."
            )
            loyalty_value_handler.execute_query_string(
                connection=loyalty_value_connection,
                query_string=create_sales_transaction_payment_temp_table_query,
            )
            logger.info(
                f"finished preparing SalesTransactionPayment temp table for migration."
            )
        finally:
            loyalty_value_connection.close()

    def get_count_query_string(self, is_full_dump: bool = True) -> str:
        """
        Generates a query string for counting total records for both full dump and
        incremental migration.

        Args:
            is_full_dump (bool): Migration type.

        Returns:
            str: A query string.
        """

        if not is_full_dump:
            return f"""
                SELECT COUNT(*)
                FROM Newmember.dbo.SMCSalesPayment ssp
                JOIN Newmember.dbo.SMCSalesHeader ssh ON ssh.key_search = ssp.key_search
                LEFT JOIN LVHeader lvh ON ssh.key_search = lvh.KeySearch COLLATE SQL_Latin1_General_CP1_CI_AS
                WHERE
                    {self.get_incremental_query_condition('ssh.DataDate')}
                    AND (
                        lvh.LVHeaderKey IS NULL
                        OR {self.get_incremental_query_condition('lvh.DocDate')}
                    );
            """

        return """
            SELECT COUNT(*)
            FROM temp_sales_transaction_payment_for_full_dump_migration;
        """

    def get_select_query_string(self, is_full_dump: str) -> str:
        """
        Generates a query string for selecting records from source for both full dump and
        incremental migration.

        Args:
            is_full_dump (bool): Migration type.

        Returns:
            str: A query string.
        """
        if not is_full_dump:
            return f"""
                SELECT
                    ROW_NUMBER() OVER (ORDER BY 
                        sales_transaction_id
                        payment_method_id,
                        amount,
                        created_at
                    ) AS id,
                    sales_transaction_id,
                    payment_method_id,
                    amount,
                    created_at
                FROM (
                    SELECT
                        CASE
                            WHEN lvh.LVHeaderKey IS NOT NULL THEN lvh.LVHeaderKey
                            ELSE dbo.fn_generate_sales_transaction_id(ssp.key_search)
                        END AS sales_transaction_id,
                        TRIM(ssp.MethodCode) AS payment_method_id,
                        ssp.Net AS amount,
                        ssh.DataDate AS created_at
                    FROM Newmember.dbo.SMCSalesPayment ssp
                    JOIN Newmember.dbo.SMCSalesHeader ssh ON ssh.key_search = ssp.key_search
                    LEFT JOIN LVHeader lvh ON ssh.key_search = lvh.KeySearch COLLATE SQL_Latin1_General_CP1_CI_AS
                    WHERE
                        {self.get_incremental_query_condition('ssh.DataDate')}
                        AND (
                            lvh.LVHeaderKey IS NULL
                            OR {self.get_incremental_query_condition('lvh.DocDate')}
                        )
                )
                ORDER BY id
                OFFSET
                    %s ROWS
                FETCH NEXT
                    %s ROWS ONLY;
            """

        return f"""
            SELECT
                id,
                sales_transaction_id,
                payment_method_id,
                amount,
                created_at
            FROM temp_sales_transaction_payment_for_full_dump_migration
            ORDER BY id
            OFFSET
                %s ROWS
            FETCH NEXT
                %s ROWS ONLY;
        """

    def transform_record(
        self,
        record: tuple,
    ) -> tuple:
        """
        Transform a record queried from source table to destination table schema format.

        Args:
            record (tuple): A record queried from source table.

        Returns:
            tuple: A record in destination table schema format.
        """

        (_, sales_transaction_id, payment_method_id, amount, created_at) = record

        return (
            generate_ulid(),
            sales_transaction_id,
            payment_method_id,
            amount,
            created_at,
        )

    def insert_batch_to_destination(
        self,
        connection: postgres_connection,
        batch: list[tuple],
    ) -> None:
        """
        Insert a batch to destination table.

        Args:
            connection (postgres_connection): A PostgreSQL connection.
            batch (list): A list of  records to insert to destination table.

        Returns:
            None
        """
        self.postgresql_handler.execute_with_rollback(
            connection, self.destination_insert_query, batch
        )

    def process_batch(
        self,
        connection: postgres_connection,
        batch: list[tuple],
        batch_no: int,
        total_batches: int,
        total_records: int,
        is_full_dump: bool,
    ) -> None:
        """
        Transform queried result from source table and insert them to a new table.

        Args:
            connection (postgres_connection): A PostgreSQL connection.
            batch (list): A batch to process.
            batch_no (int): The current batch's number, used only for logging.
            total_batches (int): The total number of batches to process, used only for logging.

        Returns:
            None
        """
        logger.info(
            f"started transforming and inserting batch {batch_no}/{total_batches} (size {len(batch)})..."
        )
        transformed_batch = [
            self.transform_record(
                record=record,
            )
            for record in batch
        ]
        self.insert_batch_to_destination(
            connection=connection,
            batch=transformed_batch,
        )
        logger.info(
            f"successfully transformed and inserted batch {batch_no}/{total_batches} (size {len(batch)})."
        )

        if is_full_dump:
            self.postgresql_handler.update_batch_tracker(
                connection=connection,
                service_name="partner_service",
                table_name="SalesTransactionPayment",
                total_records=total_records,
                batch_no=batch_no,
            )

    def migrate(
        self,
        count_query_string: str,
        select_query_string: str,
        is_full_dump: bool = True,
    ) -> None:
        """
        The main function for SalesTransactionPayment migration flow.

        Args:
            None

        Returns:
            None
        """
        created_at = datetime.now()
        incremental_date = (
            None
            if is_full_dump
            else (
                self.incremental_query_date
                if self.incremental_query_date is not None
                else (datetime.today() - timedelta(days=1)).strftime("%Y-%m-%d")
            )
        )

        mssql_connection = self.mssql_handler.hook.get_conn()
        postgresql_connection = self.postgresql_handler.hook.get_conn()

        tracker = (
            self.postgresql_handler.get_latest_batch_info(
                connection=postgresql_connection,
                service_name="partner_service",
                table_name="SalesTransactionPayment",
            )
            if is_full_dump
            else None
        )

        if is_full_dump:
            self.prepare_sales_transaction_payment_temp_table()

        total_records = (
            self.mssql_handler.get_table_total_records(count_query_string)
            if tracker is None
            else tracker[0]
        )
        total_batches = math.ceil(total_records / self.batch_size)
        offsets = get_query_offsets(
            total_records=total_records,
            batch_size=self.batch_size,
            starting_offset=(
                0 if tracker is None else (tracker[1] - 1) * self.batch_size
            ),
        )
        completed_batches = tracker[2] if tracker is not None else []

        is_migration_succeeded = False

        try:
            futures = []

            batch_generator = self.mssql_handler.generate_batches(
                connection=mssql_connection,
                query_string=select_query_string,
                total_records=total_records,
                batch_size=self.batch_size,
                offsets=offsets,
                completed_batches=completed_batches,
            )

            with ThreadPoolExecutor(max_workers=self.executor_max_workers) as executor:
                semaphore = threading.Semaphore(self.executor_max_workers)

                while True:
                    semaphore.acquire()

                    try:
                        batch, batch_no = next(batch_generator)
                    except StopIteration:
                        break

                    future = executor.submit(
                        self.process_batch,
                        postgresql_connection,
                        batch,
                        batch_no,
                        total_batches,
                        total_records,
                        is_full_dump,
                    )
                    futures.append(future)
                    future.add_done_callback(lambda _: semaphore.release())

                for future in as_completed(futures):
                    future.result()

            logger.info(
                f"succesfully processed {total_records} records into SalesTransactionPayment"
            )

            logger.info(f"started cleaning up batch tracker...")
            self.postgresql_handler.cleanup_batch_tracker(
                connection=postgresql_connection,
                service_name="partner_service",
                table_name="SalesTransactionPayment",
            )
            logger.info(f"finished cleaning up batch tracker.")

            is_migration_succeeded = True

        except Exception as error:
            logger.error(f"an error has occured: {error}")

            if postgresql_connection:
                logger.info("a postgresql connection is found, rolling back...")
                postgresql_connection.rollback()
                logger.info("successfully rolled back.")

            raise error
        finally:
            logger.info("started inserting migration result log...")
            if is_migration_succeeded:
                insert_migration_result(
                    postgresql_handler=self.postgresql_handler,
                    dag_name=f"partner_service_{'full_dump' if is_full_dump else 'incremental'}_migration_sales_transaction_payment",
                    migration_type="FULL_DUMP" if is_full_dump else "INCREMENTAL",
                    source_table="LVHeader",
                    source_table_count=total_records,
                    destination_table="SalesTransactionPayment",
                    destination_table_count=total_records,
                    validation_type="COMPLETENESS",
                    validation_result=100,
                    created_at=created_at,
                    incremental_date=incremental_date,
                )
            else:
                tracker = self.postgresql_handler.get_latest_batch_info(
                    connection=postgresql_connection,
                    service_name="partner_service",
                    table_name="SalesTransactionPayment",
                )

                destination_table_count = (
                    0
                    if tracker is None
                    else (
                        ((tracker[1] - 1) * self.batch_size)
                        if (tracker[1] - 1) * self.batch_size <= total_records
                        else total_records
                    )
                )

                total_processed = (
                    0 if tracker is None else len(tracker[2]) * self.batch_size
                )

                if tracker is not None and total_batches in tracker[2]:
                    total_processed -= self.batch_size + total_records % self.batch_size

                insert_migration_result(
                    postgresql_handler=self.postgresql_handler,
                    dag_name=f"partner_service_{'full_dump' if is_full_dump else 'incremental'}_migration_sales_transaction_payment",
                    migration_type="FULL_DUMP" if is_full_dump else "INCREMENTAL",
                    source_table="LVHeader",
                    source_table_count=total_records,
                    destination_table="SalesTransactionPayment",
                    destination_table_count=destination_table_count,
                    validation_type="COMPLETENESS",
                    validation_result=(
                        0 if tracker is None else total_processed / total_records * 100
                    ),
                    created_at=created_at,
                    incremental_date=incremental_date,
                )
            logger.info("finished inserting migration result log.")

            if mssql_connection:
                mssql_connection.close()
            if postgresql_connection:
                postgresql_connection.close()

    def migrate_full_dump(self) -> None:
        """
        The main function for SalesTransactionPayment full dump migration flow.

        Args:
            None

        Returns:
            None
        """
        full_dump_count_query_string = self.get_count_query_string(is_full_dump=True)
        full_dump_select_query_string = self.get_select_query_string(is_full_dump=True)

        self.migrate(
            count_query_string=full_dump_count_query_string,
            select_query_string=full_dump_select_query_string,
            is_full_dump=True,
        )

    def migrate_incremental(self) -> None:
        """
        The main function for SalesTransactionPayment incremental migration flow.

        Args:
            None

        Returns:
            None
        """
        incremental_count_query_string = self.get_count_query_string(is_full_dump=False)
        incremental_select_query_string = self.get_select_query_string(
            is_full_dump=False
        )

        self.migrate(
            count_query_string=incremental_count_query_string,
            select_query_string=incremental_select_query_string,
            is_full_dump=False,
        )
