import math
import os
import threading
from concurrent.futures import as_completed, ThreadPoolExecutor
from datetime import datetime, timedelta

from psycopg2.extensions import connection as postgres_connection

from common_helpers.database_services import <PERSON>S<PERSON><PERSON><PERSON><PERSON>, PostgresHandler
from common_helpers.logging import get_logger
from common_helpers.mapper import CSVMapper
from common_helpers.utils import get_query_offsets, insert_migration_result

logger = get_logger()


class SalesTransactionBurnPayment:
    def __init__(
        self,
        batch_size: int,
        executor_max_workers: int,
        mssql_handler: MSSQLHandler,
        postgresql_handler: PostgresHandler,
        incremental_query_date: str = None,
    ) -> None:
        self.batch_size = batch_size
        self.executor_max_workers = executor_max_workers
        self.incremental_query_date = incremental_query_date
        self.wallet_mapping_file_mapper = CSVMapper(
            file_path=os.path.join(
                "dags",
                "data",
                "wallet_mapping.csv",
            ),
            key="Value Code",
            sub_keys=["WalletCode"],
        )
        self.mssql_handler = mssql_handler
        self.postgresql_handler = postgresql_handler
        self.destination_insert_query = """
            INSERT INTO "partner_service"."SalesTransactionBurnPayment" (
                "id",
                "walletCode",
                "burnAmount",
                "beforeAmount",
                "afterAmount",
                "paymentAmount",
                "settings",
                "createdAt",
                "updatedAt",
                "salesTransactionId"
            )
            VALUES (%s, %s, %s, %s, %s, %s, '{}', %s, NOW () AT TIME ZONE 'UTC', %s)
            ON CONFLICT ("id") DO NOTHING;
        """

    def get_incremental_query_condition(self) -> str:
        """
        Generates a query condition for incremental migration, with specific date supported.

        Args:
            None

        Returns:
            str: A query condition string.
        """
        if self.incremental_query_date is None:
            return "lvh.DocDate >= DATEADD(DAY, -1, CAST(CAST(GETDATE() AS DATE) AS DATETIME)) AND lvh.DocDate < CAST(CAST(GETDATE() AS DATE) AS DATETIME)"

        return f"lvh.DocDate >= CAST('{self.incremental_query_date}' AS DATETIME) AND lvh.DocDate < DATEADD(DAY, 1, CAST('{self.incremental_query_date}' AS DATETIME))"

    def get_count_query_string(self, is_full_dump: bool = True) -> str:
        """
        Generates a query string for counting total records for both full dump and
        incremental migration.

        Args:
            is_full_dump (bool): Migration type.

        Returns:
            str: A query string.
        """
        return f"""
            SELECT COUNT(*)
            FROM (
                SELECT
                    lvh.LVHeaderKey
                FROM LVHeader lvh
                JOIN LVTrans lvt ON lvt.LVHeaderKey = lvh.LVHeaderKey 
                JOIN LVData lvd ON lvd.LVMainKey = lvt.LVMainKey
                WHERE
                    lvt.MovementCode = 'USE'
                    AND lvd.ValueCode IN (
                    'AP001',
                    'EP001',
                    'EP002',
                    'EP003',
                    'EP004',
                    'EP005',
                    'EP006',
                    'EP007',
                    'EP008',
                    'EP009',
                    'EP010',
                    'KPC01',
                    'KPO02',
                    'CR001',
                    'PT001'
                ){" AND lvh.DocDate < CAST(CAST(DATEADD (DAY, -1, GETDATE()) AS DATE) AS DATETIME)" if is_full_dump else f" AND {self.get_incremental_query_condition()}"}
            ) AS total_rows;
        """

    def get_select_query_string(self, is_full_dump: str) -> str:
        """
        Generates a query string for selecting records from source for both full dump and
        incremental migration.

        Args:
            is_full_dump (bool): Migration type.

        Returns:
            str: A query string.
        """
        return f"""
            SELECT
                CAST(lvh.LVHeaderKey AS VARCHAR(20)) + '_' + CAST(lvt.LVTransKey AS VARCHAR(8)) + '_' + CAST(lvd.LVMainKey AS VARCHAR(20)) AS id,
                lvd.ValueCode AS value_code,
                ABS(lvt.Amount) AS burn_amount,
                lvt.PreviousBalance AS before_amount,
                lvt.Amount + lvt.PreviousBalance AS after_amount,
                DATEADD (HOUR, -7, lvh.AddDT) AS created_at,
                lvh.LVHeaderKey AS sales_transaction_id
            FROM LVHeader lvh
            JOIN LVTrans lvt ON lvt.LVHeaderKey = lvh.LVHeaderKey 
            JOIN LVData lvd ON lvd.LVMainKey = lvt.LVMainKey
            WHERE
                lvt.MovementCode = 'USE'
                AND lvd.ValueCode IN (
                'AP001',
                'EP001',
                'EP002',
                'EP003',
                'EP004',
                'EP005',
                'EP006',
                'EP007',
                'EP008',
                'EP009',
                'EP010',
                'KPC01',
                'KPO02',
                'CR001',
                'PT001'
            ){" AND lvh.DocDate < CAST(CAST(DATEADD (DAY, -1, GETDATE()) AS DATE) AS DATETIME)" if is_full_dump else f" AND {self.get_incremental_query_condition()}"}
            ORDER BY
                lvh.LVHeaderKey,
                lvt.LVTransKey,
                lvd.LVMainKey
            OFFSET
                %s ROWS
            FETCH NEXT
                %s ROWS ONLY;
        """

    def transform_record(
        self,
        record: tuple,
    ) -> tuple:
        """
        Transform a record queried from source table to destination table schema format.

        Args:
            record (tuple): A record queried from source table.

        Returns:
            tuple: A record in destination table schema format.
        """

        (
            id,
            value_code,
            burn_amount,
            before_amount,
            after_amount,
            created_at,
            sales_transaction_id,
        ) = record

        wallet_code = self.wallet_mapping_file_mapper.get_value(value_code)[
            "WalletCode"
        ]

        return (
            id,
            wallet_code,
            burn_amount,
            before_amount,
            after_amount,
            burn_amount,
            created_at,
            sales_transaction_id,
        )

    def insert_batch_to_destination(
        self,
        connection: postgres_connection,
        batch: list[tuple],
    ) -> None:
        """
        Insert a batch to destination table.

        Args:
            connection (postgres_connection): A PostgreSQL connection.
            batch (list): A list of  records to insert to destination table.

        Returns:
            None
        """
        self.postgresql_handler.execute_with_rollback(
            connection, self.destination_insert_query, batch
        )

    def process_batch(
        self,
        connection: postgres_connection,
        batch: list[tuple],
        batch_no: int,
        total_batches: int,
        total_records: int,
        is_full_dump: bool,
    ) -> None:
        """
        Transform queried result from source table and insert them to a new table.

        Args:
            connection (postgres_connection): A PostgreSQL connection.
            batch (list): A batch to process.
            batch_no (int): The current batch's number, used only for logging.
            total_batches (int): The total number of batches to process, used only for logging.

        Returns:
            None
        """
        logger.info(
            f"started transforming and inserting batch {batch_no}/{total_batches} (size {len(batch)})..."
        )
        transformed_batch = [
            self.transform_record(
                record=record,
            )
            for record in batch
        ]
        self.insert_batch_to_destination(
            connection=connection,
            batch=transformed_batch,
        )
        logger.info(
            f"successfully transformed and inserted batch {batch_no}/{total_batches} (size {len(batch)})."
        )

        if is_full_dump:
            self.postgresql_handler.update_batch_tracker(
                connection=connection,
                service_name="partner_service",
                table_name="SalesTransactionBurnPayment",
                total_records=total_records,
                batch_no=batch_no,
            )

    def migrate(
        self,
        count_query_string: str,
        select_query_string: str,
        is_full_dump: bool = True,
    ) -> None:
        """
        The main function for SalesTransactionBurnPayment migration flow.

        Args:
            None

        Returns:
            None
        """
        created_at = datetime.now()
        incremental_date = (
            None
            if is_full_dump
            else (
                self.incremental_query_date
                if self.incremental_query_date is not None
                else (datetime.today() - timedelta(days=1)).strftime("%Y-%m-%d")
            )
        )

        mssql_connection = self.mssql_handler.hook.get_conn()
        postgresql_connection = self.postgresql_handler.hook.get_conn()

        tracker = (
            self.postgresql_handler.get_latest_batch_info(
                connection=postgresql_connection,
                service_name="partner_service",
                table_name="SalesTransactionBurnPayment",
            )
            if is_full_dump
            else None
        )

        total_records = (
            self.mssql_handler.get_table_total_records(count_query_string)
            if tracker is None
            else tracker[0]
        )
        total_batches = math.ceil(total_records / self.batch_size)
        offsets = get_query_offsets(
            total_records=total_records,
            batch_size=self.batch_size,
            starting_offset=(
                0 if tracker is None else (tracker[1] - 1) * self.batch_size
            ),
        )
        completed_batches = tracker[2] if tracker is not None else []

        is_migration_succeeded = False

        try:
            futures = []

            batch_generator = self.mssql_handler.generate_batches(
                connection=mssql_connection,
                query_string=select_query_string,
                total_records=total_records,
                batch_size=self.batch_size,
                offsets=offsets,
                completed_batches=completed_batches,
            )

            with ThreadPoolExecutor(max_workers=self.executor_max_workers) as executor:
                semaphore = threading.Semaphore(self.executor_max_workers)

                while True:
                    semaphore.acquire()

                    try:
                        batch, batch_no = next(batch_generator)
                    except StopIteration:
                        break

                    future = executor.submit(
                        self.process_batch,
                        postgresql_connection,
                        batch,
                        batch_no,
                        total_batches,
                        total_records,
                        is_full_dump,
                    )
                    futures.append(future)
                    future.add_done_callback(lambda _: semaphore.release())

                for future in as_completed(futures):
                    future.result()

            logger.info(
                f"succesfully processed {total_records} records into SalesTransactionBurnPayment"
            )

            logger.info(f"started cleaning up batch tracker...")
            self.postgresql_handler.cleanup_batch_tracker(
                connection=postgresql_connection,
                service_name="partner_service",
                table_name="SalesTransactionBurnPayment",
            )
            logger.info(f"finished cleaning up batch tracker.")

            is_migration_succeeded = True

        except Exception as error:
            logger.error(f"an error has occured: {error}")

            if postgresql_connection:
                logger.info("a postgresql connection is found, rolling back...")
                postgresql_connection.rollback()
                logger.info("successfully rolled back.")

            raise error
        finally:
            logger.info("started inserting migration result log...")
            if is_migration_succeeded:
                insert_migration_result(
                    postgresql_handler=self.postgresql_handler,
                    dag_name=f"partner_service_{'full_dump' if is_full_dump else 'incremental'}_migration_sales_transaction_burn_payment",
                    migration_type="FULL_DUMP" if is_full_dump else "INCREMENTAL",
                    source_table="LVHeader",
                    source_table_count=total_records,
                    destination_table="SalesTransactionBurnPayment",
                    destination_table_count=total_records,
                    validation_type="COMPLETENESS",
                    validation_result=100,
                    created_at=created_at,
                    incremental_date=incremental_date,
                )
            else:
                tracker = self.postgresql_handler.get_latest_batch_info(
                    connection=postgresql_connection,
                    service_name="partner_service",
                    table_name="SalesTransactionBurnPayment",
                )

                destination_table_count = (
                    0
                    if tracker is None
                    else (
                        ((tracker[1] - 1) * self.batch_size)
                        if (tracker[1] - 1) * self.batch_size <= total_records
                        else total_records
                    )
                )

                total_processed = (
                    0 if tracker is None else len(tracker[2]) * self.batch_size
                )

                if tracker is not None and total_batches in tracker[2]:
                    total_processed -= self.batch_size + total_records % self.batch_size

                insert_migration_result(
                    postgresql_handler=self.postgresql_handler,
                    dag_name=f"partner_service_{'full_dump' if is_full_dump else 'incremental'}_migration_sales_transaction_burn_payment",
                    migration_type="FULL_DUMP" if is_full_dump else "INCREMENTAL",
                    source_table="LVHeader",
                    source_table_count=total_records,
                    destination_table="SalesTransactionBurnPayment",
                    destination_table_count=destination_table_count,
                    validation_type="COMPLETENESS",
                    validation_result=(
                        0 if tracker is None else total_processed / total_records * 100
                    ),
                    created_at=created_at,
                    incremental_date=incremental_date,
                )
            logger.info("finished inserting migration result log.")

            if mssql_connection:
                mssql_connection.close()
            if postgresql_connection:
                postgresql_connection.close()

    def migrate_full_dump(self) -> None:
        """
        The main function for SalesTransactionBurnPayment full dump migration flow.

        Args:
            None

        Returns:
            None
        """
        full_dump_count_query_string = self.get_count_query_string(is_full_dump=True)
        full_dump_select_query_string = self.get_select_query_string(is_full_dump=True)

        self.migrate(
            count_query_string=full_dump_count_query_string,
            select_query_string=full_dump_select_query_string,
            is_full_dump=True,
        )

    def migrate_incremental(self) -> None:
        """
        The main function for SalesTransactionBurnPayment incremental migration flow.

        Args:
            None

        Returns:
            None
        """
        incremental_count_query_string = self.get_count_query_string(is_full_dump=False)
        incremental_select_query_string = self.get_select_query_string(
            is_full_dump=False
        )

        self.migrate(
            count_query_string=incremental_count_query_string,
            select_query_string=incremental_select_query_string,
            is_full_dump=False,
        )
