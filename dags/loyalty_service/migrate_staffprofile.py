from airflow import DAG
from common_helpers.database_services import <PERSON>S<PERSON><PERSON><PERSON><PERSON>, PostgresHandler
from constants import (
    NEWMEMBER_CONN_ID,
    TEMP_CONN_ID,
    BATCH_SIZE,
    LS_INCREMENTAL_DATE,
    LS_FULLDUMP_DATE,
)
from datetime import datetime, timedelta
from common_helpers.utils import (
    generate_ulid,
    incremental_date_condition,
    ls_dag_name,
    destination_count,
    get_last_successful_batch,
    set_last_successful_batch,
    reset_last_successful_batch,
    save_migration_result,
    upsert_data_without_encrypt,
    create_migration_result_table,
    log_successfully_migrated_data,
    log_success_process_batch,
    log_start_process_batch,
    calc_offset,
    calc_last_batch_size,
    calc_total_batches,
    is_last_batch,
    get_incremental_date,
    get_df,
    full_dump_date_condition,
)
from airflow.operators.python import PythonOperator
import pandas as pd
from common_helpers.utils import get_logger


logger = get_logger()

TABLE = "StaffPro<PERSON>le"

daily_condition = f"({incremental_date_condition('add_datetime', date=LS_INCREMENTAL_DATE)} OR {incremental_date_condition('update_datetime', date=LS_INCREMENTAL_DATE)})"
full_dump_condition = f"{full_dump_date_condition('add_datetime', LS_FULLDUMP_DATE)}"


def get_total_rows(full_dump: bool):
    mssql = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)
    query = f"""
        select count(*) from df_member 
        where subprogram_id in ('3333', '3377', '3388', '8866', '8877', '8888') 
        and staff_flag = 1 and del_flag = ' '
    """

    if full_dump:
        query = f"{query} and {full_dump_condition}"
    else:
        query = f"{query} and {daily_condition}"

    return mssql.extract_data(query)[0][0]


def mapping_data(offset: int, full_dump: bool, batch_size: int) -> pd.DataFrame:
    mssql = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)

    # field: staff_company
    # is_staff_condtion = "subprogram_id IN ('3333', '3377', '3388', '8866', '8877', '8888') AND staff_flag = 1"

    is_staff_condtion = "subprogram_id IN ('3333', '3377', '3388', '8866', '8877', '8888') AND staff_flag = 1"
    migrate_condition = f"del_flag = ' ' and {is_staff_condtion}"

    if full_dump:
        migrate_condition = f"{migrate_condition} and {full_dump_condition}"
    else:
        migrate_condition = f"{migrate_condition} and {daily_condition}"

    query = f"""
    SELECT member_id, subprogram_id, w_position FROM df_member 
    WHERE {migrate_condition} ORDER BY member_id
    OFFSET {offset} ROWS FETCH NEXT {batch_size} ROWS ONLY;
    """
    df = get_df(query, mssql)

    # Apply .str.strip() to all string columns
    df = df.map(lambda x: x.strip() if isinstance(x, str) else x)

    # 1. subprogram_id: 3333, 8888
    df1 = df[df["subprogram_id"].isin(["3333", "8888"])]

    # get staff company from TableAttribute
    if not df1.empty:
        ids = df1["member_id"].to_list()
        id_condition = ", ".join([f"'{id}'" for id in ids])
        query = f"""select ta.ValueString, ta.ValueCode from TableAttribute ta 
        where ta.TableName = 'df_member' and ta.TableKey = 'member_id'
        and ta.AttributeName = 'staff_comp' and ValueCode IN ({id_condition})"""
        tableattr_df = get_df(query, mssql)

        # change deprecated company code: V&A -> V_AND_A
        tableattr_df["ValueString"].replace("V&A", "V_AND_A", inplace=True)

        df1 = df1.merge(
            tableattr_df, left_on="member_id", right_on="ValueCode", how="left"
        )

        # debug: log unmigrated StaffProfile
        unmigrated_df = df1[df1["ValueString"].isna()]
        logger.info(
            f"Member with no staff_com ({len(unmigrated_df)} members): {unmigrated_df['member_id'].to_list()}"
        )

        # will delete this later -> member_id: 1005815 don't have ValueString
        df1 = df1[(~df1["ValueString"].isna())]

        df1.drop(columns=["ValueCode"], inplace=True)
        df1.rename(columns={"ValueString": "staff_company"}, inplace=True)

    # 2. subprogram_id: 3377, 3388, 8866, 8877
    df2 = df[df["subprogram_id"].isin(["3377", "3388", "8866", "8877"])]
    df2["staff_company"] = df2["subprogram_id"].replace(
        {"3377": "KPHM", "3388": "KPMN", "8866": "KPMN", "8877": "KPHM"}
    )

    # Concatenate DataFrames along rows
    df = pd.concat([df1, df2], axis=0)

    if df.empty:
        return df

    # field: staff_level
    staff_mapping = {
        "subprogram_id": ["3333", "3377", "3388", "8866", "8877", "8888"],
        "staff_level": [
            "OFFICER",
            "OFFICER",
            "OFFICER",
            "MANAGEMENT",
            "MANAGEMENT",
            "MANAGEMENT",
        ],
    }
    staff_mapping_df = pd.DataFrame(staff_mapping)
    df = df.merge(staff_mapping_df, on="subprogram_id", how="left")

    # field: ValueString (on member_id = ValueCode)
    ids = df["member_id"].to_list()
    query = f"""
    SELECT ValueCode, ValueString FROM TableAttribute 
    WHERE ValueCode IN ({", ".join([f"'{id}'" for id in ids])}) 
    and TableName = 'df_member' and TableKey = 'member_id' and AttributeName = 'staff_id'"""
    valuestring_df = get_df(query, mssql)

    df = df.merge(valuestring_df, left_on="member_id", right_on="ValueCode", how="left")

    # debug: log unmigrated StaffProfile
    unmigrated_df = df[df["ValueString"].isna()]
    logger.info(
        f"Member with no staff_id ({len(unmigrated_df)} members): {unmigrated_df['member_id'].to_list()}"
    )

    # get only the data row that have ValueString
    df = df[~df["ValueString"].isna()]

    # drop unused columns for saving to destination db
    df.drop(columns=["subprogram_id", "ValueCode"], inplace=True)

    df.rename(
        columns={
            "member_id": "memberId",
            "staff_level": "staffLevelCode",
            "staff_company": "companyCode",
            "ValueString": "staffNo",
            "w_position": "staffPosition",
        },
        inplace=True,
    )

    # field: id
    df["id"] = [generate_ulid() for _ in range(len(df))]

    return df


def migrate_staff_profile_data():

    full_dump = True
    start_time = datetime.now()  # keep record for start time

    try:
        total_rows = get_total_rows(full_dump)
        total_batches = calc_total_batches(total_rows, BATCH_SIZE)
        last_batch_size = calc_last_batch_size(total_rows, BATCH_SIZE)
        last_successful_batch = get_last_successful_batch(TABLE)

        for batch_num in range(last_successful_batch + 1, total_batches):
            log_start_process_batch(TABLE, batch_num, total_batches)

            offset = calc_offset(batch_num, BATCH_SIZE)
            batch_size = (
                last_batch_size
                if is_last_batch(batch_num, total_batches)
                else BATCH_SIZE
            )

            df = mapping_data(offset, full_dump, batch_size)

            if df.empty:
                set_last_successful_batch(TABLE, batch_num, full_dump)
                logger.info(f"There is NO {TABLE} data at batch {batch_num}")
                continue

            upsert_data_without_encrypt(
                df=df, table=TABLE, conflict_target=["memberId"]
            )

            set_last_successful_batch(TABLE, batch_num)
            log_success_process_batch(
                TABLE, batch_num, total_batches, batch_size, len(df)
            )

        reset_last_successful_batch(TABLE)

    except Exception as err:
        logger.error(f"An error has occured: {err}")
        raise err

    else:
        dest_count = destination_count(TABLE)
        log_successfully_migrated_data(TABLE, total_records=dest_count)

    finally:
        dest_count = destination_count(TABLE)
        save_migration_result(
            full_dump=full_dump,
            table=TABLE,
            source_table="df_member",
            source_count=total_rows,
            dest_count=dest_count,
            created_at=start_time,
        )


def incremental_migrate_staff_profile_data():

    start_time = datetime.now()  # keep record for start time
    full_dump = False

    try:
        total_rows = get_total_rows(full_dump)
        total_batches = calc_total_batches(total_rows, BATCH_SIZE)
        last_batch_number = calc_last_batch_size(total_rows, BATCH_SIZE)

        dest_count = 0

        for batch_num in range(0, total_batches):
            log_start_process_batch(TABLE, batch_num, total_batches)

            offset = calc_offset(batch_num, BATCH_SIZE)
            batch_size = (
                last_batch_number
                if is_last_batch(batch_num, total_batches)
                else BATCH_SIZE
            )

            df = mapping_data(offset, full_dump, batch_size)

            if df.empty:
                logger.info(f"There is NO {TABLE} data at batch {batch_num}")
                continue

            upsert_data_without_encrypt(
                df=df, table=TABLE, conflict_target=["memberId"]
            )

            total_rows = len(df)
            dest_count += total_rows

            log_success_process_batch(
                TABLE, batch_num, total_batches, batch_size, total_rows
            )

    except Exception as err:
        logger.error(f"An error has occured: {err}")
        raise err

    else:
        log_successfully_migrated_data(TABLE, total_records=dest_count)

    finally:
        save_migration_result(
            full_dump=full_dump,
            table=TABLE,
            source_table="df_member",
            source_count=total_rows,
            dest_count=dest_count,
            created_at=start_time,
            incremental_date=get_incremental_date(LS_INCREMENTAL_DATE),
        )


def validate_staff_profile_migration():
    """
    Compare data between source and destination databases to ensure data integrity.
    Returns a dictionary with comparison results.
    """
    mssql = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)
    postgres = PostgresHandler(conn_id=TEMP_CONN_ID)

    # Get source data with staff level and company information
    source_query = """
    SELECT 
        m.member_id, 
        m.subprogram_id, 
        m.w_position,
        CASE 
            WHEN m.subprogram_id IN ('3333', '3377', '3388') THEN 'OFFICER'
            WHEN m.subprogram_id IN ('8866', '8877', '8888') THEN 'MANAGEMENT'
        END as staff_level,
        CASE 
            WHEN m.subprogram_id IN ('3333', '8888') THEN ta.ValueString
            WHEN m.subprogram_id = '3377' THEN 'KPHM'
            WHEN m.subprogram_id = '3388' THEN 'KPMN'
            WHEN m.subprogram_id = '8866' THEN 'KPMN'
            WHEN m.subprogram_id = '8877' THEN 'KPHM'
        END as staff_company,
        ta2.ValueString as staff_id
    FROM df_member m
    LEFT JOIN TableAttribute ta ON 
        ta.TableName = 'df_member' 
        AND ta.TableKey = 'member_id'
        AND ta.AttributeName = 'staff_comp'
        AND ta.ValueCode = m.member_id
    LEFT JOIN TableAttribute ta2 ON 
        ta2.TableName = 'df_member' 
        AND ta2.TableKey = 'member_id'
        AND ta2.AttributeName = 'staff_id'
        AND ta2.ValueCode = m.member_id
    WHERE m.subprogram_id in ('3333', '3377', '3388', '8866', '8877', '8888') 
    AND m.staff_flag = 1 
    AND m.del_flag = ' '
    """
    source_df = get_df(source_query, mssql)

    # Trim string data in source DataFrame
    source_df = source_df.apply(lambda x: x.str.strip() if x.dtype == "object" else x)
    
    # Convert V&A to V_AND_A in staff_company column
    source_df['staff_company'] = source_df['staff_company'].replace('V&A', 'V_AND_A')

    # Get destination data
    dest_query = f'SELECT * FROM loyalty_service."{TABLE}"'
    dest_df = get_df(dest_query, postgres)

    # Compare counts
    source_count = len(source_df)
    dest_count = len(dest_df)

    # Compare specific fields
    comparison_results = {
        "total_records": {
            "source": source_count,
            "destination": dest_count,
            "match": source_count == dest_count,
        },
        "missing_records": [],
        "mismatched_records": [],
    }

    # Check for missing records
    source_member_ids = set(source_df["member_id"])
    dest_member_ids = set(dest_df["memberId"])
    missing_ids = source_member_ids - dest_member_ids

    if missing_ids:
        comparison_results["missing_records"] = list(missing_ids)
        logger.warning(f"Found {len(missing_ids)} missing records in destination")

    # Check for mismatched records
    for member_id in source_member_ids.intersection(dest_member_ids):
        source_row = source_df[source_df["member_id"] == member_id].iloc[0]
        dest_row = dest_df[dest_df["memberId"] == member_id].iloc[0]

        # Compare staffPosition
        if source_row["w_position"] != dest_row["staffPosition"]:
            comparison_results["mismatched_records"].append(
                {
                    "member_id": member_id,
                    "field": "staffPosition",
                    "source": source_row["w_position"],
                    "destination": dest_row["staffPosition"],
                }
            )

        # Compare staffLevelCode
        if source_row["staff_level"] != dest_row["staffLevelCode"]:
            comparison_results["mismatched_records"].append(
                {
                    "member_id": member_id,
                    "field": "staffLevelCode",
                    "source": source_row["staff_level"],
                    "destination": dest_row["staffLevelCode"],
                }
            )

        # Compare companyCode
        if source_row["staff_company"] != dest_row["companyCode"]:
            comparison_results["mismatched_records"].append(
                {
                    "member_id": member_id,
                    "field": "companyCode",
                    "source": source_row["staff_company"],
                    "destination": dest_row["companyCode"],
                }
            )

        # Compare staffNo
        if source_row["staff_id"] != dest_row["staffNo"]:
            comparison_results["mismatched_records"].append(
                {
                    "member_id": member_id,
                    "field": "staffNo",
                    "source": source_row["staff_id"],
                    "destination": dest_row["staffNo"],
                }
            )

    # Log comparison results in a more readable format
    logger.info("=== Data Comparison Results ===")
    logger.info(f"Total Records:")
    logger.info(f"  Source: {source_count}")
    logger.info(f"  Destination: {dest_count}")
    logger.info(f"  Match: {'Yes' if source_count == dest_count else 'No'}")
    
    if missing_ids:
        logger.info(f"\nMissing Records ({len(missing_ids)}):")
        for member_id in missing_ids:
            logger.info(f"  Member ID: {member_id}")
    
    if comparison_results["mismatched_records"]:
        logger.info(f"\nMismatched Records ({len(comparison_results['mismatched_records'])}):")
        for record in comparison_results["mismatched_records"]:
            logger.info(f"  Member ID: {record['member_id']}")
            logger.info(f"    Field: {record['field']}")
            logger.info(f"    Source: {record['source']}")
            logger.info(f"    Destination: {record['destination']}")
    
    if not missing_ids and not comparison_results["mismatched_records"]:
        logger.info("No discrepancies found. All records match perfectly!")

    return comparison_results


# full dump dag
with DAG(
    ls_dag_name(table=TABLE, full_dump=True),
    description="Migrate data from source to Staff Profile Table",
    schedule_interval=None,
    start_date=datetime(2023, 1, 1),
    catchup=False,
    tags=["loyalty_service", "full_dump", "staff_profile"],
) as dag:

    create_migration_result_table_task = PythonOperator(
        task_id=f"migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    mapping_data_task = PythonOperator(
        task_id="mapping_staff_profile_data",
        python_callable=migrate_staff_profile_data,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )
    
    validate_migration_task = PythonOperator(
        task_id="validate_staff_profile_migration",
        python_callable=validate_staff_profile_migration,
    )

    create_migration_result_table_task >> mapping_data_task >> validate_migration_task
