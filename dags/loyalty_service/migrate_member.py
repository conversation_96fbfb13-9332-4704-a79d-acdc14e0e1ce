import traceback
import pandas as pd
import numpy as np
from airflow import DAG
from datetime import datetime, timedelta, timezone
from common_helpers.database_services import <PERSON>g<PERSON><PERSON><PERSON><PERSON>, MSSQLHandler
from common_helpers.utils import (
    rename_columns,
    row_count,
    convert_bangkok_to_utc,
    get_last_successful_batch,
    set_last_successful_batch,
    reset_last_successful_batch,
    get_logger,
    get_field_df,
    destination_count,
    ls_dag_name,
    create_migration_result_table,
    escape_single_quotes,
    incremental_date_condition_for_member,
    upsert_encrypt_data,
    save_migration_result,
    log_start_process_batch,
    log_success_process_batch,
    log_successfully_migrated_data,
    calc_total_batches,
    calc_last_batch_size,
    is_last_batch,
    calc_offset,
    hash_and_encode,
    get_incremental_date,
    get_df,
    clean_id_card,
)
from common_helpers.utils_member import (
    get_subprogram_mapping,
    clean_email,
    email_cleansing_df,
    get_phone_number,
    get_duplicated_contact_in_db,
    get_duplicated_contact,
    handle_contact,
    get_phonecode_df,
    get_member_ids_with_address,
    clean_phone_number,
)
from common_helpers.utils_memberprofile import get_country_mapping
from airflow.operators.python import PythonOperator
from constants import (
    NEWMEMBER_CONN_ID,
    TEMP_CONN_ID,
    BATCH_SIZE,
    SUBPROGRAM_PATH,
    EMAIL_MAPPING_PATH,
    ENCRYPT_KEY,
    LS_INCREMENTAL_DATE,
    LS_FULLDUMP_DATE,
)
from loyalty_service.validators.member import validate_member_migration

SERVICE = "Loyalty Service"
TABLE = "Member"
daily_condition = f"({incremental_date_condition_for_member('add_datetime', LS_INCREMENTAL_DATE)} OR {incremental_date_condition_for_member('update_datetime', LS_INCREMENTAL_DATE)})"

logger = get_logger()


def mapping_data(
    offset: int,
    full_dump: bool,
    field_df: pd.DataFrame,
    email_df: pd.DataFrame,
    subprogram_mapping: tuple[dict],
    country_mapping: dict[str],
    phonecode_df: pd.DataFrame,
    batch_size: int = BATCH_SIZE,
) -> pd.DataFrame:

    mssql = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)

    # 1. df_member
    table = "df_member"
    thai_fields = ["remark"]
    source_fields = field_df[
        (field_df["SMC Table"] == table) & (~field_df["SMC Field"].isin(thai_fields))
    ]["SMC Field"].unique()

    migrate_condition = "del_flag = ''"
    if full_dump:
        migrate_condition = f"{migrate_condition}"
    else:
        migrate_condition = f"{migrate_condition} AND {daily_condition}"

    query = f"""
        SELECT mobile1, mobile2, china_mobile, country_code, id_card, CAST(remark AS NVARCHAR(max)) AS remark, {", ".join(source_fields)} FROM {table}
        WHERE {migrate_condition} ORDER BY member_id 
        OFFSET {offset} ROWS FETCH NEXT {batch_size} ROWS ONLY;
    """
    df = get_df(query, mssql) 

    # clean data: Apply .str.strip() to all string columns
    df = df.map(lambda x: x.strip() if isinstance(x, str) else x)
    df.replace({r"\x00": ""}, regex=True, inplace=True)
    df["shopping_card"].replace("", None, inplace=True)
    df["email"].replace("", None, inplace=True)
    df["id_card"] = df["id_card"].apply(clean_id_card)

    # used for `email` and `phone` field
    df["nationalityCode"] = df["country_code"].replace(country_mapping).apply(lambda x: x.upper())

    # field: "phoneCode"
    # 1st: get phoneCode from NationalityCode
    df = df.merge(
        phonecode_df, left_on="nationalityCode", right_on="alpha3Code", how="left"
    )
    # 2nd: if member have address -> phoneCode: +66 
    # Get member_ids that have address, then set member_id's phoneCode to '+66'
    ids = df["member_id"].to_list()
    id_list = get_member_ids_with_address(ids)
    df.loc[df["member_id"].isin(id_list), "phoneCode"] = "+66"

    # clean phone number
    df["mobile1"] = df.apply(
        lambda row: clean_phone_number(
            row["mobile1"],
            row["email"],
            row["id_card"],
            row["nationalityCode"],
            row["phoneCode"]
        ),
        axis=1,
    )
    df["mobile2"] = df.apply(
        lambda row: clean_phone_number(
            row["mobile2"],
            row["email"],
            row["id_card"],
            row["nationalityCode"],
            row["phoneCode"]
        ),
        axis=1,
    )
    df["china_mobile"] = df.apply(
        lambda row: clean_phone_number(
            row["china_mobile"],
            row["email"],
            row["id_card"],
            row["nationalityCode"],
            None
        ),
        axis=1,
    )

    # transform the fields that need to be transformed
    df["email"] = df["email"].apply(lambda e: clean_email(e, email_df))
    df["del_flag"].replace("", True, inplace=True)
    df["staff_source"].replace("", None, inplace=True)
    df["add_datetime"] = df["add_datetime"].apply(convert_bangkok_to_utc)
    df["phone"] = df.apply(
        lambda row: get_phone_number(
            row["nationalityCode"], row["mobile1"], row["mobile2"], row["china_mobile"]
        ),
        axis=1,
    )
    df["phone"].replace(np.nan, None, inplace=True)

    ug_mapping, ur_mapping, rc_mapping, rl_mapping = subprogram_mapping
    df["upgradeGroupCode"] = df["subprogram_id"].map(ug_mapping).replace({np.nan: None})
    df["upgradeReasonCode"] = (
        df["subprogram_id"].map(ur_mapping).replace({np.nan: None})
    )
    df["registrationChannelCode"] = df["subprogram_id"].map(rc_mapping)
    df["registrationLocationCode"] = (
        df["subprogram_id"].map(rl_mapping).replace(np.nan, "NA")
    )

    # 2. SMCSalesTrans
    df["lifeTimeSpending"] = 0
    df["accumulateSpending"] = 0

    # 3. No Source: ['id', 'phoneCode', 'tierEndedAt', 'tierId', 'tierStartedAt']
    now_utc = datetime.now(timezone.utc)
    start_utc = now_utc.replace(hour=17, minute=00, second=00, microsecond=000000)
    end_utc = now_utc.replace(hour=16, minute=59, second=59, microsecond=999000)

    df["id"] = df["member_id"]
    df["tierId"] = "1"
    df["tierStartedAt"] = start_utc
    df["tierEndedAt"] = end_utc


    # mock data
    df["accumulateMaintainSpending"] = 0

    # inplace=True: Original df is changed, axis=1: means operate on columns
    dropped_columns = [
        "subprogram_id",
        "mobile1",
        "mobile2",
        "china_mobile",
        "country_code",
        "alpha3Code",
        "id_card",
    ]
    df.drop(columns=dropped_columns, axis=1, inplace=True)

    df.replace(np.nan, None, inplace=True)
    df = rename_columns(df, field_df)

    # replace ' with ''(excaped character when insert with encryption)
    df["remark"] = df["remark"].apply(escape_single_quotes)

    return df


def update_query(gwl_no: str, email: str | None, phone: str | None) -> str:
    """
    Update `email` and `phone` query with encrpt data via pgp_sym_encrypt
    """
    # there is some case that ' in email
    if email and "'" in email:
        email = email.replace("'", "''")

    query = f"""
    UPDATE "loyalty_service"."{TABLE}"
    SET 
        email = {f"pgp_sym_encrypt('{email}', '{ENCRYPT_KEY}')" if email else "NULL"}, 
        phone = {f"pgp_sym_encrypt('{phone}', '{ENCRYPT_KEY}')" if phone else "NULL"},
        "emailHash" = {hash_and_encode(email)},
        "phoneHash" = {hash_and_encode(phone)}
    WHERE "gwlNo" = '{str(gwl_no)}';
    """

    return query


def update_contact_info(df, conn) -> None:
    """
    Update `email` and `phone` field in the Temp Database
    """
    # Loop through DataFrame and execute an update query for each row
    for _, row in df.iterrows():
        query = update_query(row["gwlNo"], row["email"], row["phone"])

        with conn.cursor() as cursor:
            cursor.execute(query)


def update_email_and_phone() -> None:

    mssql = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)
    temp_postgres = PostgresHandler(conn_id=TEMP_CONN_ID)

    duplicated_emails, duplicated_phones = get_duplicated_contact_in_db()
    conn = temp_postgres.hook.get_conn()

    query = f'SELECT COUNT(*) FROM "loyalty_service"."Member"'
    total_rows = temp_postgres.extract_data(query)[0][0]
    total_batches = calc_total_batches(total_rows, BATCH_SIZE)
    last_batch_size = calc_last_batch_size(total_rows, BATCH_SIZE)

    for batch_num in range(total_batches):

        offset = calc_offset(batch_num, BATCH_SIZE)
        batch_size = (
            last_batch_size if is_last_batch(batch_num, total_batches) else BATCH_SIZE
        )

        query = f"""
            SELECT "gwlNo", email, phone FROM "loyalty_service"."Member"
            ORDER BY "gwlNo" OFFSET {offset} ROWS 
            FETCH NEXT {batch_size} ROWS ONLY;
        """
        df = get_df(query, temp_postgres)

        member_ids = df["gwlNo"].to_list()
        query = f"""
        SELECT member_id, country_code FROM df_member
        WHERE member_id IN ({", ".join([f"'{id}'" for id in member_ids])})
        """
        member_df = get_df(query, mssql)
        member_df["country_code"] = member_df["country_code"].apply(lambda x: x.upper())
        df = df.merge(member_df, left_on="gwlNo", right_on="member_id", how="left")

        df[["email", "phone"]] = df.apply(
            lambda row: pd.Series(
                handle_contact(
                    row["gwlNo"],  # member_id
                    row["country_code"],
                    row["email"],
                    row["phone"],
                    duplicated_emails,
                    duplicated_phones,
                )
            ),
            axis=1,
        )
        df.drop(
            columns=["member_id", "country_code"],
            axis=1,
            inplace=True,
        )

        update_contact_info(df, conn)
        log_success_process_batch(
            table="handle contact for email and phone",
            batch_num=batch_num,
            total_batches=total_batches,
            batch_size=batch_size,
            total_records=len(df),
        )

    conn.commit()
    logger.info(f"Successfully updated `email` and `phone` field in {TABLE} table")


def migrate_member_data() -> None:
    """
    Migrate member data for full dump
    """
    start_time = datetime.now()  # keep record for start time
    full_dump = True

    field_df = get_field_df(SERVICE, TABLE)

    # used for mapping
    email_df = email_cleansing_df(EMAIL_MAPPING_PATH)
    subprogram_mapping = get_subprogram_mapping(SUBPROGRAM_PATH)
    country_mapping = get_country_mapping()
    phonecode_df = get_phonecode_df()  # alpha3Code and phoneCode df

    try:
        total_rows = row_count(
            "member_id", "df_member", "del_flag = ''"
        )
        total_batches = calc_total_batches(total_rows, BATCH_SIZE)
        last_batch_size = calc_last_batch_size(total_rows, BATCH_SIZE)

        last_successful_batch = get_last_successful_batch(TABLE)

        for batch_num in range(last_successful_batch + 1, total_batches):

            log_start_process_batch(
                table=TABLE,
                batch_num=batch_num,
                total_batches=total_batches,
            )

            offset = calc_offset(batch_num, BATCH_SIZE)
            batch_size = (
                last_batch_size
                if is_last_batch(batch_num, total_batches)
                else BATCH_SIZE
            )

            df = mapping_data(
                offset=offset,
                full_dump=full_dump,
                field_df=field_df,
                email_df=email_df,
                subprogram_mapping=subprogram_mapping,
                country_mapping=country_mapping,
                phonecode_df=phonecode_df,
                batch_size=batch_size,
            )
            df.drop(columns=["nationalityCode"], axis=1, inplace=True)

            upsert_encrypt_data(
                df=df,
                table=TABLE,
                encrypt_columns=["embossNo"],
                conflict_target=["id"],
            )

            total_records = len(df)

            set_last_successful_batch(TABLE, batch_num)
            log_success_process_batch(
                table=TABLE,
                batch_num=batch_num,
                total_batches=total_batches,
                batch_size=batch_size,
                total_records=total_records,
            )

        # handle `email` and `phone` case (contact info sheet)
        update_email_and_phone()

        reset_last_successful_batch(TABLE)

    except Exception as err:
        logger.error(f"An error has occured: {err}")
        traceback.print_exc()
        raise err

    else:
        dest_count = destination_count(TABLE)
        log_successfully_migrated_data(TABLE, dest_count)

    finally:
        dest_count = destination_count(TABLE)
        save_migration_result(
            full_dump=full_dump,
            source_table="df_member",
            table=TABLE,
            source_count=total_rows,
            dest_count=destination_count(TABLE),
            created_at=start_time,
        )


def incremental_migrate_member_data():
    """
    Member Migration for Incremental Dump
    """
    start_time = datetime.now()  # keep record for start time
    full_dump = False

    # used for mapping
    email_df = email_cleansing_df(EMAIL_MAPPING_PATH)
    subprogram_mapping = get_subprogram_mapping(SUBPROGRAM_PATH)
    country_mapping = get_country_mapping()
    phonecode_df = get_phonecode_df()  # alpha3Code and phoneCode df
    field_df = get_field_df(SERVICE, TABLE)

    try:
        total_rows = row_count(
            count_field="member_id",
            table="df_member",
            condition=f"del_flag = '' and {daily_condition}",
        )
        total_batches = calc_total_batches(total_rows, BATCH_SIZE)
        last_batch_size = calc_last_batch_size(total_rows, BATCH_SIZE)

        dest_count = 0

        for batch_num in range(0, total_batches):
            log_start_process_batch(
                table=TABLE,
                batch_num=batch_num,
                total_batches=total_batches,
            )

            offset = calc_offset(batch_num, BATCH_SIZE)
            batch_size = (
                last_batch_size
                if is_last_batch(batch_num, total_batches)
                else BATCH_SIZE
            )

            df = mapping_data(
                offset=offset,
                full_dump=False,
                field_df=field_df,
                email_df=email_df,
                subprogram_mapping=subprogram_mapping,
                country_mapping=country_mapping,
                phonecode_df=phonecode_df,
                batch_size=batch_size,
            )

            # handle duplicated contact
            df["email"].replace(np.nan, None, inplace=True)
            df["phone"].replace(np.nan, None, inplace=True)

            duplicated_emails, duplicated_phones = get_duplicated_contact(df)

            df[["email", "phone"]] = df.apply(
                lambda row: pd.Series(
                    handle_contact(
                        row["gwlNo"],  # member_id
                        row["nationalityCode"],
                        row["email"],
                        row["phone"],
                        duplicated_emails,
                        duplicated_phones,
                    )
                ),
                axis=1,
            )

            df.drop(columns=["nationalityCode"], axis=1, inplace=True)

            upsert_encrypt_data(
                df=df,
                table=TABLE,
                encrypt_columns=["embossNo", "email", "phone"],
                conflict_target=["id"],
                hash_columns=["email", "phone"],
            )

            total_records = len(df)
            dest_count += total_records

            log_success_process_batch(
                table=TABLE,
                batch_num=batch_num,
                total_batches=total_batches,
                batch_size=batch_size,
                total_records=total_records,
            )
    except Exception as err:
        logger.error(f"An error has occured: {err}")
        raise err
    else:
        log_successfully_migrated_data(TABLE, dest_count)
    finally:
        save_migration_result(
            full_dump=full_dump,
            table=TABLE,
            source_table="df_member",
            source_count=total_rows,
            dest_count=dest_count,
            created_at=start_time,
            incremental_date=get_incremental_date(LS_INCREMENTAL_DATE),
        )


# full dump dag
with DAG(
    ls_dag_name(table=TABLE, full_dump=True),
    description="Member Migration",
    schedule_interval=None,
    start_date=datetime(2023, 1, 1),
    catchup=False,
    tags=["loyalty_service", "full_dump", "member"],
) as dag:

    # create_migration_result_table_task = PythonOperator(
    #     task_id=f"migration_result_table_task",
    #     python_callable=create_migration_result_table,
    # )

    # migrate_member_task = PythonOperator(
    #     task_id=f"mapping_member_data",
    #     python_callable=migrate_member_data,
    #     retries=3,
    #     retry_delay=timedelta(minutes=5),
    # )

    validate_migration_task = PythonOperator(
        task_id="validate_migration",
        python_callable=validate_member_migration,
        retries=2,
        retry_delay=timedelta(minutes=2),
    )

    # create_migration_result_table_task >> migrate_member_task >> validate_migration_task

    validate_migration_task


# incremental dump dag
with DAG(
    ls_dag_name(table=TABLE, full_dump=False),
    description="Incremental Member Migration",
    schedule_interval=None,
    start_date=datetime(2023, 1, 1),
    catchup=False,
    tags=["loyalty_service", "incremental", "member"],
) as dag:

    create_migration_result_table_task = PythonOperator(
        task_id=f"migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    migrate_member_task = PythonOperator(
        task_id=f"mapping_member_data",
        python_callable=incremental_migrate_member_data,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    create_migration_result_table_task >> migrate_member_task
