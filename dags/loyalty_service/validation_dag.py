# dag for running validaiton script on loyalty service

import pandas as pd
from airflow import DAG
from datetime import datetime, timedelta
from airflow.operators.python import PythonOperator
from common_helpers.logging import get_logger
from common_helpers.utils import get_df
from constants import TEMP_CONN_ID
from common_helpers.database_services import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


def get_accum_spending_query() -> str:
    """
    Generates an SQL query to validate accumulated spending of member.
    """
    return """
    WITH last_2_years AS (
        SELECT DATE_TRUNC('month', NOW() - INTERVAL '1 day') - INTERVAL '2 years' AS start_date
    )
    SELECT 
        -- Total member spending
        (SELECT SUM("accumulateSpending") 
        FROM loyalty_service."Member") AS total_member_accummulate_spending,

        -- Total sales spending
        (SELECT COALESCE(SUM("totalAccumSpendableAmount"), 0) 
        FROM loyalty_service."SalesTransaction"
        WHERE "createdAt" >= (SELECT start_date FROM last_2_years)) AS total_sales_spending_in_last_2_years,

        -- Total refund spending
        (SELECT COALESCE(SUM("revokeAccumSpendableAmount"), 0) 
        FROM loyalty_service."RefundSalesTransaction"
        WHERE "createdAt" >= (SELECT start_date FROM last_2_years)) AS total_refund_spending_in_last_2_years,

        -- Difference calculation
        (
            (SELECT SUM("accumulateSpending") 
            FROM loyalty_service."Member") 
            - 
            (
                COALESCE(
                    (SELECT SUM("totalAccumSpendableAmount") 
                    FROM loyalty_service."SalesTransaction"
                    WHERE "createdAt" >= (SELECT start_date FROM last_2_years)), 0
                ) 
                + 
                COALESCE(
                    (SELECT SUM("revokeAccumSpendableAmount") 
                    FROM loyalty_service."RefundSalesTransaction"
                    WHERE "createdAt" >= (SELECT start_date FROM last_2_years)), 0
                )
            )
        ) AS difference;
    """


def get_life_spending_query() -> str:
    """
    Generates an SQL query to validate life time spending of member.
    """
    return """
    SELECT 
        (SELECT COALESCE(SUM("lifeTimeSpending"), 0) 
        FROM loyalty_service."Member") AS total_member_life_time_spending,
        
        (SELECT COALESCE(SUM("netTotalAmount"), 0) 
        FROM loyalty_service."SalesTransaction") AS total_sales_spending,
        
        (SELECT COALESCE(SUM("refundAmount"), 0) 
        FROM loyalty_service."RefundSalesTransaction") AS total_refund_spending,
        
        (
            (SELECT COALESCE(SUM("lifeTimeSpending"), 0) 
            FROM loyalty_service."Member") 
            - 
            (
                COALESCE((SELECT SUM("netTotalAmount") 
                        FROM loyalty_service."SalesTransaction"), 0) 
                - 
                COALESCE((SELECT SUM("refundAmount") 
                        FROM loyalty_service."RefundSalesTransaction"), 0)
            )
        ) AS difference;
    """


def log_dataframe_values(df: pd.DataFrame) -> None:
    """
    Logs the column name and value of each column in the DataFrame.

    Parameters:
    df (pd.DataFrame): The DataFrame whose column names and values need to be logged.
    """
    logger = get_logger()

    # Iterate over each column in the DataFrame
    for column in df.columns:
        value = df[column].iloc[0]  # Get the first value of the column
        if isinstance(value, (int, float)):
            value = f"{value:,}"

        # Log the column name and its value
        logger.info(f"{column}: {value}")


def log_life_time_spending_result(logger, difference) -> None:
    logger = get_logger()
    match difference:
        case 0:
            logger.success(
                "Validation No.2 successful: Member's life time spending matches sales and refund transactions."
            )
        case _:
            logger.error(
                "Validation No.2 failed: Member's life time spending does not match sales and refund transactions."
            )


def log_accum_spending_result(logger, difference) -> None:
    logger = get_logger()
    match difference:
        case 0:
            logger.success(
                "Validation No.1 successful: Member's accumulated spending matches sales and refund transactions in the last 2 year."
            )
        case _:
            logger.error(
                "Validation No.1 failed: Member's accumulated spending does not match sales and refund transactions."
            )


def validation_log() -> None:
    logger = get_logger()
    temp_postgres = PostgresHandler(conn_id=TEMP_CONN_ID)

    # log validation of member's accumulated spending
    query = get_accum_spending_query()
    validation_df = get_df(query, temp_postgres)
    log_dataframe_values(validation_df)

    difference = validation_df["difference"].iloc[0]
    log_accum_spending_result(logger, difference)

    # log validation of member's life time spending
    query = get_life_spending_query()
    validation_df = get_df(query, temp_postgres)
    log_dataframe_values(validation_df)

    difference = validation_df["difference"].iloc[0]
    log_life_time_spending_result(logger, difference)


with DAG(
    "loyalty_service_validation_log",
    description=f"Validation log for loyalty service",
    schedule_interval=None,
    start_date=datetime(2023, 1, 1),
    catchup=False,
    tags=["loyalty_service", "validation_log"],
) as dag:

    validation_log_task = PythonOperator(
        task_id=f"validation_log_task",
        python_callable=validation_log,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    validation_log_task
