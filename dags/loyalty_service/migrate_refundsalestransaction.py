import traceback
from airflow import DAG
from datetime import datetime, timedelta
from airflow.operators.python import PythonOperator
from common_helpers.utils import (
    incremental_date_condition,
    upsert_data_without_encrypt,
    get_last_successful_batch,
    set_last_successful_batch,
    reset_last_successful_batch,
    create_migration_result_table,
    destination_count,
    ls_dag_name,
    save_migration_result,
    log_successfully_migrated_data,
    log_start_process_batch,
    log_success_process_batch,
    calc_offset,
    calc_last_batch_size,
    calc_total_batches,
    is_last_batch,
    get_incremental_date,
    get_df,
    full_dump_date_condition,
    generate_ulid,
    generate_id_via_keysearch,
)
import pandas as pd
from common_helpers.database_services import <PERSON>S<PERSON><PERSON>and<PERSON>, PostgresHandler
from constants import (
    NEWMEMBER_CONN_ID,
    BATCH_SIZE,
    LS_INCREMENTAL_DATE,
    LS_FULLDUMP_DATE,
    TEMP_CONN_ID,
)
from common_helpers.logging import get_logger

TABLE = "RefundSalesTransaction"
logger = get_logger()


def _get_table_names(full_dump: bool) -> dict:
    suffix = "full_dump" if full_dump else "incremental"
    return {
        "header": f"temp_smc_sales_header_for_refund_{suffix}",
        "header_tnx": f"temp_smc_sales_header_tnx_for_refund_{suffix}",
        "lv_header": f"LoyaltyValue.dbo.temp_lv_header_for_refund_{suffix}",
    }


def _prepare_temp_table(
    handler: any, connection: any, query_string: str, source_table: str
) -> None:
    logger.info(f"started preparing {source_table} temp table for migration...")
    handler.execute_query_string(
        connection=connection,
        query_string=query_string,
    )
    logger.info(f"finished preparing {source_table} temp table for migration.")


def create_temp_refund_table(is_full_dump: bool = True):
    """
    Prepare indexed temporary tables for SalesTransaction migration.

    Args:
        is_full_dump (bool): The migration type.

    Returns:
        None
    """

    full_dump_tables = _get_table_names(full_dump=True)
    incremental_tables = _get_table_names(full_dump=False)

    # SalesStatus = 'R'
    create_smc_sales_header_temp_table = (
        f"""
        IF OBJECT_ID('{full_dump_tables["header"]}', 'U') IS NOT NULL
        BEGIN
            DROP TABLE {full_dump_tables["header"]};
        END

        select 
            ssh.key_search COLLATE SQL_Latin1_General_CP1_CI_AS as externalId, 
            ssh.RecvKeySearch COLLATE SQL_Latin1_General_CP1_CI_AS as RecvKeySearch,
            ssh.DataDate as DataDate,
            ssh.DataDate as refundedAt,
            ssh.DataDate as createdAt,
            ssh.DataDate as updatedAt
        into {full_dump_tables["header"]}
        from SMCSalesHeader ssh 
        where ssh.SaleStatus  = 'R' and {full_dump_date_condition('ssh.DataDate', LS_FULLDUMP_DATE)};
        CREATE INDEX temp_smc_sales_header_full_dump_externalId_idx ON {full_dump_tables["header"]} (externalId);
        """
        if is_full_dump
        else f"""
        IF OBJECT_ID('{incremental_tables["header"]}', 'U') IS NOT NULL
        BEGIN
            DROP TABLE {incremental_tables["header"]};
        END

        select 
            ssh.key_search COLLATE SQL_Latin1_General_CP1_CI_AS as externalId, 
            ssh.RecvKeySearch COLLATE SQL_Latin1_General_CP1_CI_AS as RecvKeySearch,
            ssh.DataDate as DataDate,
            ssh.DataDate as refundedAt,
            ssh.DataDate as createdAt,
            ssh.DataDate as updatedAt
        into {incremental_tables["header"]}
        from SMCSalesHeader ssh
        where ssh.SaleStatus  = 'R' and {incremental_date_condition("ssh.DataDate", LS_INCREMENTAL_DATE)};
        CREATE INDEX temp_smc_sales_header_incremental_externalId_idx ON {incremental_tables["header"]} (externalId);
        """
    )

    # SalesStatus != 'R' (SalesTransactions list)
    create_smc_sales_header_tnx_temp_table = (
        f"""
        IF OBJECT_ID('{full_dump_tables["header_tnx"]}', 'U') IS NOT NULL
        BEGIN
            DROP TABLE {full_dump_tables["header_tnx"]};
        END

        select 
            TRIM(ssh.member_id) AS memberId,
            ssh.key_search COLLATE SQL_Latin1_General_CP1_CI_AS AS key_search
        into {full_dump_tables["header_tnx"]}
        from SMCSalesHeader ssh 
        JOIN df_member dm ON dm.member_id = ssh.member_id
        where ssh.SaleStatus != 'R' and dm.del_flag = '';
        CREATE INDEX temp_smc_sales_header_tnx_full_dump_key_search_idx ON {full_dump_tables["header_tnx"]} (key_search);
        """
        if is_full_dump
        else f"""
        IF OBJECT_ID('{incremental_tables["header_tnx"]}', 'U') IS NOT NULL
        BEGIN
            DROP TABLE {incremental_tables["header_tnx"]};
        END

        select 
            TRIM(ssh.member_id) AS memberId,
            ssh.key_search COLLATE SQL_Latin1_General_CP1_CI_AS AS key_search
        into {incremental_tables["header_tnx"]}
        from SMCSalesHeader ssh 
        JOIN df_member dm ON dm.member_id = ssh.member_id
        where ssh.SaleStatus != 'R' and dm.del_flag = '';
        CREATE INDEX temp_smc_sales_header_tnx_incremental_key_search_idx ON {incremental_tables["header_tnx"]} (key_search);
        """
    )

    create_lv_header_temp_table = (
        f"""
        IF OBJECT_ID('{full_dump_tables["lv_header"]}', 'U') IS NOT NULL
        BEGIN
            DROP TABLE {full_dump_tables["lv_header"]};
        END;

        WITH MaxRowsLVHeader AS (
            SELECT 
                l.KeySearch, 
                l.LVHeaderKey,
                l.CancelHeaderKey,
                l.DocDate,
                ROW_NUMBER() OVER (PARTITION BY l.KeySearch ORDER BY l.LVHeaderKey DESC) AS rn
            FROM LoyaltyValue.dbo.LVHeader l 
        )
        select 
            ml.KeySearch, 
            ml.LVHeaderKey as salesTransactionId,
            ml.CancelHeaderKey,
            ml.DocDate as DocDate
        INTO {full_dump_tables["lv_header"]}
        from MaxRowsLVHeader ml
        where ml.rn = 1 and {full_dump_date_condition('ml.DocDate', LS_FULLDUMP_DATE)};
        CREATE INDEX temp_lv_header_full_dump_key_search_idx ON {full_dump_tables["lv_header"]} (KeySearch);
        CREATE INDEX temp_lv_header_full_dump_lv_header_key_idx ON {full_dump_tables["lv_header"]} (salesTransactionId);
        """
        if is_full_dump
        else f"""
        IF OBJECT_ID('{incremental_tables["lv_header"]}', 'U') IS NOT NULL
        BEGIN
            DROP TABLE {incremental_tables["lv_header"]};
        END;

        WITH MaxRowsLVHeader AS (
            SELECT 
                l.KeySearch, 
                l.LVHeaderKey,
                l.CancelHeaderKey,
                l.DocDate,
                ROW_NUMBER() OVER (PARTITION BY l.KeySearch ORDER BY l.LVHeaderKey DESC) AS rn
            FROM LoyaltyValue.dbo.LVHeader l 
        )
        select 
            ml.KeySearch, 
            ml.LVHeaderKey as salesTransactionId,
            ml.CancelHeaderKey,
            ml.DocDate as DocDate
        INTO {incremental_tables["lv_header"]}
        from MaxRowsLVHeader ml
        where ml.rn = 1 and {incremental_date_condition("ml.DocDate", LS_INCREMENTAL_DATE)};
        CREATE INDEX temp_lv_header_incremental_key_search_idx ON {incremental_tables["lv_header"]} (KeySearch);
        CREATE INDEX temp_lv_header_incremental_lv_header_key_idx ON {incremental_tables["lv_header"]} (salesTransactionId);
        """
    )

    newmember_handler = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)
    newmember_connection = newmember_handler.hook.get_conn()

    try:
        # SalesStatus = 'R'
        _prepare_temp_table(
            handler=newmember_handler,
            connection=newmember_connection,
            query_string=create_smc_sales_header_temp_table,
            source_table="SMCSalesHeader (SalesStatus = 'R')",
        )

        # SalesStatus != 'R'
        _prepare_temp_table(
            handler=newmember_handler,
            connection=newmember_connection,
            query_string=create_smc_sales_header_tnx_temp_table,
            source_table="SMCSalesHeader (SalesStatus != 'R')",
        )

        _prepare_temp_table(
            handler=newmember_handler,
            connection=newmember_connection,
            query_string=create_lv_header_temp_table,
            source_table="LVHeader",
        )

    finally:
        newmember_connection.close()


def drop_table_query(table: str) -> str:
    return f"DROP TABLE {table};"


def _drop_temp_table(
    handler: any, connection: any, table_name: str, source_table: str
) -> None:
    logger.info(f"started dropping {source_table} temp table for migration...")
    handler.execute_query_string(
        connection=connection,
        query_string=drop_table_query(table_name),
    )
    logger.info(f"finished dropping {source_table} temp table for migration.")


def drop_temp_refund_tables(is_full_dump: bool) -> None:
    newmember_handler = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)
    newmember_connection = newmember_handler.hook.get_conn()

    tables = _get_table_names(full_dump=is_full_dump)

    try:
        _drop_temp_table(
            handler=newmember_handler,
            connection=newmember_connection,
            table_name=tables["header"],
            source_table="SMCSalesHeader",
        )

        _drop_temp_table(
            handler=newmember_handler,
            connection=newmember_connection,
            table_name=tables["header_tnx"],
            source_table="SMCSalesHeader",
        )

        _drop_temp_table(
            handler=newmember_handler,
            connection=newmember_connection,
            table_name=tables["lv_header"],
            source_table="LVHeader",
        )

    finally:
        newmember_connection.close()


def mapping_data_with_matched_lvheader(
    offset: int,
    full_dump: bool,
    batch_size: int,
) -> pd.DataFrame:
    mssql = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)
    temp_postgres = PostgresHandler(conn_id=TEMP_CONN_ID)

    tables = _get_table_names(full_dump)

    query = f"""
    select 
        ssh2.memberId,
        'FULL_REFUND' as type,
        l.salesTransactionId,
        ssh.externalId, 
        ssh.refundedAt,
        ssh.createdAt,
        ssh.updatedAt
    from {tables["header"]} ssh 
    join {tables["header_tnx"]} ssh2 on ssh.RecvKeySearch = ssh2.key_search
    join {tables["lv_header"]} l on l.KeySearch = ssh.RecvKeySearch
    where l.CancelHeaderKey IS NULL
    order by l.salesTransactionId, ssh.externalId
    offset {offset} ROWS FETCH NEXT {batch_size} ROWS ONLY;
    """
    df = get_df(query, mssql)

    # Convert the integer to a string in order to merge dataframe on salesTransactionId
    df["salesTransactionId"] = df["salesTransactionId"].astype(str)

    # get refundAmount, revokeAccumSpendableAmount from loyalty_service.SalesTransaction
    salestrans_ids = df["salesTransactionId"].to_list()  # id in SalesTransaction
    query = f"""
        select 
            id as "salesTransactionId", 
            "netTotalAmount" as "refundAmount", 
            "totalAccumSpendableAmount" as "revokeAccumSpendableAmount"
        from loyalty_service."SalesTransaction"
        where id in ({", ".join([f"'{id}'"for id in salestrans_ids])})
    """
    df2 = get_df(query, temp_postgres)
    df2["revokeAccumSpendableAmount"] = df2["revokeAccumSpendableAmount"].apply(
        lambda x: -abs(x)
    )
    df = pd.merge(df, df2, how="inner", on="salesTransactionId")

    # generate ulid
    df["id"] = df["salesTransactionId"].apply(lambda _: generate_ulid())

    return df


def mapping_data_with_unmatched_lvheader(
    offset: int,
    full_dump: bool,
    batch_size: int,
) -> pd.DataFrame:
    mssql = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)
    temp_postgres = PostgresHandler(conn_id=TEMP_CONN_ID)

    tables = _get_table_names(full_dump)

    query = f"""
    select 
        ssh2.memberId,
        'FULL_REFUND' as type,
        ssh.externalId, 
        ssh.RecvKeySearch,
        ssh.refundedAt,
        ssh.createdAt,
        ssh.updatedAt
    from {tables["header"]} ssh 
    join {tables["header_tnx"]} ssh2 on ssh.RecvKeySearch = ssh2.key_search
    left join {tables["lv_header"]} l on l.KeySearch = ssh.RecvKeySearch 
    where l.salesTransactionId is NULL
    order by ssh.externalId
    offset {offset} ROWS FETCH NEXT {batch_size} ROWS ONLY;
    """
    df = get_df(query, mssql)

    logger.info(f"1: {len(df)}")

    # generate SalesTransactionId from key_search
    df["salesTransactionId"] = (
        df["RecvKeySearch"].apply(generate_id_via_keysearch).astype(str)
    )

    # get refundAmount, revokeAccumSpendableAmount from loyalty_service.SalesTransaction
    salestrans_ids = df["salesTransactionId"].to_list()  # id in SalesTransaction
    query = f"""
        select 
            id as "salesTransactionId", 
            "netTotalAmount" as "refundAmount", 
            "totalAccumSpendableAmount" as "revokeAccumSpendableAmount"
        from loyalty_service."SalesTransaction"
        where id in ({", ".join([f"'{id}'"for id in salestrans_ids])})
    """
    df2 = get_df(query, temp_postgres)
    df2["revokeAccumSpendableAmount"] = df2["revokeAccumSpendableAmount"].apply(
        lambda x: -abs(x)
    )

    logger.info(f"2: len(df2) :{len(df)}")

    # Find unmatched SalesTransactionIds
    unmatched_ids = set(df["salesTransactionId"].unique()) - set(
        df2["salesTransactionId"].unique()
    )
    if unmatched_ids:
        # Get the externalIds for unmatched records
        unmatched_records = df[df["salesTransactionId"].isin(unmatched_ids)][
            ["salesTransactionId", "externalId"]
        ]
        logger.warning(
            f"Found {len(unmatched_ids)} unmatched SalesTransactionIds with their externalIds:\n{unmatched_records.to_string()}"
        )

    df = pd.merge(df, df2, how="inner", on="salesTransactionId")

    logger.info(f"3: len(df2) :{len(df)}")

    # generate ulid
    df["id"] = [generate_ulid() for _ in range(len(df))]

    # drop unused column
    df.drop(columns=["RecvKeySearch"], inplace=True)

    return df


def get_total_rows_with_matched_lvheader(full_dump: bool) -> int:
    tables = _get_table_names(full_dump)
    query = f"""
    select count(*)
    from {tables["header"]} ssh 
    join {tables["header_tnx"]} ssh2 on ssh.RecvKeySearch = ssh2.key_search
    join {tables["lv_header"]} l on l.KeySearch = ssh.RecvKeySearch
    where l.CancelHeaderKey IS NULL
    """

    mssql = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)
    total_rows = mssql.extract_data(query)[0][0]
    logger.info(f"Total rows: {total_rows}")

    return total_rows


def get_total_rows_with_unmatched_lvheader(full_dump: bool) -> int:
    tables = _get_table_names(full_dump)
    query = f"""
    select count(*)
    from {tables["header"]} ssh 
    join {tables["header_tnx"]} ssh2 on ssh.RecvKeySearch = ssh2.key_search
    left join {tables["lv_header"]} l on l.KeySearch = ssh.RecvKeySearch
    where l.salesTransactionId is NULL
    """

    mssql = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)
    total_rows = mssql.extract_data(query)[0][0]
    logger.info(f"Total rows: {total_rows}")

    return total_rows


def migrate_refund_sale_transaction_data():
    start_time = datetime.now()  # keep record for start time
    full_dump = True

    try:
        # Case 1: LVHeaderKey is not NULL
        logger.info("Starting migrate SalesTransaction with matched LVHeader...")

        total_rows_1 = get_total_rows_with_matched_lvheader(full_dump)
        total_batches = calc_total_batches(total_rows_1, BATCH_SIZE)
        last_batch_size = calc_last_batch_size(total_rows_1, BATCH_SIZE)
        last_successful_batch = get_last_successful_batch(TABLE)

        for batch_num in range(last_successful_batch + 1, total_batches):
            log_start_process_batch(TABLE, batch_num, total_batches)

            offset = calc_offset(batch_num, BATCH_SIZE)
            batch_size = (
                last_batch_size
                if is_last_batch(batch_num, total_batches)
                else BATCH_SIZE
            )

            df: pd.DataFrame = mapping_data_with_matched_lvheader(
                offset, full_dump, batch_size
            )

            upsert_data_without_encrypt(
                df=df,
                table=TABLE,
                conflict_target=["salesTransactionId", "externalId"],
            )

            set_last_successful_batch(TABLE, batch_num)
            log_success_process_batch(
                TABLE, batch_num, total_batches, batch_size, len(df)
            )

        logger.info(f"destination count: {destination_count(TABLE)}")

        # Case 2: LVHeaderKey is NULL
        logger.info("Starting migrate SalesTransaction with unmatched LVHeader...")

        total_rows_2 = get_total_rows_with_unmatched_lvheader(full_dump)
        total_batches = calc_total_batches(total_rows_2, BATCH_SIZE)
        last_batch_size = calc_last_batch_size(total_rows_2, BATCH_SIZE)
        last_successful_batch = get_last_successful_batch(f"{TABLE}2")

        for batch_num in range(last_successful_batch + 1, total_batches):
            log_start_process_batch(
                f"{TABLE}  (For unmatched LVHeader)", batch_num, total_batches
            )

            offset = calc_offset(batch_num, BATCH_SIZE)
            batch_size = (
                last_batch_size
                if is_last_batch(batch_num, total_batches)
                else BATCH_SIZE
            )

            df: pd.DataFrame = mapping_data_with_unmatched_lvheader(
                offset, full_dump, batch_size
            )

            upsert_data_without_encrypt(
                df=df,
                table=TABLE,
                conflict_target=["salesTransactionId", "externalId"],
            )

            set_last_successful_batch(f"{TABLE}2", batch_num)
            log_success_process_batch(
                f"{TABLE} (For unmatched LVHeader)",
                batch_num,
                total_batches,
                batch_size,
                len(df),
            )

        logger.info(f"destination count: {destination_count(TABLE)}")

        reset_last_successful_batch(TABLE)
        reset_last_successful_batch(f"{TABLE}2")

    except Exception as err:
        logger.error(f"An error has occured: {err}")
        traceback.print_exc()
        raise err

    else:
        dest_count = destination_count(TABLE)
        log_successfully_migrated_data(TABLE, total_records=dest_count)

    finally:
        dest_count = destination_count(TABLE)
        save_migration_result(
            full_dump=full_dump,
            source_table="SMCSalesHeader",
            table=TABLE,
            source_count=total_rows_1 + total_rows_2,
            dest_count=dest_count,
            created_at=start_time,
        )


def incremental_migrate_refund_sale_transaction_data():
    start_time = datetime.now()  # keep record for start time
    full_dump = False

    try:
        dest_count = 0

        # Case 1: LVHeaderKey is not NULL
        logger.info("Starting migrate SalesTransaction with matched LVHeader...")

        total_rows_1 = get_total_rows_with_matched_lvheader(full_dump)
        total_batches = calc_total_batches(total_rows_1, BATCH_SIZE)
        last_batch_size = calc_last_batch_size(total_rows_1, BATCH_SIZE)

        for batch_num in range(0, total_batches):
            log_start_process_batch(TABLE, batch_num, total_batches)

            offset = calc_offset(batch_num, BATCH_SIZE)
            batch_size = (
                last_batch_size
                if is_last_batch(batch_num, total_batches)
                else BATCH_SIZE
            )

            df: pd.DataFrame = mapping_data_with_matched_lvheader(
                offset, full_dump, batch_size
            )

            upsert_data_without_encrypt(
                df=df,
                table=TABLE,
                conflict_target=["salesTransactionId", "externalId"],
            )

            total_records = len(df)
            dest_count += total_records

            log_success_process_batch(
                TABLE, batch_num, total_batches, batch_size, total_records
            )

        # Case 2: LVHeaderKey is NULL
        logger.info("Starting migrate SalesTransaction with unmatched LVHeader...")

        total_rows_2 = get_total_rows_with_unmatched_lvheader(full_dump)
        total_batches = calc_total_batches(total_rows_2, BATCH_SIZE)
        last_batch_size = calc_last_batch_size(total_rows_2, BATCH_SIZE)

        for batch_num in range(0, total_batches):
            log_start_process_batch(
                f"{TABLE} (For unmatched LVHeader)", batch_num, total_batches
            )

            offset = calc_offset(batch_num, BATCH_SIZE)
            batch_size = (
                last_batch_size
                if is_last_batch(batch_num, total_batches)
                else BATCH_SIZE
            )

            df: pd.DataFrame = mapping_data_with_unmatched_lvheader(
                offset, full_dump, batch_size
            )

            upsert_data_without_encrypt(
                df=df,
                table=TABLE,
                conflict_target=["salesTransactionId", "externalId"],
            )

            total_records = len(df)
            dest_count += total_records

            log_success_process_batch(
                f"{TABLE} (For unmatched LVHeader)",
                batch_num,
                total_batches,
                batch_size,
                total_records,
            )

    except Exception as err:
        logger.error(f"An error has occured: {err}")
        traceback.print_exc()
        raise err

    else:
        log_successfully_migrated_data(TABLE, total_records=dest_count)

    finally:
        save_migration_result(
            full_dump=full_dump,
            source_table="SMCSalesHeader",
            table=TABLE,
            source_count=total_rows_1 + total_rows_2,
            dest_count=dest_count,
            created_at=start_time,
            incremental_date=get_incremental_date(LS_INCREMENTAL_DATE),
        )


# full dump dag
with DAG(
    ls_dag_name(TABLE, full_dump=True),
    description=f"Migrate data of {TABLE} Table",
    schedule_interval=None,
    start_date=datetime(2023, 1, 1),
    catchup=False,
    tags=["loyalty_service", "full_dump", "refund_sales_transaction"],
) as dag:

    create_migration_result_table_task = PythonOperator(
        task_id=f"migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    create_temp_table_task = PythonOperator(
        task_id=f"create_temp_table_task",
        python_callable=create_temp_refund_table,
        op_kwargs={"is_full_dump": True},
    )

    mapping_data_task = PythonOperator(
        task_id=f"mapping_refundsalestransaction_data",
        python_callable=migrate_refund_sale_transaction_data,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    drop_temp_tables_task = PythonOperator(
        task_id=f"drop_temp_table_task",
        python_callable=drop_temp_refund_tables,
        op_kwargs={"is_full_dump": True},
    )

    create_migration_result_table_task >> mapping_data_task
    create_temp_table_task >> mapping_data_task
    mapping_data_task >> drop_temp_tables_task


# incremental dag
with DAG(
    ls_dag_name(TABLE, full_dump=False),
    description=f"Incremental migrate data of {TABLE} Table",
    schedule_interval=None,
    start_date=datetime(2023, 1, 1),
    catchup=False,
    tags=["loyalty_service", "incremental", "refund_sales_transaction"],
) as dag:

    create_migration_result_table_task = PythonOperator(
        task_id=f"migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    create_temp_refund_table_task = PythonOperator(
        task_id=f"create_temp_refund_table_task",
        python_callable=create_temp_refund_table,
        op_kwargs={"is_full_dump": False},
    )

    migrate_refund_sales_transaction_task = PythonOperator(
        task_id=f"migrate_refund_sales_transaction_data",
        python_callable=incremental_migrate_refund_sale_transaction_data,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    drop_temp_tables_task = PythonOperator(
        task_id=f"drop_temp_table_task",
        python_callable=drop_temp_refund_tables,
        op_kwargs={"is_full_dump": False},
    )

    (
        create_migration_result_table_task
        >> create_temp_refund_table_task
        >> migrate_refund_sales_transaction_task
        >> drop_temp_tables_task
    )
