from airflow import DAG
from datetime import datetime, timedelta
from airflow.operators.python import PythonOperator
from common_helpers.utils import (
    rename_columns,
    generate_ulid,
    create_mapping_df,
    incremental_date_condition,
    get_last_successful_batch,
    set_last_successful_batch,
    reset_last_successful_batch,
    cast_nvarchar,
    create_migration_result_table,
    upsert_data_without_encrypt,
    save_migration_result,
    destination_count,
    ls_dag_name,
    log_successfully_migrated_data,
    log_start_process_batch,
    log_success_process_batch,
    calc_offset,
    calc_last_batch_size,
    calc_total_batches,
    is_last_batch,
    get_incremental_date,
    get_df,
    full_dump_date_condition,
)
import pandas as pd
from common_helpers.database_services import MSSQLHandler
from constants import (
    NEWMEMBER_CONN_ID,
    BATCH_SIZE,
    LS_INCREMENTAL_DATE,
    LS_FULLDUMP_DATE,
)
from common_helpers.logging import get_logger

logger = get_logger()


SERVICE = "Loyalty Service"
TABLE = "MemberLegacyTierHistory"


def get_full_dump_condition() -> str:
    return f"{full_dump_date_condition('dm.add_datetime', LS_FULLDUMP_DATE)} AND {full_dump_date_condition('dc.add_datetime', LS_FULLDUMP_DATE)}"


def get_daily_condition() -> str:
    return f'({incremental_date_condition("dc.add_datetime", LS_INCREMENTAL_DATE)} OR {incremental_date_condition("dc.update_datetime", LS_INCREMENTAL_DATE)})'


def mapping_data(
    offset: int,
    full_dump: bool,
    batch_size: int,
    field_df: pd.DataFrame,
) -> pd.DataFrame:
    mssql = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)

    # 1. source table: df_cardhist
    table = "df_cardhist"
    source_fields = field_df[field_df["SMC Table"] == table]["SMC Field"].to_list()
    source_fields = [f"dc.{f}" for f in source_fields]

    migration_condition = f"CardTypeCode = 'LV' and dm.del_flag = ' '"
    if full_dump:
        migration_condition = f"{migration_condition} and {get_full_dump_condition()}"
    else:
        migration_condition = f"{migration_condition} and {get_daily_condition()}"

    query = f"""
        SELECT card_status, reason_id, runno as source_runno, "CardTypeCode" as source_cardtypecode, {", ".join(source_fields)} 
        FROM {table} dc LEFT JOIN df_member dm ON dc.member_id = dm.member_id 
        WHERE {migration_condition} 
        ORDER BY dc.add_datetime, dc.member_id, dc.runno, dc."CardTypeCode"
        OFFSET {offset} ROWS FETCH NEXT {batch_size} ROWS ONLY;
    """
    df = get_df(query, mssql)

    # Apply .str.strip() to all string columns
    df = df.map(lambda x: x.strip() if isinstance(x, str) else x)

    # df["start_date"] = df["start_date"].apply(convert_bangkok_to_utc)
    # df["end_date"] = df["end_date"].apply(convert_bangkok_to_utc)

    # 2. source table: mst_card_type
    query = "SELECT card_type_code, description FROM mst_card_type"
    ctype_df = get_df(query, mssql)

    ctype_df = ctype_df.map(lambda x: x.strip() if isinstance(x, str) else x)
    df = df.merge(
        ctype_df, left_on="card_type_id", right_on="card_type_code", how="left"
    )

    # 3. source table: mst_reason
    query = f"SELECT reason_id, {cast_nvarchar('reason_desc')} FROM mst_reason"
    reason_df = get_df(query, mssql)

    reason_df = reason_df.map(lambda x: x.strip() if isinstance(x, str) else x)
    df = df.merge(reason_df, on="reason_id", how="left")

    # 4. source table: MAST_CardStatus
    query = "SELECT StatusName, CardStatus FROM MAST_CardStatus"
    status_df = get_df(query, mssql)
    df = df.merge(status_df, left_on="card_status", right_on="CardStatus", how="left")

    df["id"] = [generate_ulid() for _ in range(len(df))]

    df.drop(
        columns=["card_status", "reason_id", "card_type_code", "CardStatus"],
        axis=1,
        inplace=True,
    )

    # rename columns
    df = rename_columns(df, field_df)

    return df


def get_total_rows(full_dump: bool) -> int:

    query = """
        select count(*) from df_cardhist dc 
        JOIN df_member dm ON dc.member_id = dm.member_id 
        where dm.del_flag = ' ' AND dc.CardTypeCode IN ('LV')
    """

    if full_dump:
        query = f"{query} and {get_full_dump_condition()}"
    else:
        query = f"{query} and {get_daily_condition()}"

    mssql = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)
    return mssql.extract_data(query)[0][0]


def mapping_tier_history_data():
    start_time = datetime.now()  # keep record for start time
    full_dump = True

    try:
        total_rows = get_total_rows(full_dump)
        total_batches = calc_total_batches(total_rows, BATCH_SIZE)
        last_batch_size = calc_last_batch_size(total_rows, BATCH_SIZE)
        last_successful_batch = get_last_successful_batch(TABLE)

        field_df = create_mapping_df(service=SERVICE, table=TABLE)[
            ["Field", "SMC Table", "SMC Field"]
        ]

        for batch_num in range(last_successful_batch + 1, total_batches):
            log_start_process_batch(TABLE, batch_num, total_batches)

            offset = calc_offset(batch_num, BATCH_SIZE)
            batch_size = (
                last_batch_size
                if is_last_batch(batch_num, total_batches)
                else BATCH_SIZE
            )

            df = mapping_data(offset, full_dump, batch_size, field_df)

            upsert_data_without_encrypt(
                df=df,
                table=TABLE,
                conflict_target=["memberId", "source_runno", "source_cardtypecode"],
            )

            set_last_successful_batch(TABLE, batch_num)
            log_success_process_batch(
                TABLE, batch_num, total_batches, batch_size, len(df)
            )

        reset_last_successful_batch(TABLE)

    except Exception as err:
        logger.error(f"An error has occured: {err}")
        raise err

    else:
        dest_count = destination_count(TABLE)
        log_successfully_migrated_data(TABLE, dest_count)

    finally:
        dest_count = destination_count(TABLE)
        save_migration_result(
            full_dump=full_dump,
            source_table="df_cardhist",
            table=TABLE,
            source_count=total_rows,
            dest_count=dest_count,
            created_at=start_time,
        )


def incremental_migrate_tier_history_data():
    start_time = datetime.now()  # keep record for start time
    full_dump = False

    try:
        total_rows = get_total_rows(full_dump)
        total_batches = calc_total_batches(total_rows, BATCH_SIZE)
        last_batch_size = calc_last_batch_size(total_rows, BATCH_SIZE)

        field_df = create_mapping_df(service=SERVICE, table=TABLE)[
            ["Field", "SMC Table", "SMC Field"]
        ]

        dest_count = 0

        for batch_num in range(0, total_batches):
            log_start_process_batch(TABLE, batch_num, total_batches)

            offset = calc_offset(batch_num, BATCH_SIZE)
            batch_size = (
                last_batch_size
                if is_last_batch(batch_num, total_batches)
                else BATCH_SIZE
            )

            df = mapping_data(offset, full_dump, batch_size, field_df)

            upsert_data_without_encrypt(
                df=df,
                table=TABLE,
                conflict_target=["memberId", "source_runno", "source_cardtypecode"],
            )

            total_records = len(df)
            dest_count += total_records

            log_success_process_batch(
                table=TABLE,
                batch_num=batch_num,
                total_batches=total_batches,
                batch_size=batch_size,
                total_records=total_records,
            )

    except Exception as err:
        logger.error(f"An error has occured: {err}")
        raise err

    else:
        log_successfully_migrated_data(TABLE, dest_count)

    finally:
        save_migration_result(
            full_dump=full_dump,
            source_table="df_cardhist",
            table=TABLE,
            source_count=total_rows,
            dest_count=dest_count,
            created_at=start_time,
            incremental_date=get_incremental_date(LS_INCREMENTAL_DATE),
        )


# full dump dag
with DAG(
    ls_dag_name(table=TABLE, full_dump=True),
    description="Migrate data of Member Legacy Tier History Table",
    schedule_interval=None,
    start_date=datetime(2023, 1, 1),
    catchup=False,
    tags=["loyalty_service", "full_dump", "member_legacy_tier_history"],
) as dag:

    create_migration_result_table_task = PythonOperator(
        task_id=f"migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    mapping_data_task = PythonOperator(
        task_id=f"mapping_data",
        python_callable=mapping_tier_history_data,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    create_migration_result_table_task >> mapping_data_task
