import pandas as pd
from airflow import DAG
from datetime import datetime, timedelta
from common_helpers.utils import (
    upsert_data_without_encrypt,
    create_migration_result_table,
    save_migration_result,
    destination_count,
    ls_dag_name,
    log_successfully_migrated_data,
)
from airflow.operators.python import PythonOperator
from common_helpers.logging import get_logger
from common_helpers.database_services import PostgresHandler
from constants import TEMP_CONN_ID

logger = get_logger()

TABLE = "StaffCompany"

# Master data for staff companies
STAFF_COMPANY_DATA = {
    "code": [
        "KPC",
        "KPD",
        "KPDC",
        "KPF1",
        "KPHM",
        "KPM",
        "KPMN",
        "KPS",
        "KPT",
        "V_AND_A",
        "X8",
    ],
    "name": [
        "K<PERSON>",
        "KPD",
        "KPDC",
        "KPF1",
        "KPHM",
        "KPM",
        "KPMN",
        "KPS",
        "KPT",
        "V&A",
        "X8",
    ],
}


def migrate_staff_company_data() -> None:
    start_time = datetime.now()  # keep record for start time

    try:
        df = pd.DataFrame(STAFF_COMPANY_DATA)
        upsert_data_without_encrypt(df=df, table=TABLE, conflict_target=["code"])

    except Exception as err:
        logger.error(f"An error has occured: {err}")
        raise err

    else:
        log_successfully_migrated_data(TABLE, total_records=len(df))

    finally:
        save_migration_result(
            full_dump=True,
            source_table="Staff Master Data sheet",
            table=TABLE,
            source_count=len(df),
            dest_count=destination_count(TABLE),
            created_at=start_time,
        )


def validate_staff_company_migration() -> bool:
    """
    Validates the StaffCompany migration by comparing source and destination data.
    Checks if all records were migrated and if the data matches.
    """
    try:
        # Source data
        source_df = pd.DataFrame(STAFF_COMPANY_DATA)

        # Get destination data
        postgres = PostgresHandler(conn_id=TEMP_CONN_ID)
        dest_query = f'SELECT code, name FROM "loyalty_service"."{TABLE}"'
        dest_df = pd.DataFrame(postgres.extract_data(dest_query), columns=['code', 'name'])

        # Compare counts
        source_count = len(source_df)
        dest_count = len(dest_df)

        # Compare data
        validation_results = {
            "total_records": {
                "source": source_count,
                "destination": dest_count,
                "match": source_count == dest_count,
            },
            "missing_records": [],
            "mismatched_records": [],
        }

        # Check for missing or mismatched records
        for _, source_row in source_df.iterrows():
            dest_row = dest_df[dest_df["code"] == source_row["code"]]
            
            if len(dest_row) == 0:
                validation_results["missing_records"].append({
                    "code": source_row["code"],
                    "name": source_row["name"]
                })
            elif dest_row.iloc[0]["name"] != source_row["name"]:
                validation_results["mismatched_records"].append({
                    "code": source_row["code"],
                    "source_name": source_row["name"],
                    "dest_name": dest_row.iloc[0]["name"]
                })

        # Log validation results
        logger.info("=== StaffCompany Validation Results ===")
        logger.info(f"Total Records:")
        logger.info(f"  Source: {source_count}")
        logger.info(f"  Destination: {dest_count}")
        logger.info(f"  Match: {'Yes' if source_count == dest_count else 'No'}")
        
        if validation_results["missing_records"]:
            logger.warning(f"\nMissing Records ({len(validation_results['missing_records'])}):")
            for record in validation_results["missing_records"]:
                logger.warning(f"  Code: {record['code']}, Name: {record['name']}")
        
        if validation_results["mismatched_records"]:
            logger.warning(f"\nMismatched Records ({len(validation_results['mismatched_records'])}):")
            for record in validation_results["mismatched_records"]:
                logger.warning(f"  Code: {record['code']}")
                logger.warning(f"    Source Name: {record['source_name']}")
                logger.warning(f"    Destination Name: {record['dest_name']}")
        
        if not validation_results["missing_records"] and not validation_results["mismatched_records"]:
            logger.info("No discrepancies found. All records match perfectly!")

        return True

    except Exception as err:
        logger.error(f"An error occurred during validation: {err}")
        return False


with DAG(
    ls_dag_name(table=TABLE, full_dump=True),
    description="Migrate data from source to Staff Company Table",
    schedule_interval=None,
    start_date=datetime(2023, 1, 1),
    catchup=False,
    tags=["loyalty_service", "full_dump", "staff_company"],
) as dag:

    create_migration_result_table_task = PythonOperator(
        task_id=f"migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    mapping_data_task = PythonOperator(
        task_id=f"mapping_staff_company_data",
        python_callable=migrate_staff_company_data,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    validate_migration_task = PythonOperator(
        task_id="validate_staff_company_migration",
        python_callable=validate_staff_company_migration,
        retries=1,
        retry_delay=timedelta(minutes=5),
    )

    create_migration_result_table_task >> mapping_data_task >> validate_migration_task
