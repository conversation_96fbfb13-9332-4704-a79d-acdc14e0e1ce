import pandas as pd
from typing import List, Dict, Tuple
from airflow.providers.postgres.hooks.postgres import <PERSON>gresHook
from common_helpers.database_services import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MSSQLHandler
from common_helpers.utils import (
    row_count,
    destination_count,
    get_logger,
    get_field_df,
    calc_total_batches,
    calc_offset,
    get_df,
)
from common_helpers.utils_member import (
    email_cleansing_df,
    get_subprogram_mapping,
    get_phonecode_df,
)
from common_helpers.utils_memberprofile import get_country_mapping
from constants import (
    NEWMEMBER_CONN_ID,
    TEMP_CONN_ID,
    BATCH_SIZE,
    EMAIL_MAPPING_PATH,
    SUBPROGRAM_PATH,
)

logger = get_logger()
SERVICE = "Loyalty Service"
TABLE = "Member"

def check_record_counts() -> Dict[str, int]:
    """
    Check the number of records in source and destination
    Returns a dictionary with source and destination counts
    """
    mssql = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)
    
    # Get source count (only non-deleted records)
    source_count = row_count(
        "member_id", "df_member", "del_flag = ''"
    )
    
    # Get destination count
    dest_count = destination_count(TABLE)
    
    result = {
        'source_count': source_count,
        'destination_count': dest_count,
        'is_matching': source_count == dest_count
    }
    
    if result['is_matching']:
        logger.info(f"Count check passed: {source_count:,} records in both source and destination")
    else:
        logger.warning(f"⚠️ Count mismatch detected:")
        logger.warning(f"   Source records: {source_count:,}")
        logger.warning(f"   Destination records: {dest_count:,}")
        logger.warning(f"   Difference: {abs(source_count - dest_count):,}")
        
    return result


def check_missing_members() -> Dict[str, any]:
    """
    Check for members that exist in source but not in destination and vice versa
    Returns dictionary with lists of missing member IDs and statistics
    """
    mssql = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)
    temp_postgres = PostgresHandler(conn_id=TEMP_CONN_ID)
    
    # Get all member IDs from source (non-deleted records)
    source_query = """
        SELECT member_id 
        FROM df_member 
        WHERE del_flag = ''
    """
    source_df = get_df(source_query, mssql)
    
    # Strip all string columns in source data
    for column in source_df.select_dtypes(include=['object']).columns:
        source_df[column] = source_df[column].astype(str).str.strip()
    
    source_ids = set(source_df['member_id'].tolist())
    
    # Get all member IDs from destination
    dest_query = """
        SELECT "gwlNo" 
        FROM "loyalty_service"."Member"
    """
    dest_df = get_df(dest_query, temp_postgres)
    dest_ids = set(dest_df['gwlNo'].astype(str).tolist())
    
    # Find missing members
    missing_in_dest = source_ids - dest_ids
    missing_in_source = dest_ids - source_ids
    
    result = {
        'missing_in_destination': sorted(list(missing_in_dest)),  # Sort for consistent output
        'missing_in_source': sorted(list(missing_in_source)),     # Sort for consistent output
        'stats': {
            'total_in_source': len(source_ids),
            'total_in_destination': len(dest_ids),
            'count_missing_in_destination': len(missing_in_dest),
            'count_missing_in_source': len(missing_in_source)
        }
    }
    
    # Log findings
    logger.info("\n=== Missing Members Check ===")
    logger.info(f"Total members in source: {result['stats']['total_in_source']:,}")
    logger.info(f"Total members in destination: {result['stats']['total_in_destination']:,}")
    
    if missing_in_dest:
        logger.warning(f"Members missing in destination: {len(missing_in_dest):,}")
        # Log first 10 missing members as example
        for member_id in result['missing_in_destination']:
            logger.warning(f"   Missing in destination: {member_id}")
            
    if missing_in_source:
        logger.warning(f"Members missing in source: {len(missing_in_source):,}")
        # Log first 10 missing members as example
        for member_id in result['missing_in_source']:
            logger.warning(f"   Missing in source: {member_id}")
            
    if not missing_in_dest and not missing_in_source:
        logger.info("✓ No missing members found in either direction")
        
    return result

def check_transformed_data(sample_size: int = 1000) -> Dict[str, any]:
    """
    Check the transformed data between source and destination
    Returns dictionary with validation results for transformed fields
    
    Args:
        sample_size: Number of records to sample for validation (default: 1000)
    """
    mssql = MSSQLHandler(conn_id=NEWMEMBER_CONN_ID)
    temp_postgres = PostgresHandler(conn_id=TEMP_CONN_ID)
    
    # Get mappings
    country_mapping = get_country_mapping()
    subprogram_mapping = get_subprogram_mapping(SUBPROGRAM_PATH)
    ug_mapping, ur_mapping, rc_mapping, rl_mapping = subprogram_mapping
    
    # Get source data with random sampling
    source_query = f"""
        SELECT TOP {sample_size}
            member_id,
            country_code,
            subprogram_id
        FROM df_member 
        WHERE del_flag = ''
        ORDER BY NEWID()
    """
    source_df = get_df(source_query, mssql)
    
    # Get total count for logging
    total_count_query = """
        SELECT COUNT(*) as total
        FROM df_member 
        WHERE del_flag = ''
    """
    total_count = get_df(total_count_query, mssql).iloc[0]['total']
    
    logger.info(f"\nValidating a random sample of {sample_size:,} records out of {total_count:,} total records ({(sample_size/total_count)*100:.1f}%)")
    
    # Strip all string columns in source data
    for column in source_df.select_dtypes(include=['object']).columns:
        source_df[column] = source_df[column].astype(str).str.strip()
    
    # Apply transformations
    source_df['expected_nationality'] = source_df['country_code'].replace(country_mapping).apply(lambda x: x.upper() if x else x)
    source_df['expected_upgrade_group'] = source_df['subprogram_id'].map(ug_mapping).replace({pd.NA: None})
    source_df['expected_upgrade_reason'] = source_df['subprogram_id'].map(ur_mapping).replace({pd.NA: None})
    source_df['expected_registration_channel'] = source_df['subprogram_id'].map(rc_mapping)
    source_df['expected_registration_location'] = source_df['subprogram_id'].map(rl_mapping).replace({pd.NA: 'NA'})
    
    # Get destination data for sampled records
    member_ids = source_df['member_id'].tolist()
    placeholders = ','.join([f"'{id}'" for id in member_ids])
    dest_query = f"""
        SELECT 
            "gwlNo",
            "nationalityCode",
            "upgradeGroupCode",
            "upgradeReasonCode",
            "registrationChannelCode",
            "registrationLocationCode"
        FROM "loyalty_service"."Member"
        WHERE "gwlNo" IN ({placeholders})
    """
    dest_df = get_df(dest_query, temp_postgres)
    
    differences = {
        'nationality_differences': [],
        'upgrade_differences': [],
        'stats': {
            'total_records': total_count,
            'sampled_records': sample_size,
            'sample_percentage': (sample_size/total_count)*100
        }
    }
    
    # Merge dataframes on member_id/gwlNo
    merged_df = source_df.merge(
        dest_df,
        left_on='member_id',
        right_on='gwlNo',
        how='inner',
        suffixes=('_source', '_dest')
    )
    
    # Check nationality code differences (using transformed value)
    nationality_diff_mask = merged_df['expected_nationality'] != merged_df['nationalityCode']
    nationality_diffs = merged_df[nationality_diff_mask]
    for _, row in nationality_diffs.iterrows():
        differences['nationality_differences'].append({
            'member_id': row['member_id'],
            'source_country_code': row['country_code'],
            'expected_nationality': row['expected_nationality'],
            'actual_nationality': row['nationalityCode']
        })
    
    # Check upgrade group and related differences
    upgrade_diff_mask = (
        (merged_df['expected_upgrade_group'] != merged_df['upgradeGroupCode']) |
        (merged_df['expected_upgrade_reason'] != merged_df['upgradeReasonCode']) |
        (merged_df['expected_registration_channel'] != merged_df['registrationChannelCode']) |
        (merged_df['expected_registration_location'] != merged_df['registrationLocationCode'])
    )
    upgrade_diffs = merged_df[upgrade_diff_mask]
    for _, row in upgrade_diffs.iterrows():
        differences['upgrade_differences'].append({
            'member_id': row['member_id'],
            'source_subprogram_id': row['subprogram_id'],
            'upgrade_group': {
                'expected': row['expected_upgrade_group'],
                'actual': row['upgradeGroupCode']
            },
            'upgrade_reason': {
                'expected': row['expected_upgrade_reason'],
                'actual': row['upgradeReasonCode']
            },
            'registration_channel': {
                'expected': row['expected_registration_channel'],
                'actual': row['registrationChannelCode']
            },
            'registration_location': {
                'expected': row['expected_registration_location'],
                'actual': row['registrationLocationCode']
            }
        })
    
    # Add difference statistics
    differences['stats'].update({
        'nationality_differences_count': len(differences['nationality_differences']),
        'upgrade_differences_count': len(differences['upgrade_differences']),
    })
    
    # Log findings
    logger.info("\n=== Transformed Data Check ===")
    logger.info(f"Sample size: {sample_size:,} records ({differences['stats']['sample_percentage']:.1f}% of total)")
    
    if differences['nationality_differences']:
        logger.warning(f"\nNationality code differences found: {len(differences['nationality_differences']):,} ({(len(differences['nationality_differences'])/sample_size)*100:.1f}% of sample)")
        for diff in differences['nationality_differences']:
            logger.warning(
                f"   Member {diff['member_id']}:\n"
                f"      Source country_code: {diff['source_country_code']}\n"
                f"      Expected nationality: {diff['expected_nationality']}\n"
                f"      Actual nationality: {diff['actual_nationality']}"
            )
    
    if differences['upgrade_differences']:
        logger.warning(f"\nUpgrade and registration differences found: {len(differences['upgrade_differences']):,} ({(len(differences['upgrade_differences'])/sample_size)*100:.1f}% of sample)")
        for diff in differences['upgrade_differences']:
            logger.warning(
                f"   Member {diff['member_id']} (Source subprogram_id: {diff['source_subprogram_id']}):"
            )
            if diff['upgrade_group']['expected'] != diff['upgrade_group']['actual']:
                logger.warning(
                    f"      Upgrade Group:\n"
                    f"         Expected: {diff['upgrade_group']['expected']}\n"
                    f"         Actual: {diff['upgrade_group']['actual']}"
                )
            if diff['upgrade_reason']['expected'] != diff['upgrade_reason']['actual']:
                logger.warning(
                    f"      Upgrade Reason:\n"
                        f"         Expected: {diff['upgrade_reason']['expected']}\n"
                        f"         Actual: {diff['upgrade_reason']['actual']}"
                )
            if diff['registration_channel']['expected'] != diff['registration_channel']['actual']:
                logger.warning(
                    f"      Registration Channel:\n"
                        f"         Expected: {diff['registration_channel']['expected']}\n"
                        f"         Actual: {diff['registration_channel']['actual']}"
                )
            if diff['registration_location']['expected'] != diff['registration_location']['actual']:
                logger.warning(
                    f"      Registration Location:\n"
                        f"         Expected: {diff['registration_location']['expected']}\n"
                        f"         Actual: {diff['registration_location']['actual']}"
                )
    
    if not any([differences['nationality_differences'], differences['upgrade_differences']]):
        logger.info("✓ No differences found in transformed data sample")
    
    return differences

def validate_member_migration() -> Dict[str, any]:
    """
    Main validation function that performs comprehensive validation of member migration
    Returns dictionary with validation results
    """
    logger.info("Starting member migration validation...")
    
    validation_results = {
        'count_check': None,
        'missing_members_check': None,
        'transformation_check': None,
        'overall_status': 'Unknown'
    }
    
    try:
        # Step 1: Check record counts
        validation_results['count_check'] = check_record_counts()
        
        # Step 2: Check missing members in both directions
        validation_results['missing_members_check'] = check_missing_members()
        
        # Step 3: Check transformed data
        validation_results['transformation_check'] = check_transformed_data()
        
        # Determine overall status
        has_count_mismatch = not validation_results['count_check']['is_matching']
        has_missing_members = (
            len(validation_results['missing_members_check']['missing_in_destination']) > 0 or
            len(validation_results['missing_members_check']['missing_in_source']) > 0
        )
        has_transform_differences = any(validation_results['transformation_check'].values())
        
        if not has_count_mismatch and not has_missing_members and not has_transform_differences:
            validation_results['overall_status'] = 'Success'
            logger.info("All member migration validations completed successfully!")
        else:
            validation_results['overall_status'] = 'Warning'
            logger.warning(
                "Member migration validation completed with warnings. "
                "Please check the logs for details."
            )
            
    except Exception as e:
        validation_results['overall_status'] = 'Error'
        logger.error(f"Error during member migration validation: {str(e)}")
    
    return validation_results 