# update `accumulateSpending` and `lifeTimeSpending` in "loyalty_service"."Member"
# from the data in "loyalty_service"."SalesTransaction" and "loyalty_service"."RefundSalesTransaction"

# So, we should get updated data in SalesTransaction and RefundSalesTransaction before updating `accumulateSpending` and `lifeTimeSpending` in Member

import numpy as np
from airflow import DAG
from airflow.operators.python import PythonOperator
from common_helpers.database_services import PostgresHandler
from common_helpers.utils import (
    create_migration_result_table,
    insert_migration_result,
    calc_total_batches,
    calc_last_batch_size,
    calc_offset,
    is_last_batch,
    log_start_process_batch,
    log_success_process_batch,
    get_df,
)
from constants import TEMP_CONN_ID, BATCH_SIZE, CUTOFF_DATE_ACCUMULATE_SPENDING
from datetime import datetime, timedelta
from common_helpers.logging import get_logger

# Import validation functions from validation_dag
from loyalty_service.validation_dag import validation_log

def _get_table_names() -> dict:
    return {
        "net_total": "public.temp_net_total_amount",
        "total_accum": "public.temp_total_accum_amount",
        "refund": "public.temp_refund_amount",
        "revoke_accum": "public.temp_revoke_accum_amount"
    }

def _prepare_temp_table(handler: any, connection: any, query_string: str, table: str) -> None:
    logger = get_logger()

    logger.info(f"started preparing {table} temp table for migration...")
    handler.execute_query(query=query_string)
    logger.info(f"finished preparing {table} temp table for migration.")


def drop_table_query(table: str) -> str:
    return f"DROP TABLE {table};"


def _drop_temp_table(handler: any, connection: any, table: str) -> None:
    logger = get_logger()

    logger.info(f"started dropping {table} temp table for migration...")
    handler.execute_query(query=drop_table_query(table))
    logger.info(f"finished dropping {table} temp table for migration.")


def drop_temp_member_spending_tables() -> None:
    tables = _get_table_names()
    temp_postgres = PostgresHandler(conn_id=TEMP_CONN_ID)
    temp_connection = temp_postgres.hook.get_conn()

    try:
        _drop_temp_table(temp_postgres, connection=temp_connection, table=tables["net_total"])
        _drop_temp_table(temp_postgres, connection=temp_connection, table=tables["total_accum"])
        _drop_temp_table(temp_postgres, connection=temp_connection, table=tables["refund"])
        _drop_temp_table(temp_postgres, connection=temp_connection, table=tables["revoke_accum"])

    finally:
        temp_connection.close()

def create_temp_spending_table():
    """
    Prepare indexed temporary tables for SalesTransaction migration.

    Args:
        is_full_dump (bool): The migration type.

    Returns:
        None
    """
    # cutoff date for accummulate spending
    if CUTOFF_DATE_ACCUMULATE_SPENDING:
        cutoff_date = (
            f"date_trunc('month', CAST('{CUTOFF_DATE_ACCUMULATE_SPENDING}' AS DATE) - INTERVAL '2 years')"
        )
    else:
        # default cutoff data date for full dump: yesterday (current date - 1 day)
        cutoff_date = "date_trunc('month', CURRENT_DATE - INTERVAL '1 day' - INTERVAL '2 years')"

    tables = _get_table_names()

    # SalesTransaction
    create_net_total_amount_table = f"""
        DROP TABLE IF EXISTS {tables["net_total"]};

        SELECT 
            st."memberId", 
            COALESCE(SUM(st."netTotalAmount"), 0) AS "netTotalAmount"
        INTO {tables["net_total"]}
        FROM loyalty_service."SalesTransaction" st
        GROUP BY st."memberId";

        CREATE INDEX temp_net_total_amount_member_id_idx ON {tables["net_total"]} ("memberId");
        """

    create_total_accum_amount_table = f"""
        DROP TABLE IF EXISTS {tables["total_accum"]};

        SELECT 
            st."memberId", 
            COALESCE(SUM(st."totalAccumSpendableAmount"), 0) AS "totalAccumSpendableAmount"
        INTO {tables["total_accum"]}
        FROM loyalty_service."SalesTransaction" st
        WHERE st."createdAt" >= {cutoff_date}
        GROUP BY st."memberId";

        CREATE INDEX temp_total_accum_amount_member_id_idx ON {tables["total_accum"]} ("memberId");
    """

    # RefundSalesTransaction
    create_refund_amount_table = f"""
        DROP TABLE IF EXISTS {tables["refund"]};

        SELECT
            rst."memberId",
            SUM(rst."refundAmount") AS "refundAmount"
        INTO {tables["refund"]}
        FROM loyalty_service."RefundSalesTransaction" rst
        GROUP BY rst."memberId";

        CREATE INDEX temp_refund_amount_member_id_idx ON {tables["refund"]} ("memberId");
    """

    create_revoke_accum_amount_table = f"""
        DROP TABLE IF EXISTS {tables["revoke_accum"]};

        SELECT 
            rst."memberId", 
            SUM(rst."revokeAccumSpendableAmount") AS "revokeAccumSpendableAmount"
        INTO {tables["revoke_accum"]}
        FROM loyalty_service."RefundSalesTransaction" rst
        WHERE rst."createdAt" >= {cutoff_date}
        GROUP BY rst."memberId";

        CREATE INDEX temp_revoke_accum_amount_member_id_idx ON {tables["revoke_accum"]} ("memberId");
    """

    temp_postgres = PostgresHandler(conn_id=TEMP_CONN_ID)
    temp_connection = temp_postgres.hook.get_conn()

    try:
        _prepare_temp_table(temp_postgres, temp_connection, create_net_total_amount_table, tables["net_total"])
        _prepare_temp_table(temp_postgres, temp_connection, create_total_accum_amount_table, tables["total_accum"])
        _prepare_temp_table(temp_postgres, temp_connection, create_refund_amount_table, tables["refund"])
        _prepare_temp_table(temp_postgres, temp_connection, create_revoke_accum_amount_table, tables["revoke_accum"])

    finally:
        temp_connection.close()


def update_query(
    member_id: str,
    accumulate_spending: float,
    lifetime_spending: float,
) -> str:
    """
    Query for update `lifeTimeSpending` and `accumulateSpending` of member
    """
    query = f"""
    UPDATE "loyalty_service"."Member"
    SET 
        "accumulateSpending" = {accumulate_spending}, 
        "lifeTimeSpending" = {lifetime_spending}
    WHERE "id" = '{member_id}';
    """

    return query


def reset_member_spending() -> str:
    """
    Set `lifeTimeSpending` and `accumulateSpending` to 0 for all member
    """
    query = f"""
    UPDATE "loyalty_service"."Member"
    SET 
        "accumulateSpending" = 0, 
        "lifeTimeSpending" = 0;
    """
    PostgresHandler(conn_id=TEMP_CONN_ID).execute_query(query)
    logger = get_logger()
    logger.info(
        "Set both accumulateSpending and lifeTimeSpending to 0 for all members in the Member table."
    )


def update_member_spending_in_db(df, conn):
    # Loop through DataFrame and execute an update query for each row
    for _, row in df.iterrows():
        query = update_query(
            row["memberId"],
            row["accumulateSpending"],
            row["lifeTimeSpending"],
        )

        with conn.cursor() as cursor:
            cursor.execute(query)

    conn.commit()


def get_total_rows() -> int:
    tables = _get_table_names()
    query = f"SELECT COUNT(*) FROM {tables['net_total']};"
    temp_postgres = PostgresHandler(conn_id=TEMP_CONN_ID)
    return temp_postgres.extract_data(query)[0][0]


def update_member_spending():
    """
    Update `accumulateSpending` and `lifeTimeSpending` data for all data in Member Table
    """
    start_time = datetime.now()  # keep record for start time
    tables = _get_table_names()
    logger = get_logger()
    temp_postgres = PostgresHandler(conn_id=TEMP_CONN_ID)

    total_rows = get_total_rows()
    total_batches = calc_total_batches(total_rows, BATCH_SIZE)
    last_batch_size = calc_last_batch_size(total_rows, BATCH_SIZE)

    dest_count = 0

    # set member spending to 0
    reset_member_spending()

    for batch_num in range(0, total_batches):
        log_start_process_batch("Member", batch_num, total_batches)

        offset = calc_offset(batch_num, BATCH_SIZE)
        batch_size = (
            last_batch_size if is_last_batch(batch_num, total_batches) else BATCH_SIZE
        )

        # SalesTransaction
        query = f"""
            SELECT 
                nt."memberId", 
                nt."netTotalAmount",
                ta."totalAccumSpendableAmount",
                ra."refundAmount",
                raa."revokeAccumSpendableAmount"
            FROM {tables["net_total"]} nt
            LEFT JOIN {tables["total_accum"]} ta ON nt."memberId" = ta."memberId"
            LEFT JOIN {tables["refund"]} ra ON nt."memberId" = ra."memberId"
            LEFT JOIN {tables["revoke_accum"]} raa ON nt."memberId" = raa."memberId"
            ORDER BY nt."memberId" OFFSET {offset} ROWS FETCH NEXT {batch_size} ROWS ONLY;
        """
        df = get_df(query, temp_postgres)
        df.replace(np.nan, 0, inplace=True)
        df["lifeTimeSpending"] = df["netTotalAmount"] - df["refundAmount"]
        df["accumulateSpending"] = (
            df["totalAccumSpendableAmount"] + df["revokeAccumSpendableAmount"]
        )

        conn = temp_postgres.hook.get_conn()
        update_member_spending_in_db(df=df, conn=conn)

        total_records = len(df)
        dest_count += total_records

        logger.info(
            f"Successfully updated `accumulateSpending` and `lifeTimeSpending` in Member (total rows: {len(df)})"
        )

        log_success_process_batch(
            table="Member",
            batch_num=batch_num,
            total_batches=total_batches,
            batch_size=batch_size,
            total_records=total_records,
        )

    # calculate `validation_result`
    source_count = total_rows
    if source_count == 0 and dest_count == 0:
        validation_result = 100
    else:
        validation_result = dest_count / source_count * 100

    insert_migration_result(
        postgresql_handler=temp_postgres,
        dag_name="loyalty_service_update_accumulate_spending",
        migration_type="FULL_DUMP",
        source_table="df_member",
        source_table_count=source_count,
        destination_table="Member",
        destination_table_count=dest_count,
        validation_type="COMPLETENESS",
        validation_result=validation_result,
        created_at=start_time,
    )


with DAG(
    "loyalty_service_update_member_spending",
    description="Update accummulateSpending and lifeTimeSpending in Member Table",
    schedule_interval=None,
    start_date=datetime(2023, 1, 1),
    catchup=False,
    tags=[
        "loyalty_service",
        "update_accumulate_spending",
        "update_life_time_spending",
        "member",
    ],
) as dag:

    create_migration_result_table_task = PythonOperator(
        task_id=f"migration_result_table_task",
        python_callable=create_migration_result_table,
    )

    create_temp_spending_table_task = PythonOperator(
        task_id=f"create_temp_spending_table_task",
        python_callable=create_temp_spending_table,
    )

    update_member_spending_task = PythonOperator(
        task_id="update_member_spending_task",
        python_callable=update_member_spending,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    validation_task = PythonOperator(
        task_id="validation_task",
        python_callable=validation_log,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    drop_temp_tables_task = PythonOperator(
        task_id="drop_temp_member_spending_table_task",
        python_callable=drop_temp_member_spending_tables,
        retries=3,
        retry_delay=timedelta(minutes=5),
    )

    (
        create_migration_result_table_task
        >> create_temp_spending_table_task
        >> update_member_spending_task
        >> validation_task
        >> drop_temp_tables_task
    )
