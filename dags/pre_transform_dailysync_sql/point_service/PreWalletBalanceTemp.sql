WITH member_nocashwallet AS 
(
--self join but filter on walletcode= 'CASH_WALLET'
--if this is no member it will insert nothing
SELECT 
max(wb.id) as id,
(wb."memberId") as "memberId"

FROM staging_point_service."WalletBalance" wb
INNER JOIN staging_loyalty_service."Member" m on wb."memberId" = m."gwlNo"
LEFT JOIN staging_point_service."WalletBalanceTemp" wbt ON wb."memberId" = wbt."memberId" 
    AND wbt."walletCode" = 'CASH_WALLET'
WHERE wbt."memberId" IS null
group by wb."memberId"
)
--select count(*) from member_nocashwallet -- 454261 no cash wallet

INSERT INTO staging_point_service."PreWalletBalanceTemp" (id, "memberId", "walletCode", amount, "createdAt", "updatedAt", "expiredAt")
SELECT
    concat(id,'_',"memberId",'_','CASH_WALLET') as id,
    m."memberId",
    'CASH_WALLET',
    0.00,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    NULL
FROM member_nocashwallet AS m
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "walletCode" = EXCLUDED."walletCode",
    amount = EXCLUDED.amount,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "expiredAt" = EXCLUDED."expiredAt";

 