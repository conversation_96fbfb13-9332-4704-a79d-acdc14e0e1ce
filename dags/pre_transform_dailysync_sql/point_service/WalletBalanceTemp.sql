-- CREATE TABLE IF NOT EXISTS staging_point_service."WalletBalanceTemp" (
-- 	id text NOT NULL,
-- 	"memberId" text NOT NULL,
-- 	"walletCode" text NOT NULL,
-- 	amount numeric(16, 2) DEFAULT 0.00 NOT NULL,
-- 	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
-- 	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
-- 	"expiredAt" timestamp(3) NULL,
-- 	CONSTRAINT "WalletBalanceTemp_pkey" PRIMARY KEY (id)
-- );

INSERT INTO staging_point_service."WalletBalanceTemp" (
    "id",
    "memberId",
    "walletCode",
    "amount",
    "createdAt",
    "updatedAt",
    "expiredAt"
)
SELECT
    id ,
	"memberId" ,
	"walletCode" ,
	CASE WHEN wb."walletCode" = 'CARAT_WALLET' then amount*4
         ELSE amount
    END AS amount,
	"createdAt" ,
	"updatedAt" ,
	CASE 
        WHEN wb."walletCode" = 'CARAT_WALLET' and EXTRACT(YEAR FROM wb."expiredAt") = 2099 THEN NULL
        WHEN wb."walletCode" = 'CARAT_WALLET' and wb."expiredAt" > now() 
            THEN TO_TIMESTAMP(EXTRACT(YEAR FROM wb."expiredAt") || '-12-31 16:59:59.999', 'YYYY-MM-DD HH24:MI:SS.MS')
        ELSE wb."expiredAt"
    END AS "expiredAt" 
FROM (SELECT * FROM staging_point_service."WalletBalance"
        UNION ALL 
        SELECT * FROM staging_point_service."PreWalletBalanceTemp") AS wb

WHERE ((wb."createdAt" BETWEEN 'start_timestamps' AND 'end_timestamps') OR (wb."updatedAt" BETWEEN 'start_timestamps' AND 'end_timestamps'))


ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "walletCode" = EXCLUDED."walletCode",
    amount = EXCLUDED.amount,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "expiredAt" = EXCLUDED."expiredAt";
