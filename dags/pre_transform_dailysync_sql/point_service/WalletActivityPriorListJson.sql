-- staging_point_service."WalletActivityPriorListJson" definition

-- Drop table

-- DROP TABLE staging_point_service."WalletActivityPriorListJson";

CREATE TABLE IF NOT EXISTS staging_point_service."WalletActivityPriorListJson" (
	id text NOT NULL,
	"WalletActivityId" text NULL,
	transaction_json_values jsonb NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "WalletActivityPriorListJson_pkey" PRIMARY KEY (id)
);

WITH stg_waplj AS 
(
SELECT 
    concat(wa.id,'_',wt.id) AS id,
	wa.id AS "WalletActivityId",
	COALESCE(jsonb_build_object(
            'WalletTransactionId', wt.id,
            'walletActivityId', wt."walletActivityId",
            'amount', wt.amount,
            'expiredAt', to_char(wt."expiredAt", 'YYYY-MM-DD HH24:MI:SS.MS'),
            'createdAt', to_char(wt."createdAt", 'YYYY-MM-DD HH24:MI:SS.MS')
        )
    ) ::jsonb AS transaction_json_values,
    wa."createdAt" AS "createdAt",
    wa."updatedAt" AS "updatedAt"
   
    
    FROM staging_point_service."WalletActivity" wa 
    INNER JOIN staging_loyalty_service."Member" AS sm     ON wa."memberId" = sm."gwlNo"
    INNER JOIN staging_partner_service."SalesTransaction" AS st     ON wa."refId" = st.id::text
    --inner join public.tiertransformed t on wa."memberId" = t."memberId"
    LEFT JOIN staging_point_service."WalletTransaction" wt	ON  wa.id = wt."walletActivityId"
    WHERE ((wa."createdAt" BETWEEN 'start_timestamps' AND 'end_timestamps') OR (wa."updatedAt" BETWEEN 'start_timestamps' AND 'end_timestamps'))
    --inner join public.tiertransformed t on wa."memberId" = t."memberId"
	--where wa."createdAt" between '2019-08-07' and '2022-10-07'
	--limit 2000
	--or (wa.id = '10000011_PTPOS_CR001_1477405')
	--where wa."refId" ='10000011' 
	--where wt."walletActivityId" = wa.id and wa."refId" ='10000011' 
    --WHERE wa.id = '5426897_USE_CR001_0171078'
    --WHERE wa."createdAt" = '2025-01-01'
)

INSERT INTO staging_point_service."WalletActivityPriorListJson" (
    id,
    "WalletActivityId",
    "transaction_json_values",
    "createdAt",
    "updatedAt"
)
SELECT
    id,
    "WalletActivityId",
    "transaction_json_values",
    "createdAt",
    "updatedAt"
FROM stg_waplj

ON CONFLICT (id) DO UPDATE SET
    "WalletActivityId" = EXCLUDED."WalletActivityId",
    "transaction_json_values" = EXCLUDED."transaction_json_values",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";

-- Updated Rows	23274039 10 min