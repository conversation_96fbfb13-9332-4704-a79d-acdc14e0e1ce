-- modify P_CR order same as N_CR, so, change from 10 to 8
-- have to add at the very first rule: all inactive members are Navy
-- don't consider cobrand if it is inactive


-- create new public.tiertransformed table for join in xform

-- DROP TABLE IF EXISTS public.tiertransformed CASCADE;

CREATE TABLE IF NOT EXISTS public.tiertransformed (
    "memberId" text not null PRIMARY KEY,
    "tierId" text not null DEFAULT 'Navy',
    "minimumTierId" text null,
    "minimumTierInvitedId" text null,
    "accumulateSpending" numeric(16,2) NULL DEFAULT 0.00,
    "isActive" boolean null 
);





-- insert data into public.tiertransformed table

WITH cardtypecode_cobrand AS (
    SELECT 'BVP05' AS cardTypeCode, 5 AS value
    UNION ALL
    SELECT 'SVP05', 5
    UNION ALL
    SELECT 'SVP10', 4
    UNION ALL
    SELECT 'BVS', 3
    UNION ALL
    SELECT 'SVP15', 3
    UNION ALL
    SELECT 'SVP20', 3
    UNION ALL
    SELECT 'STRIB', 2
    UNION ALL
    SELECT 'SPLD', 2
    UNION ALL
    SELECT 'SVP30', 1
)

,
max_cardtypecode_cobrand as 
(SELECT  
	"memberId"
	,"cardTypeCode"
	,value
FROM (select 
		mlch."memberId"
		,mlch."cardTypeCode"
		,cc.value
		,ROW_NUMBER() OVER (PARTITION BY mlch."memberId" ORDER BY cc.value DESC) AS rn
		from
		staging_loyalty_service."MemberLegacyCoBrandHistory" as mlch
		LEFT JOIN
	    cardtypecode_cobrand AS cc ON mlch."cardTypeCode" = cc.cardTypeCode
	    WHERE mlch."cardStatus" = 'ACTIVE' --- modified
		)
WHERE  	rn = 1
	)
--select count(*) from max_cardtypecode_cobrand
--select count(*) from (select distinct "memberId" from max_cardtypecode_cobrand)
,
cardtypecode_tier as
(SELECT 'N_NY' AS cardTypeCode, 10 AS value
    UNION ALL
    SELECT 'N_SL', 9
    UNION ALL
    SELECT 'OV_SL', 9
    UNION ALL
    SELECT 'OT_SL', 9
    UNION ALL
    SELECT 'P_CR', 8 --- modified
    UNION ALL
    SELECT 'N_CR', 8
    UNION ALL
    SELECT 'N_OX', 7
    UNION ALL
    SELECT 'OT_OX', 7
    UNION ALL
    SELECT 'OV_OX', 7
    UNION ALL
    SELECT 'IN_V1', 6
    UNION ALL
    SELECT 'IN_V2', 5
    UNION ALL
    SELECT 'IN_V3', 5
    UNION ALL
    SELECT 'IN_K', 4
    UNION ALL
    SELECT 'VEGA', 3
    UNION ALL
    SELECT 'NVVIP', 2
    UNION ALL
    SELECT 'C_STF', 1
    UNION ALL
    SELECT 'CS_MS', 1
    )
,
max_cardtypecode_tier as

(SELECT 
	"memberId",
    "cardTypeCode",
    value
FROM 	(select
		mlth."memberId"
		,mlth."cardTypeCode"
		,ct.value
		,ROW_NUMBER() OVER (PARTITION BY mlth."memberId" ORDER BY ct.value DESC) AS rn
	
		FROM staging_loyalty_service."MemberLegacyTierHistory" as mlth
		LEFT JOIN
	    cardtypecode_tier AS ct ON mlth."cardTypeCode" = ct.cardTypeCode
        WHERE mlth."cardStatus" = 'ACTIVE' --- modified
		)
WHERE rn = 1
)
--select count(*) from max_cardtypecode_tier
--select count(*) from (select distinct "memberId" from max_cardtypecode_tier)

-- ,
-- pre_accumulateSpending
-- as
-- (
-- SELECT "memberId", SUM("totalEarnableAmount") AS "accumulateSpending"
-- FROM staging_partner_service."SalesTransaction"
-- WHERE "createdAt" > NOW() - INTERVAL '24 months' AND TRIM(status) <> 'R'
-- GROUP BY "memberId"
-- )	
--select count(*) from pre_accumulateSpending


	
,

isactive_member AS (
				select DISTINCT "memberId", true as "isActive"
				from 
					(
                    SELECT "memberId", "cardStatus"
                    FROM staging_loyalty_service."MemberLegacyTierHistory"
                    WHERE "cardStatus" = 'ACTIVE'
                    UNION ALL
                    SELECT "memberId", "cardStatus" 
                    FROM staging_loyalty_service."MemberLegacyCoBrandHistory"
                    WHERE "cardStatus" = 'ACTIVE'
                    )
)
	
,
tiertransformed AS
(SELECT
	m.id AS "memberId",
    CASE
        WHEN isam."isActive" <> true THEN 'Navy'
        WHEN TRIM(mct."cardTypeCode") IN ('C_STF', 'CS_MS') THEN 'Crystal'
        WHEN TRIM(mct."cardTypeCode") IN ('NVVIP') THEN 'VVIP'
        WHEN TRIM(mcc."cardTypeCode") IN ('SVP30') THEN 'VVIP'
        WHEN m."accumulateSpending" >= 2000000 THEN 'Vega'
        WHEN TRIM(UPPER(mct."cardTypeCode")) = 'VEGA' AND m."accumulateSpending" < 2000000 THEN 'Vega(GP)'
        WHEN TRIM(mcc."cardTypeCode") IN ('STRIB', 'SPLD') THEN 'Crown'
        WHEN m."accumulateSpending" >= 300000 AND m."accumulateSpending" < 2000000 THEN 'Crown'
        WHEN TRIM(mct."cardTypeCode") IN ('IN_K') AND mp."occupation" in ('5', '11', '41','49','50') THEN 'Crown(GP)'
        WHEN TRIM(mct."cardTypeCode") IN ('P_CR', 'N_CR', 'N_OX', 'OT_OX', 'OV_OX', 'IN_V1', 'IN_V2', 'IN_V3') THEN 'Crown(GP)'
        WHEN TRIM(mcc."cardTypeCode") IN ('BVS', 'SVP15', 'SVP20') THEN 'Crown(GP)'
        WHEN m."accumulateSpending" >= 40000 AND m."accumulateSpending" < 300000 THEN 'Scarlet'
        WHEN TRIM(mcc."cardTypeCode") IN ('SVP10') THEN 'Scarlet(GP)'
        WHEN m."accumulateSpending" < 40000 AND mct."cardTypeCode" IN ('IN_K', 'N_SL', 'OT_SL', 'OV_SL') THEN 'Scarlet(GP)'
        WHEN m."accumulateSpending" < 40000 AND mct."cardTypeCode" IN ('N_NY') THEN 'Navy'
        ELSE 'Navy'
    END AS "tierId",
    CASE
        WHEN isam."isActive" <> true THEN NULL
        WHEN TRIM(mct."cardTypeCode") IN ('C_STF', 'CS_MS', 'NVVIP') THEN NULL
        WHEN TRIM(mcc."cardTypeCode") IN ('SVP30') THEN NULL
        WHEN TRIM(UPPER(mct."cardTypeCode")) = 'VEGA' THEN 'VEGA'
        WHEN TRIM(mct."cardTypeCode") IN ('IN_K') AND mp."occupation" in ('5', '11', '41','49','50') THEN 'CROWN'
        WHEN TRIM(mct."cardTypeCode") IN ('P_CR', 'N_CR', 'N_OX', 'OT_OX', 'OV_OX', 'IN_V1', 'IN_V2', 'IN_V3') THEN 'CROWN'
        WHEN TRIM(mcc."cardTypeCode") IN ('BVS', 'SVP15', 'SVP20', 'STRIB', 'SPLD') THEN 'CROWN'
        WHEN TRIM(mcc."cardTypeCode") IN ('SVP10') THEN 'SCARLET'
		WHEN mct."cardTypeCode" IN ('IN_K', 'N_SL', 'OT_SL', 'OV_SL') THEN 'SCARLET'
        ELSE NULL
    END AS "minimumTierId",
    CASE
        WHEN isam."isActive" <> true THEN NULL
        WHEN TRIM(mct."cardTypeCode") IN ('C_STF', 'CS_MS', 'NVVIP', 'N_NY') THEN NULL
        WHEN TRIM(UPPER(mct."cardTypeCode")) = 'VEGA' THEN 'VEGA'
        WHEN TRIM(mct."cardTypeCode") IN ('IN_K') AND mp."occupation" in ('5', '11', '41','49','50') THEN 'CROWN'
        WHEN TRIM(mct."cardTypeCode") IN ('P_CR', 'N_CR', 'N_OX', 'OT_OX', 'OV_OX', 'IN_V1', 'IN_V2', 'IN_V3') THEN 'CROWN'
		WHEN mct."cardTypeCode" IN ('IN_K', 'N_SL', 'OT_SL', 'OV_SL') THEN 'SCARLET'
        ELSE NULL
    END AS "minimumTierInvitedId",

    CASE WHEN isam."isActive" = true THEN m."accumulateSpending"
         ELSE 0.00
    END AS "accumulateSpending",
	CASE WHEN isam."isActive" = true THEN isam."isActive" ELSE false END AS "isActive"
FROM
        staging_loyalty_service."Member" AS m
    LEFT JOIN
        staging_loyalty_service."MemberProfile" AS mp ON m.id = mp."memberId" -- for occupation('5', '11', '41','49','50') is doctor
    LEFT JOIN
        max_cardtypecode_tier AS mct ON m.id = mct."memberId"
    LEFT JOIN
        max_cardtypecode_cobrand AS mcc ON m.id = mcc."memberId"
    -- LEFT JOIN
    --     pre_accumulateSpending AS paccs ON m.id = paccs."memberId"
    LEFT JOIN
        isactive_member AS isam ON m.id = isam."memberId"
WHERE ((m."createdAt" BETWEEN 'start_timestamps' AND 'end_timestamps') OR (m."updatedAt" BETWEEN 'start_timestamps' AND 'end_timestamps'))  
)
--    select count(*) from tiertransformed_0
INSERT INTO public.tiertransformed (
    "memberId", 
    "tierId", 
    "minimumTierId",
    "minimumTierInvitedId", 
    "accumulateSpending", 
    "isActive"
)
SELECT 
    "memberId", 
    "tierId", 
    "minimumTierId",
    "minimumTierInvitedId", 
    "accumulateSpending", 
    "isActive"
FROM tiertransformed
ON CONFLICT ("memberId") DO UPDATE SET
    "tierId" = EXCLUDED."tierId",
    "minimumTierId" = EXCLUDED."minimumTierId",
    "minimumTierInvitedId" = EXCLUDED."minimumTierInvitedId",
    "accumulateSpending" = EXCLUDED."accumulateSpending",
    "isActive" = EXCLUDED."isActive";