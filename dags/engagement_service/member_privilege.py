import math
import threading
from concurrent.futures import as_completed, ThreadPoolExecutor
from datetime import datetime

from common_helpers.database_services import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PostgresHandler
from common_helpers.logging import get_logger
from common_helpers.utils import get_query_offsets, insert_migration_result

logger = get_logger()


class MemberPrivilege:
    def __init__(
        self,
        batch_size: int,
        executor_max_workers: int,
        mssql_handler: MSSQLHandler,
        postgresql_handler: <PERSON>gresHandler,
        service_name: str,
    ) -> None:
        self.batch_size = batch_size
        self.executor_max_workers = executor_max_workers
        self.mssql_handler = mssql_handler
        self.postgresql_handler = postgresql_handler
        self.service_name = service_name
        self.YOB = 2025

        self.count_query_string = f"""
            SELECT
                COUNT(*)
            FROM
                snapshot_lv_birthday_28052025 lvb
                JOIN LVTrans lvt ON lvt.LVTransKey = lvb.LVTransKey
                JOIN LVHeader lvh ON lvh.LVHeaderKey = lvt.LVHeaderKey
                JOIN LVData lvd ON lvd.LVMainKey = lvt.LVMainKey
            WHERE "YOB" = {self.YOB};
        """
        self.select_query_string = f"""
            SELECT
                lvb.LVBirthdayKey AS id,
                lvb.LVTransKey AS lv_trans_key,
                lvb.LVNumber AS member_id,
                0 AS is_unlimited,
                DATEADD (HOUR, -7, lvh.AddDT) AS granted_at,
                DATEADD (HOUR, -7, lvh.AddDT) AS created_at,
                DATEADD (HOUR, -7,
                    CASE 
                        WHEN lvh.FinishDT IS NOT NULL THEN lvh.FinishDT
                        ELSE lvh.AddDT
                    END
                ) AS updated_at,
                CASE 
                    WHEN lvd.[ExpireDate] = CAST(lvd.[ExpireDate] AS DATE) 
                    THEN DATEADD(
                        MILLISECOND,
                        61199999, 
                        CAST(lvd.[ExpireDate] AS DATETIME2(3))
                    )
                    ELSE DATEADD (HOUR, -7, lvd.[ExpireDate])
                END AS expired_at,
                'ACTIVE' as status
            FROM snapshot_lv_birthday_28052025 lvb
            JOIN LVTrans lvt ON lvt.LVTransKey = lvb.LVTransKey
            JOIN LVHeader lvh ON lvh.LVHeaderKey = lvt.LVHeaderKey
            JOIN LVData lvd ON lvd.LVMainKey = lvt.LVMainKey
            WHERE
                "YOB" = {self.YOB}
            ORDER BY
                id,
                lv_trans_key
            OFFSET
                %s ROWS
            FETCH NEXT
                %s ROWS ONLY;
        """
        self.destination_insert_query = """
            INSERT INTO "engagement_service"."MemberPrivilege" (
                "id",
                "memberId",
                "privilegeId",
                "isUnlimited",
                "grantedAt",
                "createdAt",
                "updatedAt",
                "expiredAt",
                "status"
            )
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON CONFLICT ("id") DO NOTHING;
        """

    def transform_record(
        self,
        record: tuple,
    ) -> tuple:
        """
        Transform a record queried from source table to destination table schema format.

        Args:
            record (tuple): A record queried from source table.

        Returns:
            tuple: A record in destination table schema format.
        """
        (
            id,
            lv_trans_key,
            member_id,
            is_unlimited,
            granted_at,
            created_at,
            updated_at,
            expired_at,
            status,
        ) = record
        id = f"{id}_{lv_trans_key}" if lv_trans_key else id
        is_unlimited = bool(is_unlimited)

        return (
            id,
            member_id,
            self.service_name,
            is_unlimited,
            granted_at,
            created_at,
            updated_at,
            expired_at,
            status,
        )

    def insert_batch_to_destination(
        self,
        connection,
        batch: list[tuple],
    ) -> None:
        """
        Insert a batch to destination table.

        Args:
            connection (postgres_connection): A PostgreSQL connection.
            batch (list): A list of  records to insert to destination table.

        Returns:
            None
        """
        self.postgresql_handler.execute_with_rollback(
            connection, self.destination_insert_query, batch
        )

    def process_batch(
        self,
        connection,
        batch: list[tuple],
        batch_no: int,
        total_batches: int,
        total_records: int,
    ) -> None:
        """
        Transform queried result from source table and insert them to a new table.

        Args:
            connection (postgres_connection): A PostgreSQL connection.
            batch (list): A batch to process.
            batch_no (int): The current batch's number, used only for logging.
            total_batches (int): The total number of batches to process, used only for logging.

        Returns:
            None
        """
        logger.info(
            f"started transforming and inserting batch {batch_no}/{total_batches} (size {len(batch)})..."
        )
        transformed_batch = [self.transform_record(record=record) for record in batch]
        self.insert_batch_to_destination(
            connection=connection,
            batch=transformed_batch,
        )
        logger.info(
            f"successfully transformed and inserted batch {batch_no}/{total_batches} (size {len(batch)})."
        )
        self.postgresql_handler.update_batch_tracker(
            connection=connection,
            service_name="engagement_service",
            table_name="MemberPrivilege",
            total_records=total_records,
            batch_no=batch_no,
        )

    def migrate_full_dump(self) -> None:
        """
        The main function for Engagement Service DAG.

        Args:
            None

        Returns:
            None
        """
        created_at = datetime.now()
        incremental_date = None

        mssql_connection = self.mssql_handler.hook.get_conn()
        postgresql_connection = self.postgresql_handler.hook.get_conn()

        tracker = self.postgresql_handler.get_latest_batch_info(
            connection=postgresql_connection,
            service_name="engagement_service",
            table_name="MemberPrivilege",
        )

        total_records = (
            self.mssql_handler.get_table_total_records(self.count_query_string)
            if tracker is None
            else tracker[0]
        )
        total_batches = math.ceil(total_records / self.batch_size)
        offsets = get_query_offsets(
            total_records=total_records,
            batch_size=self.batch_size,
            starting_offset=(
                0 if tracker is None else (tracker[1] - 1) * self.batch_size
            ),
        )
        completed_batches = tracker[2] if tracker is not None else []

        is_migration_succeeded = False

        try:
            futures = []

            batch_generator = self.mssql_handler.generate_batches(
                connection=mssql_connection,
                query_string=self.select_query_string,
                total_records=total_records,
                batch_size=self.batch_size,
                offsets=offsets,
                completed_batches=completed_batches,
            )

            with ThreadPoolExecutor(max_workers=self.executor_max_workers) as executor:
                semaphore = threading.Semaphore(self.executor_max_workers)

                while True:
                    semaphore.acquire()

                    try:
                        batch, batch_no = next(batch_generator)
                    except StopIteration:
                        break

                    future = executor.submit(
                        self.process_batch,
                        postgresql_connection,
                        batch,
                        batch_no,
                        total_batches,
                        total_records,
                    )
                    futures.append(future)
                    future.add_done_callback(lambda _: semaphore.release())

                for future in as_completed(futures):
                    future.result()

            logger.info(
                f"succesfully processed {total_records} records into MemberPrivilege"
            )

            logger.info(f"started cleaning up batch tracker...")
            self.postgresql_handler.cleanup_batch_tracker(
                connection=postgresql_connection,
                service_name="engagement_service",
                table_name="MemberPrivilege",
            )
            logger.info(f"finished cleaning up batch tracker.")

            is_migration_succeeded = True

        except Exception as error:
            logger.error(f"an error has occured: {error}")

            if postgresql_connection:
                logger.info("a postgresql connection is found, rolling back...")
                postgresql_connection.rollback()
                logger.info("successfully rolled back.")

            raise error
        finally:
            logger.info("started inserting migration result log...")
            if is_migration_succeeded:
                insert_migration_result(
                    postgresql_handler=self.postgresql_handler,
                    dag_name="engagement_service_migration",
                    migration_type="FULL_DUMP",
                    source_table="LVBirthday",
                    source_table_count=total_records,
                    destination_table="MemberPrivilege",
                    destination_table_count=total_records,
                    validation_type="COMPLETENESS",
                    validation_result=100,
                    created_at=created_at,
                    incremental_date=incremental_date,
                )
            else:
                tracker = self.postgresql_handler.get_latest_batch_info(
                    connection=postgresql_connection,
                    service_name="engagement_service",
                    table_name="MemberPrivilege",
                )

                destination_table_count = (
                    0
                    if tracker is None
                    else (
                        ((tracker[1] - 1) * self.batch_size)
                        if (tracker[1] - 1) * self.batch_size <= total_records
                        else total_records
                    )
                )

                total_processed = (
                    0 if tracker is None else len(tracker[2]) * self.batch_size
                )

                if tracker is not None and total_batches in tracker[2]:
                    total_processed -= self.batch_size + total_records % self.batch_size

                insert_migration_result(
                    postgresql_handler=self.postgresql_handler,
                    dag_name="engagement_service_migration",
                    migration_type="FULL_DUMP",
                    source_table="LVBirthday",
                    source_table_count=total_records,
                    destination_table="MemberPrivilege",
                    destination_table_count=destination_table_count,
                    validation_type="COMPLETENESS",
                    validation_result=(
                        0 if tracker is None else total_processed / total_records * 100
                    ),
                    created_at=created_at,
                    incremental_date=incremental_date,
                )
            logger.info("finished inserting migration result log.")

            if mssql_connection:
                mssql_connection.close()
            if postgresql_connection:
                postgresql_connection.close()

    def validate_migration(self) -> None:
        """
        Validate the migrated data by comparing source and destination records.
        Logs validation results without raising errors.

        Args:
            None

        Returns:
            None
        """
        mssql_connection = self.mssql_handler.hook.get_conn()
        postgresql_connection = self.postgresql_handler.hook.get_conn()

        try:
            # Get total count from source
            source_count = self.mssql_handler.get_table_total_records(
                self.count_query_string
            )

            # Get total count from destination
            destination_count_query = """
                SELECT COUNT(*)
                FROM "engagement_service"."MemberPrivilege"
                WHERE "privilegeId" = %s;
            """
            with postgresql_connection.cursor() as cursor:
                cursor.execute(destination_count_query, (self.service_name,))
                destination_count = cursor.fetchone()[0]

            # Log count validation results
            if source_count != destination_count:
                logger.warning(
                    f"Count mismatch: Source has {source_count} records, but destination has {destination_count} records"
                )
            else:
                logger.info(f"Count validation passed: {source_count} records match")

            # Get source records
            source_records_query = f"""
                SELECT 
                    lvb.LVBirthdayKey AS id,
                    lvb.LVTransKey AS lv_trans_key,
                    lvb.LVNumber AS member_id,
                    0 AS is_unlimited,
                    DATEADD(HOUR, -7, lvh.AddDT) AS granted_at,
                    DATEADD(HOUR, -7, lvh.AddDT) AS created_at,
                    DATEADD(HOUR, -7,
                        CASE 
                            WHEN lvh.FinishDT IS NOT NULL THEN lvh.FinishDT
                            ELSE lvh.AddDT
                        END
                    ) AS updated_at,
                    CASE 
                        WHEN lvd.[ExpireDate] = CAST(lvd.[ExpireDate] AS DATE) 
                        THEN DATEADD(
                            MILLISECOND,
                            61199999, 
                            CAST(lvd.[ExpireDate] AS DATETIME2(3))
                        )
                        ELSE DATEADD(HOUR, -7, lvd.[ExpireDate])
                    END AS expired_at,
                    'ACTIVE' as status
                FROM snapshot_lv_birthday_28052025 lvb
                JOIN LVTrans lvt ON lvt.LVTransKey = lvb.LVTransKey
                JOIN LVHeader lvh ON lvh.LVHeaderKey = lvt.LVHeaderKey
                JOIN LVData lvd ON lvd.LVMainKey = lvt.LVMainKey
                WHERE "YOB" = {self.YOB};
            """

            with mssql_connection.cursor() as cursor:
                cursor.execute(source_records_query)
                source_records = cursor.fetchall()

            # Get destination records
            destination_records_query = """
                SELECT 
                    "id",
                    "memberId",
                    "isUnlimited",
                    "grantedAt",
                    "createdAt",
                    "updatedAt",
                    "expiredAt",
                    "status"
                FROM "engagement_service"."MemberPrivilege"
                WHERE "privilegeId" = %s;
            """

            with postgresql_connection.cursor() as cursor:
                cursor.execute(destination_records_query, (self.service_name,))
                destination_records = cursor.fetchall()

            # Create dictionaries for comparison
            source_dict = {
                f"{record[0]}_{record[1]}": {
                    "member_id": record[2],
                    "is_unlimited": bool(record[3]),
                    "granted_at": record[4],
                    "created_at": record[5],
                    "updated_at": record[6],
                    "expired_at": record[7],
                    "status": record[8],
                }
                for record in source_records
            }

            destination_dict = {
                record[0]: {
                    "member_id": record[1],
                    "is_unlimited": record[2],
                    "granted_at": record[3],
                    "created_at": record[4],
                    "updated_at": record[5],
                    "expired_at": record[6],
                    "status": record[7],
                }
                for record in destination_records
            }

            # Find missing and extra records
            source_ids = set(source_dict.keys())
            destination_ids = set(destination_dict.keys())
            missing_ids = source_ids - destination_ids
            extra_ids = destination_ids - source_ids

            # Find data mismatches
            data_mismatches = []
            common_ids = source_ids.intersection(destination_ids)

            for record_id in common_ids:
                source_data = source_dict[record_id]
                dest_data = destination_dict[record_id]

                mismatches = []
                if source_data["member_id"] != dest_data["member_id"]:
                    mismatches.append(
                        f"member_id: source={source_data['member_id']}, dest={dest_data['member_id']}"
                    )
                if source_data["is_unlimited"] != dest_data["is_unlimited"]:
                    mismatches.append(
                        f"is_unlimited: source={source_data['is_unlimited']}, dest={dest_data['is_unlimited']}"
                    )
                if source_data["granted_at"] != dest_data["granted_at"]:
                    mismatches.append(
                        f"granted_at: source={source_data['granted_at']}, dest={dest_data['granted_at']}"
                    )
                if source_data["created_at"] != dest_data["created_at"]:
                    mismatches.append(
                        f"created_at: source={source_data['created_at']}, dest={dest_data['created_at']}"
                    )
                if source_data["updated_at"] != dest_data["updated_at"]:
                    mismatches.append(
                        f"updated_at: source={source_data['updated_at']}, dest={dest_data['updated_at']}"
                    )
                if source_data["expired_at"] != dest_data["expired_at"]:
                    mismatches.append(
                        f"expired_at: source={source_data['expired_at']}, dest={dest_data['expired_at']}"
                    )
                if source_data["status"] != dest_data["status"]:
                    mismatches.append(
                        f"status: source={source_data['status']}, dest={dest_data['status']}"
                    )

                if mismatches:
                    data_mismatches.append((record_id, mismatches))

            # Log results
            if missing_ids:
                logger.warning(
                    f"Found {len(missing_ids)} records missing in destination table"
                )
                logger.warning(f"Missing record IDs: {', '.join(sorted(missing_ids))}")
            else:
                logger.info("No missing records found in destination table")

            if extra_ids:
                logger.warning(
                    f"Found {len(extra_ids)} extra records in destination table"
                )
                logger.warning(f"Extra record IDs: {', '.join(sorted(extra_ids))}")
            else:
                logger.info("No extra records found in destination table")

            if data_mismatches:
                logger.warning(
                    f"Found {len(data_mismatches)} records with data mismatches"
                )
                for record_id, mismatches in data_mismatches:
                    logger.warning(f"Data mismatches for record {record_id}:")
                    for mismatch in mismatches:
                        logger.warning(f"  - {mismatch}")
            else:
                logger.info("No data mismatches found in matching records")

            # Log overall validation summary
            logger.info("Migration validation completed")
            logger.info(f"Total records in source: {source_count}")
            logger.info(f"Total records in destination: {destination_count}")
            logger.info(f"Missing records: {len(missing_ids)}")
            logger.info(f"Extra records: {len(extra_ids)}")
            logger.info(f"Records with data mismatches: {len(data_mismatches)}")

        except Exception as error:
            logger.error(f"Error during validation: {error}")
        finally:
            if mssql_connection:
                mssql_connection.close()
            if postgresql_connection:
                postgresql_connection.close()
