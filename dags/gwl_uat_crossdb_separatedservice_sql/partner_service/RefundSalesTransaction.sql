INSERT into public."RefundSalesTransaction" (
    id, 
    "salesTransactionId", 
    "type", 
    "taxInvoices", 
    "externalId", 
    "reason", 
    "caratRefundAmount", 
    "caratRevokeAmount", 
    "cashbackRefundAmount", 
    "cashbackRevokeAmount", 
    "chargeBackAmount",
    "refundedAt", 
    "createdAt", 
    "updatedAt",
    detail,
    "approvedAt"
)
SELECT
    id, 
    "salesTransactionId", 
    "type", 
    "taxInvoices", 
    "externalId", 
    "reason", 
    "caratRefundAmount", 
    "caratRevokeAmount", 
    "cashbackRefundAmount", 
    "cashbackRevokeAmount", 
    "chargeBackAmount",
    "refundedAt", 
    "createdAt", 
    "updatedAt",
    detail,
    "approvedAt"
FROM dblink(
    'my_connection', 
    'SELECT 
        *
     FROM partner_service."RefundSalesTransaction"'
) AS t1 (
    id text,
    "salesTransactionId" bigint,
    "type" text,
    "taxInvoices" text,
    "externalId" text,
    reason text,
    "caratRefundAmount" numeric(16, 2),
    "caratRevokeAmount" numeric(16, 2),
    "cashbackRefundAmount" numeric(16, 2),
    "cashbackRevokeAmount" numeric(16, 2),
    "chargeBackAmount" numeric(16, 2),
    "refundedAt" timestamp(3),
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    detail jsonb,
    "approvedAt" timestamp(3)
)
ON CONFLICT (id) DO UPDATE SET
    "salesTransactionId" = EXCLUDED."salesTransactionId",
    "type" = EXCLUDED."type",
    "taxInvoices" = EXCLUDED."taxInvoices",
    "externalId" = EXCLUDED."externalId",
    reason = EXCLUDED.reason,
    "caratRefundAmount" = EXCLUDED."caratRefundAmount",
    "caratRevokeAmount" = EXCLUDED."caratRevokeAmount",
    "cashbackRefundAmount" = EXCLUDED."cashbackRefundAmount",
    "cashbackRevokeAmount" = EXCLUDED."cashbackRevokeAmount",
    "chargeBackAmount" = EXCLUDED."chargeBackAmount",
    "refundedAt" = EXCLUDED."refundedAt",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    detail = EXCLUDED.detail,
    "approvedAt" = EXCLUDED."approvedAt";