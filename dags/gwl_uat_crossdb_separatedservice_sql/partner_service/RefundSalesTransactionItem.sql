INSERT into public."RefundSalesTransactionItem" (
    "id",
    "refundSalesTransactionId",
    "salesTransactionItemId",
    "quantity",
    "refundWallets",
    "revokeWallets",
    "createdAt",
    "updatedAt"
)
SELECT
    "id",
    "refundSalesTransactionId",
    "salesTransactionItemId",
    "quantity",
    "refundWallets",
    "revokeWallets",
    "createdAt",
    "updatedAt"
FROM dblink(
    'my_connection', 
    'SELECT 
        *
     FROM partner_service."RefundSalesTransactionItem"'
) AS t1 (
    "id"	text,
    "refundSalesTransactionId"	text,
    "salesTransactionItemId"	text,
    "quantity"	int4,
    "refundWallets"	jsonb,
    "revokeWallets"	jsonb,
    "createdAt"	timestamp(3),
    "updatedAt"	timestamp(3)
)
ON CONFLICT (id) DO UPDATE SET
    "refundSalesTransactionId" = EXCLUDED."refundSalesTransactionId",
    "salesTransactionItemId" = EXCLUDED."salesTransactionItemId",
    "quantity" = EXCLUDED."quantity",
    "refundWallets" = EXCLUDED."refundWallets",
    "revokeWallets" = EXCLUDED."revokeWallets",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";