INSERT into public."SalesTransactionWalletActivity" (
    "id",
    "salesTransactionId",
    "type",
    "activityId",
    "detail",
    "createdAt",
    "updatedAt"
)
SELECT
    "id",
    "salesTransactionId",
    "type",
    "activityId",
    "detail",
    "createdAt",
    "updatedAt"
FROM dblink(
    'my_connection', 
    'SELECT
        *
    FROM partner_service."SalesTransactionWalletActivity"'
)  AS t1 (    
    id text,
    "salesTransactionId" bigint,
    "type" text,
    "activityId" text,
    detail jsonb,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3)
)  
ON CONFLICT (id) DO UPDATE SET
    "salesTransactionId" = EXCLUDED."salesTransactionId",
    "type" = EXCLUDED."type",
    "activityId" = EXCLUDED."activityId",
    detail = EXCLUDED.detail,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt";