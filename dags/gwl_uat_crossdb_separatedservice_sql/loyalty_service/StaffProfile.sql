-- StaffProfile
INSERT into public."StaffProfile" (
    "id"
    ,"memberId"
    ,"staffLevelCode"
    ,"companyCode"
    ,"staffNo"
    ,"createdAt"
    ,"updatedAt"
    ,"staffPosition"
    ,"staffDivision"
    ,"staffJobLevel"
    ,"createdBy"
    ,"updatedBy"
)
SELECT
    "id"
    ,"memberId"
    ,"staffLevelCode"
    ,"companyCode"
    ,"staffNo"
    ,"createdAt"
    ,"updatedAt"
    ,"staffPosition"
    ,"staffDivision"
    ,"staffJobLevel"
    ,jsonb_build_object(
                        'id', NULL, 
                        'name', 'SYSTEM', 
                        'email', NULL
                        ) AS "createdBy"
    ,jsonb_build_object(
                      'id', NULL, 
                        'name', 'SYSTEM', 
                        'email', NULL
                    ) AS "updatedBy"

FROM dblink('my_connection', 'SELECT * FROM loyalty_service."StaffProfile"') AS 
t1(id text,
    "memberId" text,
    "staffLevelCode" text,
    "companyCode" text,
    "staffNo" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "staffPosition" text,
    "staffDivision" text,
    "staffJobLevel" text)
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "staffLevelCode" = EXCLUDED."staffLevelCode",
    "companyCode" = EXCLUDED."companyCode",
    "staffNo" = EXCLUDED."staffNo",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "staffPosition" = EXCLUDED."staffPosition",
    "staffDivision" = EXCLUDED."staffDivision",
    "staffJobLevel" = EXCLUDED."staffJobLevel",
    "createdBy" = EXCLUDED."createdBy",
    "updatedBy" = EXCLUDED."updatedBy";
-- Note: The "createdBy" and "updatedBy" fields are set to a default system user.