-- Member
INSERT into public."Member" (
    "id"
    ,"gwlNo"
    ,"embossNo"
    ,"email"
    ,"emailVerifiedAt"
    ,"phone"
    ,"phoneVerifiedAt"
    ,"registeredAt"
    ,"deletedAt"
    ,"tierId"
    ,"tierStartedAt"
    ,"tierEndedAt"
    ,"accumulateSpending"
    ,"lifeTimeSpending"
    ,"createdAt"
    ,"updatedAt"
    ,"isActive"
    ,"reason"
    ,"picRemark"
    ,"referralCode"
    ,"upgradeGroupCode"
    ,"upgradeReasonCode"
    ,"registrationChannelCode"
    ,"registrationLocationCode"
    ,"accumulateMaintainSpending"
    ,"shoppingCardId"
    ,"onepassId"
    ,"phoneCode"
    ,"updatedBy"
    ,"isCoBrandNonMember"
    ,"emailHash"
    ,"phoneHash"
    ,"minimumTierId"
    ,"minimumTierInvitedId"
    ,"remark"
    ,"createdBy"
)
SELECT -- seqence matter due to prod col order differ from source
    "id"
    ,"gwlNo"
    ,"embossNo"
    ,"email"
    ,"emailVerifiedAt"
    ,"phone"
    ,"phoneVerifiedAt"
    ,"registeredAt"
    ,"deletedAt"
    ,"tierId"
    ,"tierStartedAt"
    ,"tierEndedAt"
    ,"accumulateSpending"
    ,"lifeTimeSpending"
    ,"createdAt"
    ,"updatedAt"
    ,"isActive"
    ,"reason"
    ,"picRemark"
    ,"referralCode"
    ,"upgradeGroupCode"
    ,"upgradeReasonCode"
    ,"registrationChannelCode"
    ,"registrationLocationCode"
    ,"accumulateMaintainSpending"
    ,"shoppingCardId"
    ,"onepassId"
    ,"phoneCode"
    ,"updatedBy"
    ,"isCoBrandNonMember"
    ,"emailHash"
    ,"phoneHash"
    ,"minimumTierId"
    ,"minimumTierInvitedId"
    ,"remark"
    ,"createdBy"	
FROM dblink('my_connection', 'SELECT * FROM loyalty_service."Member"') AS 
t1( 
    id text,
    "gwlNo" text,
    "embossNo" text,
    email text,
    "emailVerifiedAt" timestamp(3),
    phone text,
    "phoneVerifiedAt" timestamp(3),
    "registeredAt" timestamp(3),
    "deletedAt" timestamp(3),
    "tierId" text,
    "minimumTierId" text,
    "tierStartedAt" timestamp(3) ,
    "tierEndedAt" timestamp(3),
    "accumulateSpending" numeric(16, 2),
    "lifeTimeSpending" numeric(16, 2),
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "isActive" bool,
    reason text,
    "picRemark" text,
    "referralCode" text,
    "upgradeGroupCode" text,
    "upgradeReasonCode" text,
    "registrationChannelCode" text,
    "registrationLocationCode" text,
    "accumulateMaintainSpending" numeric(16, 2),
    "shoppingCardId" text,
    "onepassId" text,
    "phoneCode" text,
    "isCoBrandNonMember" bool,
    "emailHash" text,
    "phoneHash" text,
    "minimumTierInvitedId" text,
    "updatedBy" jsonb,
    remark text,
    "createdBy" jsonb
)
ON CONFLICT(id) DO UPDATE SET
    "gwlNo" = EXCLUDED."gwlNo",
    "embossNo" = EXCLUDED."embossNo",
    email = EXCLUDED.email,
    "emailVerifiedAt" = EXCLUDED."emailVerifiedAt",
    phone = EXCLUDED.phone,
    "phoneVerifiedAt" = EXCLUDED."phoneVerifiedAt",
    "registeredAt" = EXCLUDED."registeredAt",
    "deletedAt" = EXCLUDED."deletedAt",
    "tierId" = EXCLUDED."tierId",
    "minimumTierId" = EXCLUDED."minimumTierId",
    "tierStartedAt" = EXCLUDED."tierStartedAt",
    "tierEndedAt" = EXCLUDED."tierEndedAt",
    "accumulateSpending" = EXCLUDED."accumulateSpending",
    "lifeTimeSpending" = EXCLUDED."lifeTimeSpending",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "isActive" = EXCLUDED."isActive",
    reason = EXCLUDED.reason,
    "picRemark" = EXCLUDED."picRemark",
    "referralCode" = EXCLUDED."referralCode",
    "upgradeGroupCode" = EXCLUDED."upgradeGroupCode",
    "upgradeReasonCode" = EXCLUDED."upgradeReasonCode",
    "registrationChannelCode" = EXCLUDED."registrationChannelCode",
    "registrationLocationCode" = EXCLUDED."registrationLocationCode",
    "accumulateMaintainSpending" = EXCLUDED."accumulateMaintainSpending",
    "shoppingCardId" = EXCLUDED."shoppingCardId",
    "onepassId" = EXCLUDED."onepassId",
    "phoneCode" = EXCLUDED."phoneCode",
    "isCoBrandNonMember" = EXCLUDED."isCoBrandNonMember",
    "emailHash" = EXCLUDED."emailHash",
    "phoneHash" = EXCLUDED."phoneHash",
    "minimumTierInvitedId" = EXCLUDED."minimumTierInvitedId",
    "updatedBy" = EXCLUDED."updatedBy",
    remark = EXCLUDED.remark,
    "createdBy" = EXCLUDED."createdBy";