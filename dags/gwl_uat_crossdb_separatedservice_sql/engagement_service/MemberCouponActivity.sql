-- MemberCouponActivity
INSERT into public."MemberCouponActivity" (
    "id"
    ,"memberId"
    ,"refId"
    ,"refType"
    ,"remark"
    ,"activity"
    ,"couponCode"
    ,"reason"
    ,"location"
    ,"usedBy"
    ,"createdAt"
    ,"deletedAt"
    ,"createdBy"
    ,"channel"
)
SELECT
    "id"
    ,"memberId"
    ,"refId"
    ,"refType"
    ,"remark"
    ,"activity"
    ,"couponCode"
    ,"reason"
    ,"location"
    ,"usedBy"
    ,"createdAt"
    ,"deletedAt"
    ,"createdBy"
    ,"channel"

FROM dblink('my_connection', 'SELECT * FROM engagement_service."MemberCouponActivity"') AS 
t1(id text,
    "memberId" text,
    "refId" text,
    "refType" text,
    remark jsonb,
    activity text,
    "couponCode" text,
    reason text,
    "location" text,
    "createdBy" text,
    "usedBy" text,
    "createdAt" timestamp(3),
    "deletedAt" timestamp(3),
    channel text)
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "refId" = EXCLUDED."refId",
    "refType" = EXCLUDED."refType",
    remark = EXCLUDED.remark,
    activity = EXCLUDED.activity,
    "couponCode" = EXCLUDED."couponCode",
    reason = EXCLUDED.reason,
    "location" = EXCLUDED."location",
    "createdBy" = EXCLUDED."createdBy",
    "usedBy" = EXCLUDED."usedBy",
    "createdAt" = EXCLUDED."createdAt",
    "deletedAt" = EXCLUDED."deletedAt",
    channel = EXCLUDED.channel;
    -- add chanel
    -- fixed prod sep to ba same shcema as prod gwl and uat
    -- fixed "createdBy" to jsonb from text
    -- set "createdBy" default '{"id": null, "name": "SYSTEM", "email": null}'::jsonb