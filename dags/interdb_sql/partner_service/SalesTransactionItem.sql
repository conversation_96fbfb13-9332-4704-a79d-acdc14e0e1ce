INSERT into partner_service."SalesTransactionItem" (
    "id",
    "productId",
    "quantity",
    "netAmount",
    "caratEarnableAmount",
    "normalPointEarned",
    "burnPaymentAmount",
    "settings",
    "paymentDetail",
    "createdAt",
    "updatedAt",
    "originalPrice",
    "salesTransactionId",
    "tierExtraPointEarned",
    "sku"
)
SELECT
    sti."id",
    "productId",
    "quantity",
    "netAmount",
    "caratEarnableAmount",
    "normalPointEarned",
    "burnPaymentAmount",
    "settings",
    "paymentDetail",
    "createdAt",
    "updatedAt",
    "originalPrice",
    "salesTransactionId",
    "tierExtraPointEarned",
    "sku"
FROM staging_partner_service."SalesTransactionItem" sti
INNER JOIN (
	select id
	from partner_service."SalesTransaction"
) st
on sti."salesTransactionId" = st.id
ON CONFLICT (id) DO UPDATE SET
    "productId" = EXCLUDED."productId",
    quantity = EXCLUDED.quantity,
    "netAmount" = EXCLUDED."netAmount",
    "caratEarnableAmount" = EXCLUDED."caratEarnableAmount",
    "normalPointEarned" = EXCLUDED."normalPointEarned",
    "burnPaymentAmount" = EXCLUDED."burnPaymentAmount",
    settings = EXCLUDED.settings,
    "paymentDetail" = EXCLUDED."paymentDetail",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "originalPrice" = EXCLUDED."originalPrice",
    "salesTransactionId" = EXCLUDED."salesTransactionId",
    "tierExtraPointEarned" = EXCLUDED."tierExtraPointEarned",
    sku = EXCLUDED.sku;