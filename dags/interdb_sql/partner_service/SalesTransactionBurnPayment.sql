INSERT into partner_service."SalesTransactionBurnPayment" (
    "id",
    "walletCode",
    "burnAmount",
    "beforeAmount",
    "afterAmount",
    "paymentAmount",
    "settings",
    "createdAt",
    "updatedAt",
    "salesTransactionId",
    "burnPurpose"
)
WITH salestransactionburnpayment AS
(SELECT
    ulid.ulid_id as "id",
    stbp."walletCode",
    stbp."burnAmount",
    stbp."beforeAmount",
    stbp."afterAmount",
    stbp."paymentAmount",
    
    json_build_object(
		'burnRate', 1,
		'currency', case when "walletCode" <> 'CARAT_WALLET' then 'THB' else 'CARAT' end,
		'walletName', case
			when "walletCode" = 'CARAT_WALLET' then 'CARAT WALLET' 
			when "walletCode" = 'CASH_WALLET' then 'CASH WALLET'
			when "walletCode" = 'EPURSE_BD_CASHBACK' then 'BIRTHDAY CASHBACK'
			when "walletCode" = 'EPURSE_PROMO_WALLET' then 'E-PURSE use at All King Power stores'
			when "walletCode" = 'EPURSE_PROMO_RANGNAM' then 'E-PURSE use at Rangnam exclusively'
			else '-'
		end
	) AS "settings",
	
    stbp."createdAt",
    stbp."updatedAt",
    stbp."salesTransactionId",
    'ITEM' as "burnPurpose"
FROM staging_partner_service."SalesTransactionBurnPayment" stbp
INNER JOIN partner_service."SalesTransaction" st on stbp."salesTransactionId" = st.id
LEFT JOIN staging_partner_service."ulid_SalesTransactionBurnPayment" ulid ON stbp.id = ulid.id
--where ("walletCode" = 'CASH_WALLET' and stbp."afterAmount" >100000)
)
SELECT
    "id",
    "walletCode",
    "burnAmount",
    "beforeAmount",
    "afterAmount",
    "paymentAmount",
    "settings",
    "createdAt",
    "updatedAt",
    "salesTransactionId",
    "burnPurpose"
FROM salestransactionburnpayment

ON CONFLICT (id) DO UPDATE SET
    "walletCode" = EXCLUDED."walletCode",
    "burnAmount" = EXCLUDED."burnAmount",
    "beforeAmount" = EXCLUDED."beforeAmount",
    "afterAmount" = EXCLUDED."afterAmount",
    "paymentAmount" = EXCLUDED."paymentAmount",
    settings = EXCLUDED.settings,
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "salesTransactionId" = EXCLUDED."salesTransactionId",
    "burnPurpose" = EXCLUDED."burnPurpose";