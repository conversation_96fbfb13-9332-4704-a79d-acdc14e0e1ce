INSERT into loyalty_service."Member" (
    "id",
    "gwlNo",
    "embossNo",
    "email",
    "emailVerifiedAt",
    "phone",
    "phoneVerifiedAt",
    "registeredAt",
    "deletedAt",
    "tierId",
    "minimumTierId",
    "tierStartedAt",
    "tierEndedAt",
    "accumulateSpending",
    "lifeTimeSpending",
    "createdAt",
    "updatedAt",
    "isActive",
    "reason",
    "picRemark",
    "referralCode",
    "upgradeGroupCode",
    "upgradeReasonCode",
    "registrationChannelCode",
    "registrationLocationCode",
    "accumulateMaintainSpending",
    "shoppingCardId",
    "onepassId",
    "phoneCode",
    "isCoBrandNonMember",
    "emailHash",
    "phoneHash",
    "minimumTierInvitedId",
    "updatedBy",
    "remark",
    "createdBy"
)
WITH tier_id AS 
(SELECT
    pre_tier_id."id",
    pre_tier_id."gwlNo",
    pre_tier_id."embossNo",
    pre_tier_id."email",
    pre_tier_id."emailVerifiedAt",
    pre_tier_id."phone",
    pre_tier_id."phoneVerifiedAt",
    pre_tier_id."registeredAt",
    pre_tier_id."deletedAt",
    tier.id as "tierId",

	-- CASE    WHEN TRIM(UPPER(pre_tier_id."minimumTierId")) = 'NAVY'    THEN '01JDH60A17HZSCM5EBF41DQ5N9'
	--         WHEN TRIM(UPPER(pre_tier_id."minimumTierId")) = 'SCARLET' THEN '01J7AR435H9RA6CKY8Y18SKYC1'
	--         WHEN TRIM(UPPER(pre_tier_id."minimumTierId")) = 'CROWN'   THEN '01J7AQT7546T9BF5X4HWAX205C'
	--         WHEN TRIM(UPPER(pre_tier_id."minimumTierId")) = 'VEGA'    THEN '01J7AQNC895Y186Q7XXEN2YGFH'
	--         WHEN TRIM(UPPER(pre_tier_id."minimumTierId")) = 'CRYSTAL' THEN '01JACNRT6EK46R4KFGWCAQWDAW'
	--         WHEN TRIM(UPPER(pre_tier_id."minimumTierId")) = 'VVIP'    THEN '01J7ASTER48X7XQG14SK3BEFAF'
    --         ELSE NULL
    -- END AS "minimumTierId",
    tier1.id as "minimumTierId",
    pre_tier_id."tierStartedAt",
    pre_tier_id."tierEndedAt",
    pre_tier_id."accumulateSpending",
    pre_tier_id."lifeTimeSpending",
    pre_tier_id."createdAt",
    pre_tier_id."updatedAt",
    pre_tier_id."isActive",
    pre_tier_id."reason",
    pre_tier_id."picRemark",
    pre_tier_id."referralCode",
    pre_tier_id."upgradeGroupCode",
    pre_tier_id."upgradeReasonCode",
    pre_tier_id."registrationChannelCode",
    pre_tier_id."registrationLocationCode",
    pre_tier_id."accumulateMaintainSpending",
    pre_tier_id."shoppingCardId",
    pre_tier_id."onepassId",
    pre_tier_id."phoneCode",
    pre_tier_id.remark,
    pre_tier_id."isCoBrandNonMember"::bool,
    pre_tier_id."emailHash",
    pre_tier_id."phoneHash",
    pre_tier_id."minimumTierInvitedId",
    pre_tier_id."updatedBy",
    pre_tier_id."createdBy"
FROM
    (   SELECT
            ulid_id AS id,
            "gwlNo",
            "embossNo",
            email,
            "emailVerifiedAt",
            phone,
            "phoneVerifiedAt",
            "registeredAt",
            "deletedAt",
            CASE
                WHEN trim(UPPER(mt."tierId")) = 'VEGA(GP)' THEN 'VEGA'
                WHEN trim(UPPER(mt."tierId")) = 'SCARLET(GP)' THEN 'SCARLET'
                WHEN trim(UPPER(mt."tierId")) = 'CROWN(GP)' THEN 'CROWN'
                ELSE UPPER(mt."tierId")
            END as "tierId", 
            CASE
                WHEN trim(UPPER(mt."tierId")) = 'VEGA(GP)' THEN 'VEGA'
                WHEN trim(UPPER(mt."tierId")) = 'SCARLET(GP)' THEN 'SCARLET'
                WHEN trim(UPPER(mt."tierId")) = 'CROWN(GP)' THEN 'CROWN'
                ELSE NULL 
                END AS "minimumTierId", -- this indicate for member that in Grace Period
            "tierStartedAt",
            "tierEndedAt",
            "accumulateSpending",
            "lifeTimeSpending",
            "createdAt",
            "updatedAt",
            "isActive",
            reason,
            "picRemark",
            "referralCode",
            "upgradeGroupCode",
            "upgradeReasonCode",
            "registrationChannelCode",
            "registrationLocationCode",
            "accumulateMaintainSpending",
            "shoppingCardId",
            "onepassId",
            "phoneCode",
            remark,
            "isCoBrandNonMember"::bool,
            "emailHash",
            "phoneHash",
            
            CASE
                WHEN trim(UPPER(mt."minimumTierInvitedId")) = 'VEGA(GP)' THEN 'VEGA'
                WHEN trim(UPPER(mt."minimumTierInvitedId")) = 'SCARLET(GP)' THEN 'SCARLET'
                WHEN trim(UPPER(mt."minimumTierInvitedId")) = 'CROWN(GP)' THEN 'CROWN'
                ELSE trim(UPPER(mt."minimumTierInvitedId"))
                END AS "minimumTierInvitedId",
            "updatedBy",
            "createdBy"
        FROM(
                SELECT
                    ulid_member.ulid_id,
                    member."gwlNo",
                    member."embossNo",
                    member.email,
                    member."emailVerifiedAt",
                    member.phone,
                    member."phoneVerifiedAt",
                    member."registeredAt",
                    member."deletedAt",
                    CASE WHEN
                            (CASE WHEN tiertransformed."isActive" = true THEN tiertransformed."isActive" ELSE false END) = false THEN 'NAVY' ELSE UPPER(tiertransformed."tierId")
                        END AS "tierId",
                    
                    TIMESTAMP '2025-06-30 17:00:00' AS "tierStartedAt",

                    CASE WHEN TRIM(tiertransformed."tierId") IN ('Vega(GP)','Scarlet(GP)','Crown(GP)') THEN TIMESTAMP '2026-12-31 16:59:59' 
                        ELSE TIMESTAMP '2027-12-31 16:59:59'
                        END AS "tierEndedAt", 

                    tiertransformed."accumulateSpending",
                    member."lifeTimeSpending",
                    member."createdAt",
                    member."updatedAt",
                    CASE WHEN tiertransformed."isActive" = true THEN tiertransformed."isActive" ELSE false END AS "isActive",
                    member_reason.reason,
                    member."picRemark",
                    member."referralCode",
                    member."upgradeGroupCode",
                    member."upgradeReasonCode",
                    member."registrationChannelCode",
                    member."registrationLocationCode",
                    member."accumulateMaintainSpending",
                    member."shoppingCardId",
                    member."onepassId",
                    member."phoneCode",
                    member.remark,
                    isconon_m."isCoBrandNonMember"::bool,
                    member."emailHash",
                    member."phoneHash",
                    tiertransformed."minimumTierInvitedId",
                    jsonb_build_object(
                        'id', null, 
                        'name', 'SYSTEM', 
                        'email', null
                    ) as "updatedBy",
                    jsonb_build_object(
                        'id', null, 
                        'name', 'SYSTEM', 
                        'email', null
                    ) as "createdBy"
                FROM staging_loyalty_service."Member" as member
                INNER JOIN public.member_reason as member_reason ON member.id = member_reason."memberId"
                LEFT JOIN public.tiertransformed as tiertransformed ON member.id = tiertransformed."memberId"
                LEFT JOIN public.member_iscobrandnonmember as isconon_m ON member.id = isconon_m."memberId"
                LEFT JOIN staging_loyalty_service.ulid_member as ulid_member ON member.id = ulid_member.id
                WHERE (trim(lower(member_reason.reason)) <> 'not migrate' or (member_reason.reason is null))
                ) AS mt
    ) AS pre_tier_id
LEFT JOIN loyalty_service."Tier" as tier ON pre_tier_id."tierId" = tier.code
LEFT JOIN loyalty_service."Tier" as tier1 ON pre_tier_id."minimumTierId" = tier1.code
)

SELECT 
    "id",
    "gwlNo",
    "embossNo",
    "email",
    "emailVerifiedAt",
    "phone",
    "phoneVerifiedAt",
    "registeredAt",
    "deletedAt",
    "tierId",
    "minimumTierId",
    "tierStartedAt",
    "tierEndedAt",
    "accumulateSpending",
    "lifeTimeSpending",
    "createdAt",
    "updatedAt",
    "isActive",
    "reason",
    "picRemark",
    "referralCode",
    "upgradeGroupCode",
    "upgradeReasonCode",
    "registrationChannelCode",
    "registrationLocationCode",
    "accumulateMaintainSpending",
    "shoppingCardId",
    "onepassId",
    "phoneCode",
    "isCoBrandNonMember"::bool,
    "emailHash",
    "phoneHash",
    "minimumTierInvitedId",
    "updatedBy",
    "remark",
    "createdBy"
FROM tier_id

ON CONFLICT (id) DO UPDATE SET
"gwlNo" = EXCLUDED."gwlNo",
"embossNo" = EXCLUDED."embossNo",
email = EXCLUDED.email,
"emailVerifiedAt" = EXCLUDED."emailVerifiedAt",
phone = EXCLUDED.phone,
"phoneVerifiedAt" = EXCLUDED."phoneVerifiedAt",
"registeredAt" = EXCLUDED."registeredAt",
"deletedAt" = EXCLUDED."deletedAt",
"tierId" = EXCLUDED."tierId",
"minimumTierId" = EXCLUDED."minimumTierId",
"tierStartedAt" = EXCLUDED."tierStartedAt",
"tierEndedAt" = EXCLUDED."tierEndedAt",
"accumulateSpending" = EXCLUDED."accumulateSpending",
"lifeTimeSpending" = EXCLUDED."lifeTimeSpending",
"createdAt" = EXCLUDED."createdAt",
"updatedAt" = EXCLUDED."updatedAt",
"isActive" = EXCLUDED."isActive",
reason = EXCLUDED.reason,
"picRemark" = EXCLUDED."picRemark",
"referralCode" = EXCLUDED."referralCode",
"upgradeGroupCode" = EXCLUDED."upgradeGroupCode",
"upgradeReasonCode" = EXCLUDED."upgradeReasonCode",
"registrationChannelCode" = EXCLUDED."registrationChannelCode",
"registrationLocationCode" = EXCLUDED."registrationLocationCode",
"accumulateMaintainSpending" = EXCLUDED."accumulateMaintainSpending",
"shoppingCardId" = EXCLUDED."shoppingCardId",
"onepassId" = EXCLUDED."onepassId",
"phoneCode" = EXCLUDED."phoneCode",
"isCoBrandNonMember" = EXCLUDED."isCoBrandNonMember",
"emailHash" = EXCLUDED."emailHash",
"phoneHash" = EXCLUDED."phoneHash",
"minimumTierInvitedId" = EXCLUDED."minimumTierInvitedId",
"updatedBy" = EXCLUDED."updatedBy",
remark = EXCLUDED.remark,
"createdBy" = EXCLUDED."createdBy";
