INSERT INTO loyalty_service."StaffProfile" (
    "id",
    "memberId", --made to ULID 
    "staffLevelCode",
    "companyCode",
    "staffNo",
    "createdAt",
    "updatedAt",
    "staffPosition"
)
WITH staffprofile AS
(
SELECT
    sf."id",
    sm.id AS "memberId", --made to ULID 
    sf."staffLevelCode",
    sf."companyCode",
    sf."staffNo",
    sf."createdAt"::TIMESTAMPTZ,
    sf."updatedAt"::TIMESTAMPTZ,
    sf."staffPosition"
FROM staging_loyalty_service."StaffProfile" AS sf
INNER JOIN loyalty_service."Member" AS sm
ON sf."memberId"  = sm."gwlNo"
)
SELECT
    "id",
    "memberId", --made to ULID 
    "staffLevelCode",
    "companyCode",
    "staffNo",
    "createdAt",
    "updatedAt",
    "staffPosition"
FROM staffprofile

ON CONFLICT ("id") DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "staffLevelCode" = EXCLUDED."staffLevelCode",
    "companyCode" = EXCLUDED."companyCode",
    "staffNo" = EXCLUDED."staffNo",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "staffPosition" = EXCLUDED."staffPosition";
