INSERT INTO point_service."WalletTransaction" (
    "id",
    "memberId",
    "walletActivityId",
    "balanceId",
    "type",
    "walletCode",
    "amount",
    "expiredAt",
    "createdAt"
)
WITH wallettransaction AS
(SELECT
    wt.id,
    wt."memberId",
    wt."walletActivityId",
    wb.id as "balanceId",
    wt."type",
    wt."walletCode",
    coalesce(wt."amount", 0.00) as "amount",
    wt."expiredAt",
    wt."createdAt"
FROM (
    SELECT
        ulid.ulid_id as id,
        sm.id as "memberId",
        wa_ulid.ulid_id as "walletActivityId",
        wt."balanceId",
        wt."type",
        wt."walletCode",
        CASE WHEN wt."walletCode" = 'CARAT_WALLET' then wt.amount*4
            ELSE wt.amount
        END AS amount,
        CASE 
            WHEN wt."walletCode" = 'CARAT_WALLET' and EXTRACT(YEAR FROM wt."expiredAt") = 2099 THEN NULL
            WHEN wt."walletCode" = 'CARAT_WALLET' and wt."expiredAt" > now()
                THEN TO_TIMESTAMP(EXTRACT(YEAR FROM wt."expiredAt") || '-12-31 16:59:59.999', 'YYYY-MM-DD HH24:MI:SS.MS')
            ELSE wt."expiredAt"
        END AS "expiredAt",
        wt."createdAt"
    FROM staging_point_service."WalletTransaction" wt
    LEFT JOIN staging_point_service."ulid_WalletTransaction" ulid
    ON wt.id = ulid.id
    INNER JOIN loyalty_service."Member" AS sm
    ON wt."memberId" = sm."gwlNo"
    LEFT JOIN staging_point_service."ulid_WalletActivity" wa_ulid
    ON wt."walletActivityId" = wa_ulid.id
    INNER JOIN (select id from point_service."WalletActivity") wa
    ON wa_ulid.ulid_id = wa.id
) wt
INNER JOIN point_service."WalletBalance" wb
ON wt."memberId" = wb."memberId"
AND wt."walletCode" = wb."walletCode"
AND coalesce(wt."expiredAt", TIMESTAMP '2099-01-01 00:00:00') = coalesce(wb."expiredAt", TIMESTAMP '2099-01-01 00:00:00')
)

SELECT
    "id",
    "memberId",
    "walletActivityId",
    "balanceId",
    "type",
    "walletCode",
    "amount",
    "expiredAt",
    "createdAt"
FROM wallettransaction


ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "walletActivityId" = EXCLUDED."walletActivityId",
    "balanceId" = EXCLUDED."balanceId",
    "type" = EXCLUDED."type",
    "walletCode" = EXCLUDED."walletCode",
    amount = EXCLUDED.amount,
    "expiredAt" = EXCLUDED."expiredAt",
    "createdAt" = EXCLUDED."createdAt";
