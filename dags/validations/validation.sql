CREATE TABLE IF NOT EXISTS validation."ValidationBasicConditions" (
	gwl_table_schema      text,
	gwl_table_name        text,
	gwl_column_name       text,
	check_data_type       boolean,
	check_row_count       boolean,
	check_null_count      boolean,
	check_unique_count    boolean,
	check_sum_value       boolean,
	check_min_value       boolean,
	check_max_value       boolean,
	check_samples_uniqueness boolean,
	n_samples_uniqueness  integer,
	check_transformation  boolean,
	is_migrated			  boolean,
	updated_datetime      timestamp DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Bangkok')::timestamp
);

CREATE TABLE IF NOT EXISTS validation."ValidationResults" (
	temp_table_schema       text,
	temp_table_name         text,
	temp_column_name        text,
	temp_data_type          text,
	temp_row_count          integer,
	temp_null_count         integer,
	temp_unique_count       integer,
	temp_sum_value          numeric,
	temp_min_value          numeric,
	temp_max_value          numeric,
	gwl_table_schema        text,
	gwl_table_name          text,
	gwl_column_name         text,
	gwl_data_type           text,
	gwl_row_count           integer,
	gwl_null_count          integer,
	gwl_unique_count        integer,
	gwl_sum_value           numeric,
	gwl_min_value           numeric,
	gwl_max_value           numeric,
	check_data_type         boolean,
	check_row_count         boolean,
	check_null_count        boolean,
	check_unique_count      boolean,
	check_sum_value         boolean,
	check_min_value         boolean,
	check_max_value         boolean,
	check_samples_uniqueness boolean,
	n_samples_uniqueness    integer,
	check_transformation    boolean,
	is_match                boolean,
	error_message			text,
	validated_datetime      timestamp DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Bangkok')::timestamp
);

CREATE TABLE IF NOT EXISTS validation."ValidationTransformConditions" (
	gwl_table_schema      text,
	gwl_table_name        text,
	gwl_column_name       text,
	source_logic		  text,
	target_logic		  text,
	validation_criteria   text,
	join_key              text,
	n_samples             text,
	updated_datetime      timestamp DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Bangkok')::timestamp
);

CREATE TABLE IF NOT EXISTS validation."ValidationSummaryResults" (
	gwl_table_schema      text,
	gwl_table_name        text,
	is_match              boolean,
	error_message         jsonb,
	updated_datetime      timestamp DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Bangkok')::timestamp
);

truncate table validation."ValidationTransformConditions";

insert into validation."ValidationTransformConditions" (
gwl_table_schema,
gwl_table_name,
gwl_column_name,
source_logic,
target_logic,
validation_criteria,
join_key,
n_samples
)
values 
---- 2.2: All inactive members' tier = Navy
(
'loyalty_service',
'Member',
'tierId',
$$select '1' as id, count(1) as source_values 
from loyalty_service."Member" m
left join loyalty_service."Tier" t 
on m."tierId" = t.id 
where m."isActive" = false and t."code" <> 'NAVY'$$,
$$select '1' as id, null as target_values $$,
'source_values == 0',
'source.id = target.id',
null
),
---- 3.3: minimumTierId
---- VEGA: [Expected output: tier: VEGA]
---- CROWN: [Expected output: tier: IN_K and "occupation" in ('5', '11', '41','49','50') 
----						  tier: 'P_CR', 'N_CR', 'N_OX', 'OT_OX', 'OV_OX', 'IN_V1', 'IN_V2', 'IN_V3'
----						  cobrand: 'BVS', 'SVP15', 'SVP20', 'STRIB', 'SPLD']
---- SCARLET: [Expected output: tier: ('IN_K', 'N_SL', 'OT_SL', 'OV_SL'), cobrand: SVP10]
(
'loyalty_service',
'Member',
'minimumTierId',
$$WITH cardtypecode_cobrand AS (
SELECT 'BVP05' AS cardTypeCode, 5 AS value
UNION ALL
SELECT 'SVP05', 5
UNION ALL
SELECT 'SVP10', 4
UNION ALL
SELECT 'BVS', 3
UNION ALL
SELECT 'SVP15', 3
UNION ALL
SELECT 'SVP20', 3
UNION ALL
SELECT 'STRIB', 2
UNION ALL
SELECT 'SPLD', 2
UNION ALL
SELECT 'SVP30', 1
),
max_cardtypecode_cobrand as 
(SELECT 
"memberId"
,"cardTypeCode"
,value
FROM (select 
mlch."memberId"
,mlch."cardTypeCode"
,cc.value
,ROW_NUMBER() OVER (PARTITION BY mlch."memberId" ORDER BY cc.value DESC) AS rn
from
staging_loyalty_service."MemberLegacyCoBrandHistory" as mlch
LEFT JOIN
cardtypecode_cobrand AS cc ON mlch."cardTypeCode" = cc.cardTypeCode
WHERE mlch."cardStatus" = 'ACTIVE'
)
WHERE rn = 1),
cardtypecode_tier as
(SELECT 'N_NY' AS cardTypeCode, 10 AS value
UNION ALL
SELECT 'N_SL', 9
UNION ALL
SELECT 'OV_SL', 9
UNION ALL
SELECT 'OT_SL', 9
UNION ALL
SELECT 'P_CR', 8
UNION ALL
SELECT 'N_CR', 8
UNION ALL
SELECT 'N_OX', 7
UNION ALL
SELECT 'OT_OX', 7
UNION ALL
SELECT 'OV_OX', 7
UNION ALL
SELECT 'IN_V1', 6
UNION ALL
SELECT 'IN_V2', 5
UNION ALL
SELECT 'IN_V3', 5
UNION ALL
SELECT 'IN_K', 4
UNION ALL
SELECT 'VEGA', 3
UNION ALL
SELECT 'NVVIP', 2
UNION ALL
SELECT 'C_STF', 1
UNION ALL
SELECT 'CS_MS', 1
),
max_cardtypecode_tier as
(SELECT 
"memberId",
"cardTypeCode",
value
FROM (select
mlth."memberId"
,mlth."cardTypeCode"
,ct.value
,ROW_NUMBER() OVER (PARTITION BY mlth."memberId" ORDER BY ct.value DESC) AS rn
FROM staging_loyalty_service."MemberLegacyTierHistory" as mlth
LEFT JOIN
cardtypecode_tier AS ct ON mlth."cardTypeCode" = ct.cardTypeCode
WHERE mlth."cardStatus" = 'ACTIVE'
)
WHERE rn = 1
)
select '1' as id, count(1) as source_values 
from loyalty_service."Member" m 
left join loyalty_service."Tier" t on m."minimumTierId" = t.id 
left join staging_loyalty_service.ulid_member um on m.id = um.ulid_id 
left join max_cardtypecode_tier mlth on um.id = mlth."memberId"
left join max_cardtypecode_cobrand mlch on um.id = mlch."memberId"
left join staging_loyalty_service."MemberProfile" mp on um.id = mp."memberId"
where 
	(t.name = 'VEGA' and not (mlth."cardTypeCode" = 'VEGA'))
	or (t.name = 'CROWN'
		and not (
			(mlth."cardTypeCode" = 'IN_K' and mp."occupation" in ('5', '11', '41','49','50'))
			or (mlth."cardTypeCode" in ('P_CR', 'N_CR', 'N_OX', 'OT_OX', 'OV_OX', 'IN_V1', 'IN_V2', 'IN_V3'))
			or (mlch."cardTypeCode" in ('BVS', 'SVP15', 'SVP20', 'STRIB', 'SPLD'))
		))
	or (t.name = 'SCARLET'
		and not (mlth."cardTypeCode" in ('IN_K', 'N_SL', 'OT_SL', 'OV_SL') or mlch."cardTypeCode" = 'SVP10'))$$,
$$select '1' as id, null as target_values $$,
'source_values == 0',
'source.id = target.id',
null
),
---- 3.5: tierId
---- VVIP: [Expected output: tier: NVVIP, cobrand: SVP30]
---- CRYSTAL: [Expected output: tier: C_STF, CS_MS]
---- VEGA tierEndedAt: end of 2026: [Expected output: tier: VEGA, "accumulateSpending" < 2000000 ]
---- VEGA tierEndedAt: end of 2027: [Expected output: "accumulateSpending" >= 2000000 ]
---- CROWN tierEndedAt: end of 2027: [Expected output: "accumulateSpending" >= 300000 and accumulateSpending < 2000000, cobrand: STRIB,SPLD]
---- CROWN tierEndedAt: end of 2026: [Expected output: tier: IN_K and "occupation" in ('5', '11', '41','49','50') 
----												   tier: 'P_CR', 'N_CR', 'N_OX', 'OT_OX', 'OV_OX', 'IN_V1', 'IN_V2', 'IN_V3' 
----												   cobrand: 'BVS', 'SVP15', 'SVP20']
---- SCARLET tierEndedAt: end of 2027: [Expected output: "accumulateSpending" >= 40000 and accumulateSpending < 300000]
---- SCARLET tierEndedAt: end of 2026: [Expected output: tier: ('IN_K', 'N_SL', 'OT_SL', 'OV_SL'), cobrand: SVP10]
(
'loyalty_service',
'Member',
'tierId',
$$WITH cardtypecode_cobrand AS (
SELECT 'BVP05' AS cardTypeCode, 5 AS value
UNION ALL
SELECT 'SVP05', 5
UNION ALL
SELECT 'SVP10', 4
UNION ALL
SELECT 'BVS', 3
UNION ALL
SELECT 'SVP15', 3
UNION ALL
SELECT 'SVP20', 3
UNION ALL
SELECT 'STRIB', 2
UNION ALL
SELECT 'SPLD', 2
UNION ALL
SELECT 'SVP30', 1
),
max_cardtypecode_cobrand as 
(SELECT 
"memberId"
,"cardTypeCode"
,value
FROM (select 
mlch."memberId"
,mlch."cardTypeCode"
,cc.value
,ROW_NUMBER() OVER (PARTITION BY mlch."memberId" ORDER BY cc.value DESC) AS rn
from
staging_loyalty_service."MemberLegacyCoBrandHistory" as mlch
LEFT JOIN
cardtypecode_cobrand AS cc ON mlch."cardTypeCode" = cc.cardTypeCode
WHERE mlch."cardStatus" = 'ACTIVE'
)
WHERE rn = 1),
cardtypecode_tier as
(SELECT 'N_NY' AS cardTypeCode, 10 AS value
UNION ALL
SELECT 'N_SL', 9
UNION ALL
SELECT 'OV_SL', 9
UNION ALL
SELECT 'OT_SL', 9
UNION ALL
SELECT 'P_CR', 8
UNION ALL
SELECT 'N_CR', 8
UNION ALL
SELECT 'N_OX', 7
UNION ALL
SELECT 'OT_OX', 7
UNION ALL
SELECT 'OV_OX', 7
UNION ALL
SELECT 'IN_V1', 6
UNION ALL
SELECT 'IN_V2', 5
UNION ALL
SELECT 'IN_V3', 5
UNION ALL
SELECT 'IN_K', 4
UNION ALL
SELECT 'VEGA', 3
UNION ALL
SELECT 'NVVIP', 2
UNION ALL
SELECT 'C_STF', 1
UNION ALL
SELECT 'CS_MS', 1
),
max_cardtypecode_tier as
(SELECT 
"memberId",
"cardTypeCode",
value
FROM (select
mlth."memberId"
,mlth."cardTypeCode"
,ct.value
,ROW_NUMBER() OVER (PARTITION BY mlth."memberId" ORDER BY ct.value DESC) AS rn
FROM staging_loyalty_service."MemberLegacyTierHistory" as mlth
LEFT JOIN
cardtypecode_tier AS ct ON mlth."cardTypeCode" = ct.cardTypeCode
WHERE mlth."cardStatus" = 'ACTIVE'
)
WHERE rn = 1
)
select '1' as id, count(1) as source_values 
from loyalty_service."Member" m 
left join loyalty_service."Tier" t on m."tierId" = t.id 
left join staging_loyalty_service.ulid_member um on m.id = um.ulid_id 
left join max_cardtypecode_tier mlth on um.id = mlth."memberId"
left join max_cardtypecode_cobrand mlch on um.id = mlch."memberId"
left join staging_loyalty_service."MemberProfile" mp on um.id = mp."memberId"
where 
	(t.name = 'VVIP' and not (mlth."cardTypeCode" = 'NVVIP' or mlch."cardTypeCode" = 'SVP30'))
	or (t.name = 'CRYSTAL' and not (mlth."cardTypeCode" in ('C_STF', 'CS_MS')))
	or (t.name = 'VEGA' and m."tierEndedAt" = '2026-12-31T16:59:59.000' and not (m."accumulateSpending" < 2000000))
	or (t.name = 'VEGA' and m."tierEndedAt" = '2027-12-31T16:59:59.000' and not (m."accumulateSpending" >= 2000000))
	or (t.name = 'CROWN' and m."tierEndedAt" = '2027-12-31T16:59:59.000' 
		and not ((m."accumulateSpending" >= 300000 and m."accumulateSpending" < 2000000) or mlch."cardTypeCode" in ('STRIB', 'SPLD')))
	or (t.name = 'CROWN' and m."tierEndedAt" = '2026-12-31T16:59:59.000' 
		and not (
			(mlth."cardTypeCode" = 'IN_K' and mp."occupation" in ('5', '11', '41','49','50'))
			or (mlth."cardTypeCode" in ('P_CR', 'N_CR', 'N_OX', 'OT_OX', 'OV_OX', 'IN_V1', 'IN_V2', 'IN_V3'))
			or (mlch."cardTypeCode" in ('BVS', 'SVP15', 'SVP20'))
		))
	or (t.name = 'SCARLET' and m."tierEndedAt" = '2027-12-31T16:59:59.000'
		and not ((m."accumulateSpending" >= 40000 and m."accumulateSpending" < 300000)))
	or (t.name = 'SCARLET' and m."tierEndedAt" = '2026-12-31T16:59:59.000'
		and not (mlth."cardTypeCode" in ('IN_K', 'N_SL', 'OT_SL', 'OV_SL') or mlch."cardTypeCode" = 'SVP10'))$$,
$$select '1' as id, null as target_values $$,
'source_values == 0',
'source.id = target.id',
null
),
---- 3.6: Mark tierEndAt as end of the year
(
'loyalty_service',
'Member',
'tierEndedAt',
$$select '1' as id, count(1) as source_values
from loyalty_service."Member"
where "tierEndedAt" not in ('2026-12-31T16:59:59.000', '2027-12-31T16:59:59.000')$$,
$$select '1' as id, null as target_values$$,
'source_values == 0',
'source.id = target.id',
null
),
---- 3.7: minimumTierInvitedId
---- VEGA: [Expected output: tier: VEGA]
---- CROWN: [Expected output: tier: IN_K and "occupation" in ('5', '11', '41','49','50') 
----						  tier: 'P_CR', 'N_CR', 'N_OX', 'OT_OX', 'OV_OX', 'IN_V1', 'IN_V2', 'IN_V3']
---- SCARLET: [Expected output: tier: ('IN_K', 'N_SL', 'OT_SL', 'OV_SL')]
(
'loyalty_service',
'Member',
'minimumTierInvitedId',
$$WITH cardtypecode_cobrand AS (
SELECT 'BVP05' AS cardTypeCode, 5 AS value
UNION ALL
SELECT 'SVP05', 5
UNION ALL
SELECT 'SVP10', 4
UNION ALL
SELECT 'BVS', 3
UNION ALL
SELECT 'SVP15', 3
UNION ALL
SELECT 'SVP20', 3
UNION ALL
SELECT 'STRIB', 2
UNION ALL
SELECT 'SPLD', 2
UNION ALL
SELECT 'SVP30', 1
),
max_cardtypecode_cobrand as 
(SELECT 
"memberId"
,"cardTypeCode"
,value
FROM (select 
mlch."memberId"
,mlch."cardTypeCode"
,cc.value
,ROW_NUMBER() OVER (PARTITION BY mlch."memberId" ORDER BY cc.value DESC) AS rn
from
staging_loyalty_service."MemberLegacyCoBrandHistory" as mlch
LEFT JOIN
cardtypecode_cobrand AS cc ON mlch."cardTypeCode" = cc.cardTypeCode
WHERE mlch."cardStatus" = 'ACTIVE'
)
WHERE rn = 1),
cardtypecode_tier as
(SELECT 'N_NY' AS cardTypeCode, 10 AS value
UNION ALL
SELECT 'N_SL', 9
UNION ALL
SELECT 'OV_SL', 9
UNION ALL
SELECT 'OT_SL', 9
UNION ALL
SELECT 'P_CR', 8
UNION ALL
SELECT 'N_CR', 8
UNION ALL
SELECT 'N_OX', 7
UNION ALL
SELECT 'OT_OX', 7
UNION ALL
SELECT 'OV_OX', 7
UNION ALL
SELECT 'IN_V1', 6
UNION ALL
SELECT 'IN_V2', 5
UNION ALL
SELECT 'IN_V3', 5
UNION ALL
SELECT 'IN_K', 4
UNION ALL
SELECT 'VEGA', 3
UNION ALL
SELECT 'NVVIP', 2
UNION ALL
SELECT 'C_STF', 1
UNION ALL
SELECT 'CS_MS', 1
),
max_cardtypecode_tier as
(SELECT 
"memberId",
"cardTypeCode",
value
FROM (select
mlth."memberId"
,mlth."cardTypeCode"
,ct.value
,ROW_NUMBER() OVER (PARTITION BY mlth."memberId" ORDER BY ct.value DESC) AS rn
FROM staging_loyalty_service."MemberLegacyTierHistory" as mlth
LEFT JOIN
cardtypecode_tier AS ct ON mlth."cardTypeCode" = ct.cardTypeCode
WHERE mlth."cardStatus" = 'ACTIVE'
)
WHERE rn = 1
)
select '1' as id, count(1) as source_values 
from loyalty_service."Member" m 
left join staging_loyalty_service.ulid_member um on m.id = um.ulid_id 
left join max_cardtypecode_tier mlth on um.id = mlth."memberId"
left join max_cardtypecode_cobrand mlch on um.id = mlch."memberId"
left join staging_loyalty_service."MemberProfile" mp on um.id = mp."memberId"
where 
	(m."minimumTierInvitedId" = 'VEGA' and not (mlth."cardTypeCode" = 'VEGA'))
	or (m."minimumTierInvitedId" = 'CROWN'
		and not (
			(mlth."cardTypeCode" = 'IN_K' and mp."occupation" in ('5', '11', '41','49','50'))
			or (mlth."cardTypeCode" in ('P_CR', 'N_CR', 'N_OX', 'OT_OX', 'OV_OX', 'IN_V1', 'IN_V2', 'IN_V3'))
		))
	or (m."minimumTierInvitedId" = 'SCARLET'
		and not (mlth."cardTypeCode" in ('IN_K', 'N_SL', 'OT_SL', 'OV_SL')))$$,
$$select '1' as id, null as target_values$$,
'source_values == 0',
'source.id = target.id',
null
),
---- 3.8: TierStartedAt = Go-Live date (2025-06-30 17:00:00)
(
'loyalty_service',
'Member',
'tierStartedAt',
$$select '1' as id, count(1) as source_values
from loyalty_service."Member"
where "tierStartedAt" <> '2025-06-30T17:00:00.000'$$,
$$select '1' as id, null as target_values$$,
'source_values == 0',
'source.id = target.id',
null
),
---- 4.1: Migrate non KBANK members, no KBANK info is migrated, only SCB members migrated
(
'loyalty_service',
'MemberCoBrandCard',
'id',
$$select '1' as id, count(1) as source_values
from loyalty_service."MemberCoBrandCard" mcbc
inner join (
	select *
	from staging_loyalty_service."MemberLegacyCoBrandHistory"
	where TRIM(source_cardtypecode) <> 'SCB' 
) mlch
on mcbc.id = mlch.id$$,
$$select '1' as id, null as target_values$$,
'source_values == 0',
'source.id = target.id',
null
),
---- 5: Member isActive Status
(
'loyalty_service',
'Member',
'isActive',
$$select '1' as id, count(1) as source_values
from loyalty_service."Member" m
left join (
	select "memberId"
	from loyalty_service."MemberLegacyCoBrandHistory"
	where "cardStatus" = 'ACTIVE'
) active_cobrand
on m.id = active_cobrand."memberId"
left join (
	select "memberId"
	from loyalty_service."MemberLegacyTierHistory"
	where "cardStatus" = 'ACTIVE'
) active_lv
on active_lv."memberId" = m.id
where (m."isActive" = false and (active_cobrand."memberId" is not null or active_lv."memberId" is not null))
	and (m."isActive" = true and (active_cobrand."memberId" is null and active_lv."memberId" is null))$$,
$$select '1' as id, null as target_values$$,
'source_values == 0',
'source.id = target.id',
null
),
---- 5.1: not migrate - KBANK only members
(
'loyalty_service',
'Member',
'reason',
$$select '1' as id, count(1) as source_values
from (
	select kbank."memberId" as "kbank_member_id", m.id as "member_id"
	from loyalty_service."MemberLegacyCoBrandHistory" kbank
	inner join loyalty_service."Member" m
	on kbank."memberId" = m.id
	where "cardTypeCode" in ('BVP05', 'BVP10', 'BVP15', 'BVP20', 'BVS')
) kbank
left join (
	select "memberId"
	from loyalty_service."MemberLegacyCoBrandHistory"
	where "cardTypeCode" not in ('BVP05', 'BVP10', 'BVP15', 'BVP20', 'BVS')
) others_bank1
on kbank."kbank_member_id" = others_bank1."memberId"
left join (
	select "memberId"
	from loyalty_service."MemberLegacyTierHistory"
) others_bank2
on kbank."kbank_member_id" = others_bank2."memberId"
where (others_bank1."memberId" is not null or others_bank2."memberId" is not null) 
	and kbank.member_id is null$$,
$$select '1' as id, null as target_values$$,
'source_values == 0',
'source.id = target.id',
null
),
---- 5.2: not migrated list
(
'loyalty_service',
'Member',
'reason',
$$select '1' as id, count(1) as source_values
from loyalty_service."Member" m 
left join staging_loyalty_service.ulid_member um 
on m.id = um.ulid_id
inner join public.members_expired_notmigrate mb
on um.id = mb."memberId"
where m."isActive" = false$$,
$$select '1' as id, null as target_values$$,
'source_values == 0','source.id = target.id', 
null
),
---- 7: suspended
(
'loyalty_service',
'Member',
'reason',
$$select '1' as id, count(1) as source_values
from loyalty_service."Member" m 
left join staging_loyalty_service.ulid_member um 
on m.id = um.ulid_id
inner join public.members_blacklist mb
on um.id = mb."memberId"
where m.reason <> 'Suspended'$$,
$$select '1' as id, null as target_values$$,
'source_values == 0','source.id = target.id', 
null
),
---- 9: Point Conversion (WalletBalance)
(
'point_service',
'WalletBalance',
'amount',
$$select "id", "amount" as source_values
from staging_point_service."WalletBalance"$$,
$$select "id", CASE WHEN "walletCode" = 'CARAT_WALLET' then "amount"/4 ELSE amount END as target_values
from staging_point_service."WalletBalanceTemp"$$,
'"source_values" == "target_values"',
'source.id = target.id',
'100000'
),
(
'point_service',
'WalletBalance',
'amount',
$$select 1 as "id", sum("amount") as source_values
from staging_point_service."WalletBalanceTemp" wb
INNER JOIN (SELECT member.id, member."gwlNo" FROM loyalty_service."Member" member) AS sm
ON wb."memberId" = sm."gwlNo"$$,
$$select 1 as "id", sum("amount") as target_values
from point_service."WalletBalance"$$,
'"source_values" == "target_values"',
'source.id = target.id',
null
),
---- 9: Point Conversion (WalletTransaction)
(
'point_service',
'WalletTransaction',
'amount',
$$select "id", "amount"/4 as source_values
from point_service."WalletTransaction"
where "walletCode" = 'CARAT_WALLET'$$,
$$select uwt.ulid_id as "id", "amount" as target_values
from staging_point_service."WalletTransaction" wt
left join staging_point_service."ulid_WalletTransaction" uwt
on wt.id = uwt.id
where "walletCode" = 'CARAT_WALLET'$$,
'"source_values" == "target_values"',
'source.id = target.id',
'100000'
),
---- 11: Flag inactive members (totalAccumSpendable = 0)
(
'loyalty_service',
'SalesTransaction',
'totalAccumSpendableAmount',
$$select '1' as id, count(1) as source_values
from loyalty_service."SalesTransaction" st
left join loyalty_service."Member" m
on st."memberId" = m.id
where m."isActive" = false and "totalAccumSpendableAmount" <> 0$$,
$$select '1' as id, null as target_values$$,
'source_values == 0',
'source.id = target.id',
null
),
-- 11.1: reason = SMC Expired
(
'loyalty_service',
'Member',
'reason',
$$select '1' as id, count(1) as source_values
from loyalty_service."Member" m
where m.reason = 'SMC Expired' and "isActive" = True$$,
$$select '1' as id, null as target_values$$,
'source_values == 0',
'source.id = target.id',
null
),
-- expired not migrate members list
(
'loyalty_service',
'Member',
'reason',
$$select '1' as id, count(1) as source_values 
from loyalty_service."Member" m
inner join (
	select "memberId"
	from public.members_blacklist
	union
	select "memberId"
	from public.members_expired_notmigrate
) suspended_and_notmigrate
on m.id = suspended_and_notmigrate."memberId"
where m.reason = 'SMC Expired'$$,
$$select '1' as id, null as target_values$$,
'source_values == 0',
'source.id = target.id',
null
),
---- 12: SalesTransaction refundStatus
(
'partner_service',
'SalesTransaction',
'refundStatus',
$$select '1' as id, count(1) as source_values
FROM partner_service."SalesTransaction" st
LEFT JOIN public."RefundSalesTransactionId" rsti
ON st.id = cast(rsti."salesTransactionId" as bigint)
where rsti."salesTransactionId" is not null and st."refundStatus" <> 'FULL'$$,
$$select '1' as id, null as target_values$$,
'source_values == 0',
'source.id = target.id',
null
),
---- 12.2: Copy data from SalesTranItem to SalesTransRefundItem table, where SalesTransRefundTable.id = SalesTransTable.id
(
'partner_service',
'RefundSalesTransactionItem',
'id',
$$select '1' as id, count(1) as source_values
FROM partner_service."SalesTransactionItem" sti     
INNER JOIN partner_service."RefundSalesTransaction" rst
ON sti."salesTransactionId" = rst."salesTransactionId"$$,
$$select '1' as id, count(1) as target_values
FROM partner_service."RefundSalesTransactionItem"$$,
'"source_values" == "target_values"',
'source.id = target.id',
null
),
---- 14: SalesTransaction - partner/brand/branch mapping (loyalty_service)
(
'loyalty_service',
'SalesTransaction',
'branchCode',
$$select '1' as id, count(1) as source_values
from staging_loyalty_service."SalesTransaction" source
INNER JOIN (SELECT member.id, member."gwlNo" FROM loyalty_service."Member" member) AS sm
ON source."memberId" = sm."gwlNo"
left join (
	select st."id", sbm."site_codes" 
	from loyalty_service."SalesTransaction" st 
	left join (
		select "gwl_branch_code", array_agg("site_code") as site_codes 
		from (
			select site_code, gwl_branch_code
			from public.site_branch_mapping 
			union all 
			select * from (
				values 
					('', 'KPC_RANGNAM'),
					(NULL, 'KPC_RANGNAM'),
					('9999', 'KPD_DMK_AIRPORT'),
					('9999', 'KPD_SVB_AIRPORT')
			) as temp("site_code", "gwl_branch_code")
		) temp2 
		group by "gwl_branch_code"
	) sbm 
	on st."branchCode" = sbm."gwl_branch_code"
) target
on source.id = target.id
WHERE NOT EXISTS (
    SELECT 1 FROM UNNEST(target.site_codes) AS x(site_code)
    WHERE source."branchCode" = x.site_code
) and not (source."branchCode" = '9999' and source."brandCode" is null)$$,
$$select '1' as id, null as target_values$$,
'source_values == 0',
'source.id = target.id',
null
),
---- 14: SalesTransaction - partner/brand/branch mapping (partner_service)
(
'partner_service',
'SalesTransaction',
'branchId',
$$select '1' as id, count(1) as source_values
from staging_partner_service."SalesTransaction" source
INNER JOIN (SELECT member.id, member."gwlNo" FROM loyalty_service."Member" member) AS sm
ON source."memberId" = sm."gwlNo"
left join (
	select st."id", sbm."site_codes" 
	from partner_service."SalesTransaction" st 
	left join partner_service."Branch" b
	on st."branchId" = b.id 
	left join (
		select "gwl_branch_code", array_agg("site_code") as site_codes 
		from (
			select site_code, gwl_branch_code
			from public.site_branch_mapping 
			union all 
			select * from (
				values 
					('', 'KPC_RANGNAM'),
					(NULL, 'KPC_RANGNAM'),
					('9999', 'KPD_DMK_AIRPORT'),
					('9999', 'KPD_SVB_AIRPORT')
			) as temp("site_code", "gwl_branch_code")
		) temp2 
		group by "gwl_branch_code"
	) sbm 
	on b."code" = sbm."gwl_branch_code"
) target
on source.id = target.id
WHERE NOT EXISTS (
    SELECT 1 FROM UNNEST(target.site_codes) AS x(site_code)
    WHERE source."branchId" = x.site_code
) and not (source."branchId" = '9999' and source."brandId" is null)$$,
$$select '1' as id, null as target_values$$,
'source_values == 0',
'source.id = target.id',
null
),
---- 14.1: WalletActivity - partner/brand/branch mapping
(
'point_service',
'WalletActivity',
'branchCode',
$$select '1' as id, count(1) as source_values
from staging_point_service."WalletActivity" source
left join staging_point_service."ulid_WalletActivity" uwa 
on source.id = uwa.id
INNER JOIN (SELECT member.id, member."gwlNo" FROM loyalty_service."Member" member) AS sm
ON source."memberId" = sm."gwlNo"
left join (
	select st."id", sbm."site_codes" 
	from point_service."WalletActivity" st 
	left join (
		select "gwl_branch_code", array_agg("site_code") as site_codes 
		from (
			select site_code, gwl_branch_code
			from public.site_branch_mapping 
			union all 
			select * from (
				values 
					('', 'KPC_RANGNAM'),
					(NULL, 'KPC_RANGNAM'),
					('9999', 'KPD_DMK_AIRPORT'),
					('9999', 'KPD_SVB_AIRPORT')
			) as temp("site_code", "gwl_branch_code")
		) temp2 
		group by "gwl_branch_code"
	) sbm 
	on st."branchCode" = sbm."gwl_branch_code"
) target
on uwa.ulid_id = target.id
WHERE NOT EXISTS (
    SELECT 1 FROM UNNEST(target.site_codes) AS x(site_code)
    WHERE source."branchCode" = x.site_code
) and not (source."branchCode" = '9999' and source."brandCode" is null)$$,
$$select '1' as id, null as target_values$$,
'source_values == 0',
'source.id = target.id',
null
),
-- 15: Push expiry date of wallet transactions/balance to end of the year (WalletBalance)
(
'point_service',
'WalletBalance',
'expiredAt',
$$select '1' as id, count(1) as source_values
from (
	select id
	from staging_point_service."WalletBalance"
	where "walletCode" = 'CARAT_WALLET' and "expiredAt" >= '2099-01-01'
) source
inner join (
	select id
	from staging_point_service."WalletBalanceTemp"
	where "walletCode" = 'CARAT_WALLET' and "expiredAt" is not null
) target
on source.id = target.id$$,
$$select '1' as id, null as target_values$$,
'source_values == 0',
'source.id = target.id',
null
),
(
'point_service',
'WalletBalance',
'expiredAt',
$$select '1' as id, count(1) as source_values
from (
	select id
	from staging_point_service."WalletBalance"
	where "walletCode" = 'CARAT_WALLET' and "expiredAt" > now() and "expiredAt" < '2099-01-01'
) source
inner join (
	select id
	from staging_point_service."WalletBalanceTemp"
	where "walletCode" = 'CARAT_WALLET' and "expiredAt" <> TO_TIMESTAMP(EXTRACT(YEAR FROM "expiredAt") || '-12-31 16:59:59.999', 'YYYY-MM-DD HH24:MI:SS.MS')
) target
on source.id = target.id$$,
$$select '1' as id, null as target_values$$,
'source_values == 0',
'source.id = target.id',
null
),
-- 15: Push expiry date of wallet transactions/balance to end of the year (WalletTransaction)
(
'point_service',
'WalletTransaction',
'expiredAt',
$$select '1' as id, count(1) as source_values
from (
	select id
	from staging_point_service."WalletTransaction"
	where "walletCode" = 'CARAT_WALLET' and "expiredAt" >= '2099-01-01'
) source
inner join (
	select id
	from point_service."WalletTransaction"
	where "walletCode" = 'CARAT_WALLET' and "expiredAt" is not null
) target
on source.id = target.id$$,
$$select '1' as id, null as target_values$$,
'source_values == 0',
'source.id = target.id',
null
),
(
'point_service',
'WalletTransaction',
'expiredAt',
$$select '1' as id, count(1) as source_values
from (
	select id
	from staging_point_service."WalletTransaction"
	where "walletCode" = 'CARAT_WALLET' and "expiredAt" > now() and "expiredAt" < '2099-01-01'
) source
inner join (
	select id
	from point_service."WalletTransaction"
	where "walletCode" = 'CARAT_WALLET' and "expiredAt" <> TO_TIMESTAMP(EXTRACT(YEAR FROM "expiredAt") || '-12-31 16:59:59.999', 'YYYY-MM-DD HH24:MI:SS.MS')
) target
on source.id = target.id$$,
$$select '1' as id, null as target_values$$,
'source_values == 0',
'source.id = target.id',
null
),
---- direct SalesTransactionWalletActivity: copy from PointService.WalletActivity
(
'partner_service',
'SalesTransactionWalletActivity',
'id',
$$select '1' as id, count(1) as source_values
from staging_point_service."WalletActivity" wa
INNER JOIN partner_service."SalesTransaction" st 
ON wa."refId" = st.id::text
WHERE "refType" = 'SALES_TRANSACTION'$$,
$$select '1' as id, count(1) as target_values
from partner_service."SalesTransactionWalletActivity"$$,
'"source_values" == "target_values"',
'source.id = target.id',
null
),
---- 18: MemberProfile , If has (province OR district OR subdistrict), then,"CountryCode" = THA, else, NULL
(
'loyalty_service',
'MemberProfile',
'countryCode',
$$select '1' as id, count(1) as source_values
from loyalty_service."MemberProfile"
where "countryCode" is null and "province" is not null$$,
$$select '1' as id, null as target_values$$,
'source_values == 0',
'source.id = target.id',
null
),
---- 21: copy SalesTransaction.createdAt to completedAt (loyalty_service)
(
'loyalty_service',
'SalesTransaction',
'createdAt',
$$select '1' as id, count(1) as source_values
from loyalty_service."SalesTransaction"
where "createdAt" <> "completedAt"$$,
$$select '1' as id, null as target_values$$,
'source_values == 0',
'source.id = target.id',
null
),
---- 21: copy SalesTransaction.createdAt to completedAt (partner_service)
(
'partner_service',
'SalesTransaction',
'createdAt',
$$select '1' as id, count(1) as source_values
from partner_service."SalesTransaction"
where "createdAt" <> "completedAt"$$,
$$select '1' as id, null as target_values$$,
'source_values == 0',
'source.id = target.id',
null
),
---- 25: migrate partner service RST to loyalty service RefundSalesTransaction
(
'loyalty_service',
'RefundSalesTransaction',
'id',
$$select '1' as id, count(1) as source_values
from partner_service."RefundSalesTransaction"$$,
$$select '1' as id, count(1) as target_values
from loyalty_service."RefundSalesTransaction"$$,
'"source_values" == "target_values"',
'source.id = target.id',
null
),
---- 27: Migrate MemberCoupon table
(
'engagement_service',
'MemberCoupon',
'id',
$$select '1' as id, count(1) as source_values
from staging_engagement_service."MemberPrivilege" smp
inner join (SELECT member.id, member."gwlNo" FROM loyalty_service."Member" member) sm 
on smp."memberId" = sm."gwlNo"
left join staging_engagement_service."ulid_MemberPrivilege" usmp
on smp.id = usmp.id
left join engagement_service."MemberCoupon" mc
on usmp.ulid_id = mc.id
where smp."createdAt" >= '2024-12-31T17:00:00.000Z'
and mc.id is null$$,
$$select '1' as id, null as target_values$$,
'source_values == 0',
'source.id = target.id',
null
),
---- 28: Migrate MemberCouponActivity table
(
'engagement_service',
'MemberCouponActivity',
'id',
$$select '1' as id, count(1) as source_values
from staging_engagement_service."MemberPrivilege" smp
inner join (SELECT member.id, member."gwlNo" FROM loyalty_service."Member" member) sm 
on smp."memberId" = sm."gwlNo"
left join staging_engagement_service."ulid_MemberPrivilege" usmp
on smp.id = usmp.id
left join engagement_service."MemberCouponActivity" mca
on usmp.ulid_id = mca.id
where smp."createdAt" >= '2024-12-31T17:00:00.000Z'
and mca.id is null$$,
$$select '1' as id, null as target_values$$,
'source_values == 0',
'source.id = target.id',
null
),
---- 29: Modify MemberPrivilege table
(
'engagement_service',
'MemberPrivilege',
'memberId',
$$select '1' as id, count(1) as source_values
from staging_engagement_service."MemberPrivilege" smp
inner join (SELECT member.id, member."gwlNo" FROM loyalty_service."Member" member) sm 
on smp."memberId" = sm."gwlNo"
left join engagement_service."MemberPrivilege" mp
on sm.id = mp."memberId"
where smp."createdAt" >= '2024-12-31T17:00:00.000Z'
and mp.id is null$$,
$$select '1' as id, null as target_values$$,
'source_values == 0',
'source.id = target.id',
null
),
----- direct: loyalty_service.MemberLegacyTierHistory
(
'loyalty_service',
'MemberLegacyTierHistory',
'id',
$$select '1' as id, count(1) as source_values
from staging_loyalty_service."MemberLegacyTierHistory" smp
inner join (SELECT member.id, member."gwlNo" FROM loyalty_service."Member" member) sm 
on smp."memberId" = sm."gwlNo"$$,
$$select '1' as id, count(1) as target_values
from loyalty_service."MemberLegacyTierHistory" smp$$,
'"source_values" == "target_values"',
'source.id = target.id',
null
),
----- direct: loyalty_service.MemberLegacyCoBrandHistory
(
'loyalty_service',
'MemberLegacyCoBrandHistory',
'id',
$$select '1' as id, count(1) as source_values
from staging_loyalty_service."MemberLegacyCoBrandHistory" smp
inner join (SELECT member.id, member."gwlNo" FROM loyalty_service."Member" member) sm 
on smp."memberId" = sm."gwlNo"$$,
$$select '1' as id, count(1) as target_values
from loyalty_service."MemberLegacyCoBrandHistory" smp$$,
'"source_values" == "target_values"',
'source.id = target.id',
null
),
----- direct: loyalty_service.StaffProfile
(
'loyalty_service',
'StaffProfile',
'id',
$$select '1' as id, count(1) as source_values
from staging_loyalty_service."StaffProfile" smp
inner join (SELECT member.id, member."gwlNo" FROM loyalty_service."Member" member) sm 
on smp."memberId" = sm."gwlNo"$$,
$$select '1' as id, count(1) as target_values
from loyalty_service."StaffProfile" smp$$,
'"source_values" == "target_values"',
'source.id = target.id',
null
),
----- direct: partner_service.RefundSalesTransaction
(
'partner_service',
'RefundSalesTransaction',
'id',
$$select '1' as id, count(1) as source_values
from staging_partner_service."RefundSalesTransaction" rst
inner join (
	select id
	from partner_service."SalesTransaction"
) st
on rst."salesTransactionId" = st.id$$,
$$select '1' as id, count(1) as target_values
from partner_service."RefundSalesTransaction" smp$$,
'"source_values" == "target_values"',
'source.id = target.id',
null
),
----- direct: partner_service.SalesTransactionItem
(
'partner_service',
'SalesTransactionItem',
'id',
$$select '1' as id, count(1) as source_values
from staging_partner_service."SalesTransactionItem" rst
inner join (
	select id
	from partner_service."SalesTransaction"
) st
on rst."salesTransactionId" = st.id$$,
$$select '1' as id, count(1) as target_values
from partner_service."SalesTransactionItem" smp$$,
'"source_values" == "target_values"',
'source.id = target.id',
null
),
----- direct: partner_service.SalesTransactionBurnPayment
(
'partner_service',
'SalesTransactionBurnPayment',
'id',
$$select '1' as id, count(1) as source_values
from staging_partner_service."SalesTransactionBurnPayment" rst
inner join (
	select id
	from partner_service."SalesTransaction"
) st
on rst."salesTransactionId" = st.id$$,
$$select '1' as id, count(1) as target_values
from partner_service."SalesTransactionBurnPayment" smp$$,
'"source_values" == "target_values"',
'source.id = target.id',
null
),
----- direct: partner_service.WalletAdjustmentTransaction
(
'point_service',
'WalletAdjustmentTransaction',
'id',
$$select '1' as id, count(1) as source_values
from staging_point_service."WalletAdjustmentTransaction" wat
inner join loyalty_service."Member" sm
on wat."memberId" = sm."gwlNo"$$,
$$select '1' as id, count(1) as target_values
from point_service."WalletAdjustmentTransaction" wat$$,
'"source_values" == "target_values"',
'source.id = target.id',
null
),
---- UAT 2: direct totalCaratEarnableAmount from temp database
(
'partner_service',
'SalesTransaction',
'totalCaratEarnableAmount',
$$select '1' as id, count(1) as source_values
from partner_service."SalesTransaction" st 
left join staging_partner_service."SalesTransaction" sst
on st.id = sst.id
where st."totalCaratEarnableAmount" <> sst."totalEarnableAmount"$$,
$$select '1' as id, null as target_values$$,
'source_values == 0',
'source.id = target.id',
null
),
---- UAT 7: direct normalPointEarned from temp database
(
'partner_service',
'SalesTransactionItem',
'normalPointEarned',
$$select '1' as id, count(1) as source_values
from partner_service."SalesTransactionItem" sti
left join staging_partner_service."SalesTransactionItem" ssti
on sti.id = ssti.id
where sti."normalPointEarned" <> ssti."normalPointEarned"$$,
$$select '1' as id, null as target_values$$,
'source_values == 0',
'source.id = target.id',
null
),
---- UAT 11: every member has at least 1 cash wallet
(
'point_service',
'WalletBalance',
'memberId',
$$select '1' as id, count(1) as source_values
from loyalty_service."Member" m
left join (
	select distinct "memberId"
	from point_service."WalletBalance"
	where "walletCode" = 'CASH_WALLET'
) wb
on m.id = wb."memberId"
where wb."memberId" is null$$,
$$select '1' as id, null as target_values$$,
'source_values == 0',
'source.id = target.id',
null
),
---- UAT 13: MemberCoBrandCard Status
(
'loyalty_service',
'MemberCoBrandCard',
'status',
$$select '1' as id, count(1) as source_values
from (
	select "id", "status" as target_status
	from loyalty_service."MemberCoBrandCard"
) target
left join ( 
	select "id", CASE WHEN "cardStatus" in ('ACTIVE', 'NEW CARD') THEN 'ACTIVE'
	     WHEN "cardStatus" = 'Canceled Card' THEN 'INACTIVE'
	     ELSE 'INACTIVE' END as source_status
	from staging_loyalty_service."MemberLegacyCoBrandHistory"
WHERE TRIM(source_cardtypecode) = 'SCB'
) source
on source.id = target.id
where source.source_status <> target.target_status$$,
$$select '1' as id, null as target_values$$,
'source_values == 0',
'source.id = target.id',
null
),
---- UAT 13: MemberCoBrandCard.cardReason
(
'loyalty_service',
'MemberCoBrandCard',
'cardReason',
$$select '1' as id, count(1) as source_values
from (
	select "id", "cardReason" as target_reason
	from loyalty_service."MemberCoBrandCard"
) target
left join ( 
	select "id", CASE WHEN "cardReason" in ('บัตรหมดอายุ (ยกเลิก)', 'ยกเลิกบัตรเครดิตร่วม KPG-SCB', 'บัตรเครดิตร่วมหมดอายุ (ยกเลิก)', 'ยกเลิกบัตรเครดิตร่วม KPG - KBANK') THEN 'CLOSED'
    	 WHEN "cardReason" in ('สมาชิกใหม่ (GOLD CARD)', 'สมาชิกบัตรเครดิตร่วม KPG-SCB') THEN 'NEW_CARD'
    	 WHEN "cardReason" = 'ออกบัตร CONDITION ผิด (ยกเลิก)' THEN 'CLOSED'
    	 when "cardReason" in ('ออกบัตรใหม่ (ต่ออายุสถานะภาพ)', 'ออกบัตรใหม่ (ทดแทน)', 'สมาชิกสมัครใหม่ (SILVER CARD)', 'ออกบัตรใหม่ (SPECIAL UPGRADE)', 'บัตรใหม่ (UPGRADE BY PROMOTION)') THEN 'RE_ISSUED_NEW_CARD_NO'
   	end as source_reason
	from staging_loyalty_service."MemberLegacyCoBrandHistory"
WHERE TRIM(source_cardtypecode) = 'SCB'
) source
on source.id = target.id
where source.source_reason <> target.target_reason$$,
$$select '1' as id, null as target_values$$,
'source_values == 0',
'source.id = target.id',
null
),
---- UAT 17: mask cobrand card no (embossNo) as 12345678xxxx1234 (16 digits)
(
'loyalty_service',
'MemberCoBrandCard',
'cardNo',
$$select '1' as id, count(1) as source_values
from loyalty_service."MemberCoBrandCard"
where length("cardNo") = 16 and substring("cardNo", 9, 4) <> 'xxxx'$$,
$$select '1' as id, null as target_values$$,
'source_values == 0',
'source.id = target.id', 
null
),
---- UAT 22: Member Handling (add isCoBrandNonMember flag)
(
'loyalty_service',
'Member',
'isCoBrandNonMember',
$$select '1' as id, count(1) as source_values
from (
	select id, "isCoBrandNonMember"
	from loyalty_service."Member"
	where "isCoBrandNonMember" <> true
) m
left join (
	select "memberId"
	from staging_loyalty_service."MemberLegacyCoBrandHistory"
	where "cardStatus" = 'ACTIVE' and trim("source_cardtypecode") = 'SCB'
) active_cobrand
on m.id = active_cobrand."memberId"
left join (
	select "memberId"
	from staging_loyalty_service."MemberLegacyTierHistory"
	where "cardStatus" = 'ACTIVE'
) active_lv
on active_lv."memberId" = active_cobrand."memberId"
where active_lv."memberId" is null and active_cobrand."memberId" is not null$$,
$$select '1' as id, null as target_values$$,
'source_values == 0',
'source.id = target.id',
null
),
----- direct: partner_service.SalesTransactionPayment
(
'partner_service',
'SalesTransactionPayment',
'id',
$$select '1' as id, count(1) as source_values
from staging_partner_service."SalesTransactionPayment" stp
INNER JOIN partner_service."SalesTransaction" st
on stp."salesTransactionId" = st.id$$,
$$select '1' as id, count(1) as target_values
from partner_service."SalesTransactionPayment" stp$$,
'"source_values" == "target_values"',
'source.id = target.id',
null
),
----- count: loyalty_service.MemberCoBrandCard
(
'loyalty_service',
'MemberCoBrandCard',
'id',
$$select '1' as id, count(1) as source_values
from staging_loyalty_service."MemberCoBrandCard" AS mcbc
INNER JOIN loyalty_service."Member" AS sm ON mcbc."memberId" = sm."gwlNo"
INNER JOIN loyalty_service."CoBrand" AS cb ON substring(mcbc."cardNo",1,8) = cb."cardBin"$$,
$$select '1' as id, count(1) as target_values
from loyalty_service."MemberCoBrandCard" stp$$,
'"source_values" == "target_values"',
'source.id = target.id',
null
),
----- count: loyalty_service.MemberProfile
(
'loyalty_service',
'MemberProfile',
'id',
$$select '1' as id, count(1) as source_values
from staging_loyalty_service."MemberProfile" AS mp 
INNER JOIN loyalty_service."Member" AS sm ON sm."gwlNo" = mp."memberId"$$,
$$select '1' as id, count(1) as target_values
from loyalty_service."MemberProfile" mp$$,
'"source_values" == "target_values"',
'source.id = target.id',
null
),
----- count: loyalty_service.SalesTransaction
(
'loyalty_service',
'SalesTransaction',
'id',
$$select '1' as id, count(1) as source_values
from staging_loyalty_service."SalesTransaction" st
INNER JOIN loyalty_service."Member" AS sm ON st."memberId"  = sm."gwlNo"$$,
$$select '1' as id, count(1) as target_values
from loyalty_service."SalesTransaction" st$$,
'"source_values" == "target_values"',
'source.id = target.id',
null
),
----- count: partner_service.SalesTransaction
(
'partner_service',
'SalesTransaction',
'id',
$$select '1' as id, count(1) as source_values
from staging_partner_service."SalesTransaction" st
INNER JOIN loyalty_service."Member" AS sm ON st."memberId"  = sm."gwlNo"$$,
$$select '1' as id, count(1) as target_values
from partner_service."SalesTransaction" st$$,
'"source_values" == "target_values"',
'source.id = target.id',
null
),
----- count: point_service.WalletActivity
(
'point_service',
'WalletActivity',
'id',
$$select '1' as id, count(1) as source_values
FROM staging_point_service."WalletActivity" wa
INNER JOIN loyalty_service."Member" AS sm ON wa."memberId" = sm."gwlNo"$$,
$$select '1' as id, count(1) as target_values
from point_service."WalletActivity" st$$,
'"source_values" == "target_values"',
'source.id = target.id',
null
),
----- count: point_service.WalletTransaction
(
'point_service',
'WalletTransaction',
'id',
$$select '1' as id, count(1) as source_values
FROM (
	select *,
		CASE 
            WHEN wt."walletCode" = 'CARAT_WALLET' and EXTRACT(YEAR FROM wt."expiredAt") = 2099 THEN NULL
            WHEN wt."walletCode" = 'CARAT_WALLET' and wt."expiredAt" > now()
                THEN TO_TIMESTAMP(EXTRACT(YEAR FROM wt."expiredAt") || '-12-31 16:59:59.999', 'YYYY-MM-DD HH24:MI:SS.MS')
            ELSE wt."expiredAt"
        END AS "new_expiredAt"
	from staging_point_service."WalletTransaction" wt
) wt
INNER JOIN loyalty_service."Member" AS sm
ON wt."memberId" = sm."gwlNo"
LEFT JOIN staging_point_service."ulid_WalletActivity" wa_ulid
ON wt."walletActivityId" = wa_ulid.id
INNER JOIN (select id from point_service."WalletActivity") wa
ON wa_ulid.ulid_id = wa.id
INNER JOIN point_service."WalletBalance" wb
ON sm.id = wb."memberId"
AND wt."walletCode" = wb."walletCode"
AND coalesce(wt."new_expiredAt", TIMESTAMP '2099-01-01 00:00:00') = coalesce(wb."expiredAt", TIMESTAMP '2099-01-01 00:00:00')$$,
$$select '1' as id, count(1) as target_values
FROM point_service."WalletTransaction" wt$$,
'"source_values" == "target_values"',
'source.id = target.id',
null
)
;