import pandas as pd
import numpy as np
import psycopg2
from configs.db_configs import DBConfig
from validations.validation_helpers_xc_logic import (
    build_validation_dataframe, 
    insert_dataframe_into_table, 
    validate_all_condition,
    get_summary_results
)

temp_dbname = DBConfig.temp_dbname
temp_user = DBConfig.temp_user
temp_password = DBConfig.temp_password
temp_host = DBConfig.temp_host
temp_port = DBConfig.temp_port

gwl_dbname = DBConfig.gwl_dbname
gwl_user = DBConfig.gwl_user
gwl_password = DBConfig.gwl_password
gwl_host = DBConfig.gwl_host
gwl_port = DBConfig.gwl_port


def validate_engagement_service():
    '''
    Function to validate engagement_service schema
    '''
    temp_db_conn = psycopg2.connect(
        dbname=temp_dbname,
        user=temp_user,
        password=temp_password,
        host=temp_host,
        port=temp_port
    )
    gwl_db_conn = psycopg2.connect(
        dbname=gwl_dbname,
        user=gwl_user,
        password=gwl_password,
        host=gwl_host,
        port=gwl_port
    )
    try:
        schema_list = ['engagement_service']
        validation_df = build_validation_dataframe(temp_db_conn, gwl_db_conn, schema_list)
        query = '''
            select
                gwl_table_schema,
                gwl_table_name,
                gwl_column_name,
                check_data_type,
                check_row_count,
                check_null_count,
                check_unique_count,
                check_sum_value,
                check_min_value,
                check_max_value,
                check_samples_uniqueness,
                n_samples_uniqueness,
                check_transformation,
                is_migrated
            from validation."ValidationBasicConditions_xc_logic"
        '''
        all_condition_df = pd.read_sql(query, gwl_db_conn)

        validation_df = pd.merge(
            validation_df, 
            all_condition_df, 
            on=['gwl_table_schema', 'gwl_table_name', 'gwl_column_name'],
            how='left'
        )
        validation_df = validation_df.replace({np.nan: None, pd.NaT: None})
        # Validate with condition
        validation_df['checking_results'] = validation_df.apply(validate_all_condition, axis=1, args=(gwl_db_conn,))
        # Splitting the tuple column into separate columns
        validation_df[['is_match', 'error_message']] = validation_df['checking_results'].apply(pd.Series)
        # Dropping the original tuple column (optional)
        validation_df.drop(columns=['checking_results'], inplace=True)
        # Insert validation resutls
        insert_dataframe_into_table(validation_df.drop(columns=['is_migrated']), 'validation."ValidationResults_xc_logic"', gwl_db_conn)
        # Get summary results for only migrated table
        summary_validation_df = get_summary_results(validation_df[validation_df['is_migrated'] == True])
        # Insert summary validation results
        insert_dataframe_into_table(summary_validation_df, 'validation."ValidationSummaryResults_xc_logic"', gwl_db_conn)
    finally:
        if temp_db_conn:
            temp_db_conn.close()
        if gwl_db_conn:
            gwl_db_conn.close()

def validate_loyalty_service():
    '''
    Function to validate loyalty_service schema
    '''
    temp_db_conn = psycopg2.connect(
        dbname=temp_dbname,
        user=temp_user,
        password=temp_password,
        host=temp_host,
        port=temp_port
    )
    gwl_db_conn = psycopg2.connect(
        dbname=gwl_dbname,
        user=gwl_user,
        password=gwl_password,
        host=gwl_host,
        port=gwl_port
    )
    try:
        schema_list = ['loyalty_service']
        validation_df = build_validation_dataframe(temp_db_conn, gwl_db_conn, schema_list)
        query = '''
            select
                gwl_table_schema,
                gwl_table_name,
                gwl_column_name,
                check_data_type,
                check_row_count,
                check_null_count,
                check_unique_count,
                check_sum_value,
                check_min_value,
                check_max_value,
                check_samples_uniqueness,
                n_samples_uniqueness,
                check_transformation,
                is_migrated
            from validation."ValidationBasicConditions_xc_logic"
        '''
        all_condition_df = pd.read_sql(query, gwl_db_conn)

        validation_df = pd.merge(
            validation_df, 
            all_condition_df, 
            on=['gwl_table_schema', 'gwl_table_name', 'gwl_column_name'],
            how='left'
        )
        validation_df = validation_df.replace({np.nan: None, pd.NaT: None})
        # Validate with condition
        validation_df['checking_results'] = validation_df.apply(validate_all_condition, axis=1, args=(gwl_db_conn,))
        # Splitting the tuple column into separate columns
        validation_df[['is_match', 'error_message']] = validation_df['checking_results'].apply(pd.Series)
        # Dropping the original tuple column (optional)
        validation_df.drop(columns=['checking_results'], inplace=True)
        # Insert validation resutls
        insert_dataframe_into_table(validation_df.drop(columns=['is_migrated']), 'validation."ValidationResults_xc_logic"', gwl_db_conn)
        # Get summary results for only migrated table
        summary_validation_df = get_summary_results(validation_df[validation_df['is_migrated'] == True])
        # Insert summary validation results
        insert_dataframe_into_table(summary_validation_df, 'validation."ValidationSummaryResults_xc_logic"', gwl_db_conn)
    finally:
        if temp_db_conn:
            temp_db_conn.close()
        if gwl_db_conn:
            gwl_db_conn.close()

def validate_partner_service():
    '''
    Function to validate partner_service schema
    '''
    temp_db_conn = psycopg2.connect(
        dbname=temp_dbname,
        user=temp_user,
        password=temp_password,
        host=temp_host,
        port=temp_port
    )
    gwl_db_conn = psycopg2.connect(
        dbname=gwl_dbname,
        user=gwl_user,
        password=gwl_password,
        host=gwl_host,
        port=gwl_port
    )
    try:
        schema_list = ['partner_service']
        validation_df = build_validation_dataframe(temp_db_conn, gwl_db_conn, schema_list)
        query = '''
            select
                gwl_table_schema,
                gwl_table_name,
                gwl_column_name,
                check_data_type,
                check_row_count,
                check_null_count,
                check_unique_count,
                check_sum_value,
                check_min_value,
                check_max_value,
                check_samples_uniqueness,
                n_samples_uniqueness,
                check_transformation,
                is_migrated
            from validation."ValidationBasicConditions_xc_logic"
        '''
        all_condition_df = pd.read_sql(query, gwl_db_conn)

        validation_df = pd.merge(
            validation_df, 
            all_condition_df, 
            on=['gwl_table_schema', 'gwl_table_name', 'gwl_column_name'],
            how='left'
        )
        validation_df = validation_df.replace({np.nan: None, pd.NaT: None})
        # Validate with condition
        validation_df['checking_results'] = validation_df.apply(validate_all_condition, axis=1, args=(gwl_db_conn,))
        # Splitting the tuple column into separate columns
        validation_df[['is_match', 'error_message']] = validation_df['checking_results'].apply(pd.Series)
        # Dropping the original tuple column (optional)
        validation_df.drop(columns=['checking_results'], inplace=True)
        # Insert validation resutls
        insert_dataframe_into_table(validation_df.drop(columns=['is_migrated']), 'validation."ValidationResults_xc_logic"', gwl_db_conn)
        # Get summary results for only migrated table
        summary_validation_df = get_summary_results(validation_df[validation_df['is_migrated'] == True])
        # Insert summary validation results
        insert_dataframe_into_table(summary_validation_df, 'validation."ValidationSummaryResults_xc_logic"', gwl_db_conn)
    finally:
        if temp_db_conn:
            temp_db_conn.close()
        if gwl_db_conn:
            gwl_db_conn.close()


def validate_point_service():
    '''
    Function to validate point_service schema
    '''
    temp_db_conn = psycopg2.connect(
        dbname=temp_dbname,
        user=temp_user,
        password=temp_password,
        host=temp_host,
        port=temp_port
    )
    gwl_db_conn = psycopg2.connect(
        dbname=gwl_dbname,
        user=gwl_user,
        password=gwl_password,
        host=gwl_host,
        port=gwl_port
    )
    try:
        schema_list = ['point_service']
        validation_df = build_validation_dataframe(temp_db_conn, gwl_db_conn, schema_list)
        query = '''
            select
                gwl_table_schema,
                gwl_table_name,
                gwl_column_name,
                check_data_type,
                check_row_count,
                check_null_count,
                check_unique_count,
                check_sum_value,
                check_min_value,
                check_max_value,
                check_samples_uniqueness,
                n_samples_uniqueness,
                check_transformation,
                is_migrated
            from validation."ValidationBasicConditions_xc_logic"
        '''
        all_condition_df = pd.read_sql(query, gwl_db_conn)

        validation_df = pd.merge(
            validation_df, 
            all_condition_df, 
            on=['gwl_table_schema', 'gwl_table_name', 'gwl_column_name'],
            how='left'
        )
        validation_df = validation_df.replace({np.nan: None, pd.NaT: None})
        # Validate with condition
        validation_df['checking_results'] = validation_df.apply(validate_all_condition, axis=1, args=(gwl_db_conn,))
        # Splitting the tuple column into separate columns
        validation_df[['is_match', 'error_message']] = validation_df['checking_results'].apply(pd.Series)
        # Dropping the original tuple column (optional)
        validation_df.drop(columns=['checking_results'], inplace=True)
        # Insert validation resutls
        insert_dataframe_into_table(validation_df.drop(columns=['is_migrated']), 'validation."ValidationResults_xc_logic"', gwl_db_conn)
        # Get summary results for only migrated table
        summary_validation_df = get_summary_results(validation_df[validation_df['is_migrated'] == True])
        # Insert summary validation results
        insert_dataframe_into_table(summary_validation_df, 'validation."ValidationSummaryResults_xc_logic', gwl_db_conn)
    finally:
        if temp_db_conn:
            temp_db_conn.close()
        if gwl_db_conn:
            gwl_db_conn.close()
