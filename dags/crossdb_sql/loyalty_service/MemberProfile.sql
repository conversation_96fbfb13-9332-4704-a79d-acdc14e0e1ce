INSERT into staging_loyalty_service."MemberProfile" (
    id,
    "memberId",
    "firstName",
    "firstNameTh",
    "middleName",
    "middleNameTh",
    "lastName",
    "lastNameTh",
    cid,
    "passportNo",
    "passportExpiryDate",
    "dateOfBirth",
    gender,
    "addressLine",
    "subDistrict",
    district,
    province,
    city,
    "postalCode",
    "createdAt",
    "updatedAt",
    occupation,
    title,
    "countryCode",
    "nationalityCode",
    "addressLineHash",
    "cidHash",
    "firstNameHash",
    "firstNameThHash",
    "genderHash",
    "lastNameHash",
    "lastNameThHash",
    "middleNameHash",
    "middleNameThHash",
    "passportNoHash",
    "dateOfBirthHash"
)
SELECT * FROM dblink(
    'my_connection', 
    'SELECT
        id,
        "memberId",
        "firstName",
        "firstNameTh",
        "middleName",
        "middleNameTh",
        "lastName",
        "lastNameTh",
        cid,
        "passportNo",
        "passportExpiryDate",
        "dateOfBirth",
        gender,
        "addressLine",
        "subDistrict",
        district,
        province,
        city,
        "postalCode",
        "createdAt",
        "updatedAt",
        occupation,
        title,
        "countryCode",
        "nationalityCode",
        "addressLineHash",
        "cidHash",
        "firstNameHash",
        "firstNameThHash",
        "genderHash",
        "lastNameHash",
        "lastNameThHash",
        "middleNameHash",
        "middleNameThHash",
        "passportNoHash",
        "dateOfBirthHash"
    FROM loyalty_service."MemberProfile"'
)  AS t1(
    id text,
    "memberId" text,
    "firstName" text,
    "firstNameTh" text,
    "middleName" text,
    "middleNameTh" text,
    "lastName" text,
    "lastNameTh" text,
    cid text,
    "passportNo" text,
    "passportExpiryDate" date,
    "dateOfBirth" text,
    gender text,
    "addressLine" text,
    "subDistrict" text,
    district text,
    province text,
    city text,
    "postalCode" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    occupation int4,
    title int4,
    "countryCode" text,
    "nationalityCode" text,
    "addressLineHash" text,
	"cidHash" text,
	"firstNameHash" text,
	"firstNameThHash" text,
	"genderHash" text,
	"lastNameHash" text,
	"lastNameThHash" text,
	"middleNameHash" text,
	"middleNameThHash" text,
	"passportNoHash" text,
	"dateOfBirthHash" text
)
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "firstName" = EXCLUDED."firstName",
    "firstNameTh" = EXCLUDED."firstNameTh",
    "middleName" = EXCLUDED."middleName",
    "middleNameTh" = EXCLUDED."middleNameTh",
    "lastName" = EXCLUDED."lastName",
    "lastNameTh" = EXCLUDED."lastNameTh",
    cid = EXCLUDED.cid,
    "passportNo" = EXCLUDED."passportNo",
    "passportExpiryDate" = EXCLUDED."passportExpiryDate",
    "dateOfBirth" = EXCLUDED."dateOfBirth",
    gender = EXCLUDED.gender,
    "addressLine" = EXCLUDED."addressLine",
    "subDistrict" = EXCLUDED."subDistrict",
    district = EXCLUDED.district,
    province = EXCLUDED.province,
    city = EXCLUDED.city,
    "postalCode" = EXCLUDED."postalCode",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    occupation = EXCLUDED.occupation,
    title = EXCLUDED.title,
    "countryCode" = EXCLUDED."countryCode",
    "nationalityCode" = EXCLUDED."nationalityCode",
    "addressLineHash" = EXCLUDED."addressLineHash",
    "cidHash" = EXCLUDED."cidHash",
    "firstNameHash" = EXCLUDED."firstNameHash",
    "firstNameThHash" = EXCLUDED."firstNameThHash",
    "genderHash" = EXCLUDED."genderHash",
    "lastNameHash" = EXCLUDED."lastNameHash",
    "lastNameThHash" = EXCLUDED."lastNameThHash",
    "middleNameHash" = EXCLUDED."middleNameHash",
    "middleNameThHash" = EXCLUDED."middleNameThHash",
    "passportNoHash" = EXCLUDED."passportNoHash",
    "dateOfBirthHash" = EXCLUDED."dateOfBirthHash";