INSERT into staging_loyalty_service."StaffProfile" (
    id,
    "memberId",
    "staffLevelCode",
    "companyCode",
    "staffNo",
    "createdAt",
    "updatedAt",
    "staffPosition"
)
SELECT * FROM dblink(
    'my_connection', 
    'SELECT
        id,
        "memberId",
        "staffLevelCode",
        "companyCode",
        "staffNo",
        "createdAt",
        "updatedAt",
        "staffPosition"
    FROM loyalty_service."StaffProfile"'
)  AS t1 (
    id text,
    "memberId" text,
    "staffLevelCode" text,
    "companyCode" text,
    "staffNo" text,
    "createdAt" timestamp(3),
    "updatedAt" timestamp(3),
    "staffPosition" text
)
ON CONFLICT (id) DO UPDATE SET
    "memberId" = EXCLUDED."memberId",
    "staffLevelCode" = EXCLUDED."staffLevelCode",
    "companyCode" = EXCLUDED."companyCode",
    "staffNo" = EXCLUDED."staffNo",
    "createdAt" = EXCLUDED."createdAt",
    "updatedAt" = EXCLUDED."updatedAt",
    "staffPosition" = EXCLUDED."staffPosition";
