from airflow import DAG
from airflow.operators.python import PythonOperator
from migration_utils.migration_ops import  fetch_transform_insert_cross_gwl_uat


def temp_sql_run_to_table():
    """
    This function is a placeholder for the actual SQL run logic.
    It should be replaced with the actual implementation.
    """
    # Example SQL run logic
    # Replace this with your actual SQL execution code
    fetch_transform_insert_cross_gwl_uat('point_service."WalletBalance"', 'uat_point_db_conn')






    print("Running SQL to do some operation to table")



with DAG(
    dag_id="deposit_table_run_UAT_dag",
    start_date=None,
    schedule_interval=None,
    catchup=False,
    tags=["gwl_uat_cross","sql_runUAT"],
) as dag:
    

    temp_sql_runUAT = PythonOperator(
        task_id="run_specific_sql_to_table_cross_gwl_uat",
        python_callable=temp_sql_run_to_table,
    )

    temp_sql_runUAT